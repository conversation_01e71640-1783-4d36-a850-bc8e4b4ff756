#!/usr/bin/env node

/**
 * Simple Database Connection Test
 * This script tests the database connection with debug information
 */

require('dotenv').config();
const { Pool } = require('pg');

console.log('🔧 Database Connection Debug Test');
console.log('='.repeat(50));

console.log('\n📋 Environment Variables:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD type:', typeof process.env.DB_PASSWORD);
console.log('DB_PASSWORD length:', process.env.DB_PASSWORD ? process.env.DB_PASSWORD.length : 'undefined');
console.log('DB_PASSWORD value:', process.env.DB_PASSWORD ? '***' + process.env.DB_PASSWORD.slice(-3) : 'undefined');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || '',
  max: 5,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000,
};

console.log('\n📊 Database Configuration:');
console.log('Host:', dbConfig.host);
console.log('Port:', dbConfig.port);
console.log('Database:', dbConfig.database);
console.log('User:', dbConfig.user);
console.log('Password type:', typeof dbConfig.password);
console.log('Password defined:', dbConfig.password ? 'YES' : 'NO');

async function testConnection() {
  console.log('\n🔍 Testing database connection...');
  
  const pool = new Pool(dbConfig);
  
  try {
    // Test basic connection
    const client = await pool.connect();
    console.log('✅ Database connection successful!');
    
    // Test simple query
    const result = await client.query('SELECT NOW() as current_time, version() as postgres_version');
    console.log('✅ Query test successful!');
    console.log('Current time:', result.rows[0].current_time);
    console.log('PostgreSQL version:', result.rows[0].postgres_version.split(' ')[0]);
    
    // Test tables exist
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_name IN ('assignments', 'drivers', 'dump_trucks', 'locations')
      ORDER BY table_name
    `);
    
    console.log('\n📋 Required tables found:');
    tablesResult.rows.forEach(row => {
      console.log('✅', row.table_name);
    });
    
    if (tablesResult.rows.length < 4) {
      console.log('⚠️  Some required tables are missing. You may need to run database migrations.');
    }
    
    client.release();
    await pool.end();
    
    return true;
    
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    console.log('Error code:', error.code);
    console.log('Error details:', error.detail || 'No additional details');
    
    if (error.message.includes('password')) {
      console.log('\n💡 Password-related troubleshooting:');
      console.log('1. Check if PostgreSQL is running: net start postgresql-x64-15 (or similar)');
      console.log('2. Verify password in .env file matches PostgreSQL user password');
      console.log('3. Try connecting manually: psql -U postgres -d hauling_qr_system');
      console.log('4. Check if password contains special characters that need escaping');
    }
    
    if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.log('\n💡 Database troubleshooting:');
      console.log('1. Create database: createdb -U postgres hauling_qr_system');
      console.log('2. Or connect to PostgreSQL and run: CREATE DATABASE hauling_qr_system;');
    }
    
    try {
      await pool.end();
    } catch (poolError) {
      // Ignore pool end error
    }
    
    return false;
  }
}

async function main() {
  const success = await testConnection();
  
  console.log('\n' + '='.repeat(50));
  
  if (success) {
    console.log('🎉 DATABASE CONNECTION TEST: SUCCESS!');
    console.log('\n✅ Database is ready for route deviation testing');
    console.log('✅ You can now run: node database-verification.js');
  } else {
    console.log('❌ DATABASE CONNECTION TEST: FAILED');
    console.log('\n🔧 Please fix the issues above and try again');
  }
  
  console.log('='.repeat(50));
}

if (require.main === module) {
  main().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}
