const { Pool } = require('pg');
require('dotenv').config({ path: '../.env' });

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD
});

async function addMissingLocations() {
  try {
    console.log('Adding missing locations to database...\n');
    
    // Check existing locations
    const existing = await pool.query('SELECT location_code FROM locations');
    const existingCodes = existing.rows.map(r => r.location_code);
    console.log('Existing locations:', existingCodes.join(', ') || 'None');
    
    // Define required locations
    const requiredLocations = [
      {
        location_code: 'LOC001',
        name: 'Point A - Main Loading Site',
        type: 'loading',
        address: '123 Main Street, Industrial Zone',
        coordinates: { lat: 14.5995, lng: 120.9842 },
        qr_code_data: JSON.stringify({ type: 'location', id: 'LOC001', name: 'Point A - Main Loading Site' }),
        is_active: true
      },
      {
        location_code: 'LOC002',
        name: 'Point B - Primary Unloading Site',
        type: 'unloading',
        address: '456 Construction Avenue, Building Site',
        coordinates: { lat: 14.6042, lng: 120.9822 },
        qr_code_data: JSON.stringify({ type: 'location', id: 'LOC002', name: 'Point B - Primary Unloading Site' }),
        is_active: true
      },
      {
        location_code: 'LOC003',
        name: 'Point C - Secondary Unloading Site',
        type: 'unloading',
        address: '789 Development Road, Project Area',
        coordinates: { lat: 14.6100, lng: 120.9900 },
        qr_code_data: JSON.stringify({ type: 'location', id: 'LOC003', name: 'Point C - Secondary Unloading Site' }),
        is_active: true
      }
    ];
    
    // Insert missing locations
    for (const location of requiredLocations) {
      if (!existingCodes.includes(location.location_code)) {
        const result = await pool.query(
          `INSERT INTO locations (location_code, name, type, address, coordinates, qr_code_data, is_active)
           VALUES ($1, $2, $3, $4, $5, $6, $7)
           RETURNING id, location_code, name`,
          [
            location.location_code,
            location.name,
            location.type,
            location.address,
            JSON.stringify(location.coordinates),
            location.qr_code_data,
            location.is_active
          ]
        );
        console.log(`✅ Added: ${result.rows[0].name} (${result.rows[0].location_code})`);
      } else {
        console.log(`⏭️  Skipped: ${location.name} (${location.location_code}) - already exists`);
      }
    }
    
    // Verify all locations
    console.log('\nVerifying all locations:');
    const allLocations = await pool.query('SELECT * FROM locations ORDER BY id');
    allLocations.rows.forEach(loc => {
      console.log(`  ID: ${loc.id} | ${loc.location_code} | ${loc.name} | Type: ${loc.type}`);
    });
    
    console.log('\n✅ Database locations setup complete!');
    
  } catch (error) {
    console.error('Error adding locations:', error.message);
    console.error('Details:', error);
  } finally {
    await pool.end();
  }
}

// Run the script
addMissingLocations();