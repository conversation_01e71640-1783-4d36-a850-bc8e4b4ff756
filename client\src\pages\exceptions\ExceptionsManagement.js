import React, { useState, useEffect, useCallback } from 'react';
import { approvalsAPI } from '../../services/api';
import ExceptionFormModal from './components/ExceptionFormModal';
import ExceptionsTable from './components/ExceptionsTable';
import ExceptionDetailModal from './components/ExceptionDetailModal';
import toast from 'react-hot-toast';

const ExceptionsManagement = () => {
  const [exceptions, setExceptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Modal states
  const [showExceptionModal, setShowExceptionModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedException, setSelectedException] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({
    status: '',
    exception_type: '',
    severity: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  // Stats state
  const [stats, setStats] = useState(null);// Load exceptions with error handling
  const loadExceptions = useCallback(async (page = 1, retryCount = 0) => {
    setLoading(true);
    
    const params = {
      page,
      limit: pagination.itemsPerPage,
      ...filters
    };
    
    try {      console.log('Loading exceptions with params:', params);
      const response = await approvalsAPI.getAll({ params });
      
      if (!response.data || !response.data.success) {
        throw new Error(`API returned error: ${response.data?.message || 'Unknown error'}`);
      }

      setExceptions(response.data.data || []);
      setPagination(response.data.pagination || {});
    } catch (error) {      console.error('Error loading exceptions:', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,        requestParams: params,
        retryCount
      });
        // Handle circuit breaker errors
      if (error.message.includes('Service temporarily unavailable')) {
        toast.error('Service temporarily unavailable. Please try again in a moment.');
        setExceptions([]);
        setPagination({});
        return;
      }
      
      // Retry logic for 500 errors
      if (error.response?.status === 500 && retryCount < 2) {
        console.log(`Retrying exceptions load (attempt ${retryCount + 1}/2)...`);
        setTimeout(() => loadExceptions(page, retryCount + 1), 1000 * (retryCount + 1));
        return;
      }
      
      const errorMessage = error.response?.data?.message || 
                          `Failed to load exceptions (${error.response?.status || 'Network Error'})`;
      toast.error(errorMessage);
      
      // Set empty data on error to prevent UI crashes
      setExceptions([]);
      setPagination({});
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.itemsPerPage]);

  // Load statistics
  const loadStats = useCallback(async (retryCount = 0) => {
    try {      console.log('Loading exception statistics...');
      const response = await approvalsAPI.getStats({ days: 30 });
      
      if (!response.data || !response.data.success) {
        throw new Error(`Stats API returned error: ${response.data?.message || 'Unknown error'}`);
      }
      
      setStats(response.data.data || {});
    } catch (error) {
      console.error('Error loading stats:', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,        retryCount
      });
        // Handle service unavailable errors
      if (error.message.includes('Service temporarily unavailable')) {
        console.warn('Stats loading failed due to service unavailability, using empty data');
        setStats({});
        return;
      }
      
      // Retry logic for 500 errors
      if (error.response?.status === 500 && retryCount < 2) {
        console.log(`Retrying stats load (attempt ${retryCount + 1}/2)...`);
        setTimeout(() => loadStats(retryCount + 1), 1000 * (retryCount + 1));
        return;
      }
      
      // Don't show toast for stats errors to avoid spam
      console.warn('Stats loading failed, using empty data');
      setStats({});
    }
  }, []);

  // Initial load and stats
  useEffect(() => {
    loadExceptions();
    loadStats();
  }, [loadExceptions, loadStats]);

  // Handle filter changes
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setFilters({
      status: '',
      exception_type: '',
      severity: '',
      sortBy: 'created_at',
      sortOrder: 'desc'
    });
  };

  // Handle exception creation
  const handleCreateException = () => {
    setSelectedException(null);
    setShowExceptionModal(true);
  };

  // Handle view exception details
  const handleViewException = (exception) => {
    setSelectedException(exception);
    setShowDetailModal(true);
  };

  // Handle approve/reject decision
  const handleDecision = async (exceptionId, decision, notes = '') => {
    try {
      await approvalsAPI.updateDecision(exceptionId, { decision, notes });
      toast.success(`Exception ${decision} successfully`);
      loadExceptions(pagination.currentPage);
      loadStats();
    } catch (error) {
      console.error('Error updating exception:', error);
      const errorMessage = error.response?.data?.message || `Failed to ${decision} exception`;
      toast.error(errorMessage);
    }
  };

  // Handle form submission
  const handleExceptionSubmit = async (formData) => {
    try {
      await approvalsAPI.create(formData);
      toast.success('Exception reported successfully');
      setShowExceptionModal(false);
      loadExceptions();
      loadStats();
    } catch (error) {
      console.error('Error creating exception:', error);
      const errorMessage = error.response?.data?.message || 'Failed to report exception';
      toast.error(errorMessage);
    }
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    loadExceptions(newPage);
  };

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== ''
  ).length;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Exception Management</h1>
          <p className="text-secondary-600 mt-1">Manage trip exceptions and approval requests</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={handleCreateException}
            className="btn btn-primary"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Report Exception
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
          <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-md bg-warning-500 flex items-center justify-center">
                    <span className="text-white text-lg">⚠️</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      Total Exceptions
                    </dt>                    <dd className="text-2xl font-semibold text-secondary-900">
                      {stats?.summary?.total_exceptions || 0}
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-3">                <p className="text-sm text-secondary-600">
                  {stats?.summary?.exception_rate || 0}% of all trips
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-md bg-yellow-500 flex items-center justify-center">
                    <span className="text-white text-lg">⏳</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      Pending
                    </dt>
                    <dd className="text-2xl font-semibold text-secondary-900">
                      {stats?.summary?.pending || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-md bg-success-500 flex items-center justify-center">
                    <span className="text-white text-lg">✅</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      Approved
                    </dt>
                    <dd className="text-2xl font-semibold text-secondary-900">
                      {stats?.summary?.approved || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-md bg-danger-500 flex items-center justify-center">
                    <span className="text-white text-lg">❌</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      Rejected
                    </dt>
                    <dd className="text-2xl font-semibold text-secondary-900">
                      {stats?.summary?.rejected || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-md bg-red-500 flex items-center justify-center">
                    <span className="text-white text-lg">🚨</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      Critical
                    </dt>
                    <dd className="text-2xl font-semibold text-secondary-900">
                      {stats?.summary?.critical || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-secondary-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
          <h3 className="text-lg font-medium text-secondary-900">Filter Exceptions</h3>
          {activeFiltersCount > 0 && (
            <button
              onClick={handleClearFilters}
              className="mt-2 sm:mt-0 text-sm text-primary-600 hover:text-primary-500"
            >
              Clear all filters ({activeFiltersCount})
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="input"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Exception Type
            </label>
            <select
              value={filters.exception_type}
              onChange={(e) => handleFilterChange('exception_type', e.target.value)}
              className="input"
            >
              <option value="">All Types</option>
              <option value="route_deviation">Route Deviation</option>
              <option value="time_violation">Time Violation</option>
              <option value="equipment_issue">Equipment Issue</option>
              <option value="weather_delay">Weather Delay</option>
              <option value="manual_override">Manual Override</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Severity
            </label>
            <select
              value={filters.severity}
              onChange={(e) => handleFilterChange('severity', e.target.value)}
              className="input"
            >
              <option value="">All Severities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Sort By
            </label>
            <select
              value={`${filters.sortBy}-${filters.sortOrder}`}
              onChange={(e) => {
                const [sortBy, sortOrder] = e.target.value.split('-');
                handleFilterChange('sortBy', sortBy);
                handleFilterChange('sortOrder', sortOrder);
              }}
              className="input"
            >
              <option value="created_at-desc">Newest First</option>
              <option value="created_at-asc">Oldest First</option>
              <option value="severity-desc">Severity High to Low</option>
              <option value="severity-asc">Severity Low to High</option>
              <option value="exception_type-asc">Type A to Z</option>
              <option value="exception_type-desc">Type Z to A</option>
            </select>
          </div>
        </div>
      </div>

      {/* Exceptions Table */}
      <ExceptionsTable
        exceptions={exceptions}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onViewException={handleViewException}
        onApprove={(id, notes) => handleDecision(id, 'approved', notes)}
        onReject={(id, notes) => handleDecision(id, 'rejected', notes)}
      />

      {/* Exception Form Modal */}
      {showExceptionModal && (
        <ExceptionFormModal
          isOpen={showExceptionModal}
          onClose={() => setShowExceptionModal(false)}
          onSubmit={handleExceptionSubmit}
          exception={selectedException}
        />
      )}

      {/* Exception Detail Modal */}
      {showDetailModal && selectedException && (
        <ExceptionDetailModal
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
          exception={selectedException}
          onApprove={(notes) => handleDecision(selectedException.id, 'approved', notes)}
          onReject={(notes) => handleDecision(selectedException.id, 'rejected', notes)}
        />
      )}
    </div>
  );
};

export default ExceptionsManagement;
