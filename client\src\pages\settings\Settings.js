import React from 'react';

const Settings = () => {
  return (
    <div className="space-y-6">
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-secondary-900 sm:text-3xl sm:truncate">
            ⚙️ Settings
          </h1>
          <p className="mt-1 text-sm text-secondary-500">System configuration and user preferences.</p>
        </div>
      </div>

      <div className="bg-warning-50 border border-warning-200 rounded-lg p-6">
        <div className="flex items-center">
          <span className="text-2xl mr-3">🔧</span>
          <div>
            <h3 className="text-lg font-medium text-warning-800">Coming in Future Updates</h3>
            <p className="mt-1 text-sm text-warning-700">System settings and configuration options.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;