const { query, getClient } = require('../config/database');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function testApprovalEndpoint() {
  console.log('🧪 Testing approval endpoint logic...');

  const client = await getClient();
  
  try {
    await client.query('BEGIN');

    // Simulate the exact PUT /api/approvals/3 logic
    const id = 3;
    const decision = 'approved';
    const notes = 'Test approval from debug';
    const userId = 1; // Mock user ID

    console.log('Step 1: Getting approval details with lock...');
    const approvalResult = await client.query(
      `SELECT a.*, tl.*, tl.notes as trip_notes
       FROM approvals a
       JOIN trip_logs tl ON a.trip_log_id = tl.id
       WHERE a.id = $1
       FOR UPDATE`,
      [id]
    );

    if (approvalResult.rows.length === 0) {
      console.log('❌ Approval not found');
      await client.query('ROLLBACK');
      return;
    }

    const approval = approvalResult.rows[0];
    console.log('✅ Found approval:', approval.id, 'Status:', approval.status);

    if (approval.status !== 'pending') {
      console.log('❌ Approval is not pending, status:', approval.status);
      await client.query('ROLLBACK');
      return;
    }

    console.log('Step 2: Updating approval decision...');
    const updateApprovalResult = await client.query(`
      UPDATE approvals 
      SET status = $1, reviewed_by = $2, reviewed_at = CURRENT_TIMESTAMP, 
          notes = $3, updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
      RETURNING *
    `, [decision, userId, notes, id]);

    console.log('✅ Approval updated:', updateApprovalResult.rows[0].status);

    console.log('Step 3: Testing handleApprovedException...');
    const { trip_log_id, exception_description } = approval;
    
    if (exception_description && exception_description.includes('Loading at')) {
      console.log('✅ This is a route deviation for loading');
      
      // Parse trip notes
      let tripNotes;
      try {
        tripNotes = approval.trip_notes ? JSON.parse(approval.trip_notes) : {};
        console.log('✅ Trip notes parsed:', tripNotes);
      } catch (e) {
        console.log('❌ Failed to parse trip notes:', e.message);
        tripNotes = {};
      }

      if (tripNotes.pending_assignment_id) {
        console.log('✅ Has pending assignment ID:', tripNotes.pending_assignment_id);
        
        // Check if assignment exists
        const assignmentCheck = await client.query(
          'SELECT id, status FROM assignments WHERE id = $1',
          [tripNotes.pending_assignment_id]
        );

        if (assignmentCheck.rows.length === 0) {
          console.log('❌ Assignment not found, updating trip status only');
          await client.query(`
            UPDATE trip_logs 
            SET status = 'loading_start',
                exception_approved_by = $1,
                exception_approved_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $2
          `, [userId, trip_log_id]);
        } else {
          console.log('✅ Assignment exists, updating trip and assignment');
          
          // Update trip log to use new assignment
          await client.query(`
            UPDATE trip_logs 
            SET assignment_id = $1, 
                status = 'loading_start',
                exception_approved_by = $2,
                exception_approved_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $3
          `, [tripNotes.pending_assignment_id, userId, trip_log_id]);

          // Update assignment status
          await client.query(`
            UPDATE assignments
            SET status = 'in_progress',
                start_time = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
          `, [tripNotes.pending_assignment_id]);
          
          console.log('✅ Assignment updated to in_progress');
        }
      } else {
        console.log('No pending assignment, just updating trip status');
        await client.query(`
          UPDATE trip_logs 
          SET status = 'loading_start',
              exception_approved_by = $1,
              exception_approved_at = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
        `, [userId, trip_log_id]);
      }
    }

    console.log('Step 4: Getting final approval data...');
    const completeDataResult = await client.query(`
      SELECT 
        a.*,
        tl.trip_number,
        dt.truck_number,
        d.full_name as driver_name,
        u.email as reviewed_by_email
      FROM approvals a
      JOIN trip_logs tl ON a.trip_log_id = tl.id
      JOIN assignments ass ON tl.assignment_id = ass.id
      JOIN dump_trucks dt ON ass.truck_id = dt.id
      LEFT JOIN drivers d ON ass.driver_id = d.id
      LEFT JOIN users u ON a.reviewed_by = u.id
      WHERE a.id = $1
    `, [id]);

    console.log('✅ Final approval data:', {
      id: completeDataResult.rows[0].id,
      status: completeDataResult.rows[0].status,
      reviewed_by: completeDataResult.rows[0].reviewed_by,
      truck_number: completeDataResult.rows[0].truck_number
    });

    await client.query('COMMIT');
    console.log('✅ Test completed successfully - approval should work now!');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Test failed:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      detail: error.detail
    });
  } finally {
    client.release();
  }
}

testApprovalEndpoint().then(() => {
  console.log('Done');
  process.exit(0);
}).catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
