/**
 * Test script to verify that assigned_date is now optional in assignments
 * and that scanning works correctly without considering assigned_date
 */
const { Pool } = require('pg');
const { db } = require('../config/database');
const { processTruckScan } = require('../routes/scanner');

// Use the existing database connection to ensure correct config
const pool = db;

async function runTest() {
  const client = await pool.connect();
  
  try {
    console.log('==========================================');
    console.log('TEST: ASSIGNED_DATE OPTIONAL IN ASSIGNMENTS');
    console.log('==========================================');
    
    console.log('\n1. Testing assignment creation with NULL assigned_date...');
    
    // Create a test truck if it doesn't exist
    let result = await client.query('SELECT id FROM dump_trucks WHERE truck_number = $1', ['TEST-TRUCK-99']);
    let truckId;
    
    if (result.rows.length === 0) {
      const truckResult = await client.query(`
        INSERT INTO dump_trucks (truck_number, license_plate, status, qr_code_data, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING id
      `, ['TEST-TRUCK-99', 'TEST-99', 'active', JSON.stringify({ type: 'truck', id: 'TEST-TRUCK-99' })]);
      truckId = truckResult.rows[0].id;
      console.log('- Created test truck with ID:', truckId);
    } else {
      truckId = result.rows[0].id;
      console.log('- Using existing test truck with ID:', truckId);
    }
    
    // Verify test locations exist
    const loadingLocationResult = await client.query('SELECT id FROM locations WHERE location_code = $1', ['LOADING-A']);
    const unloadingLocationResult = await client.query('SELECT id FROM locations WHERE location_code = $1', ['UNLOADING-B']);
    
    if (loadingLocationResult.rows.length === 0 || unloadingLocationResult.rows.length === 0) {
      throw new Error('Test locations not found. Please create LOADING-A and UNLOADING-B locations first.');
    }
    
    const loadingLocationId = loadingLocationResult.rows[0].id;
    const unloadingLocationId = unloadingLocationResult.rows[0].id;
    
    // Create an assignment with NULL assigned_date
    console.log('\n2. Creating test assignment with NULL assigned_date...');
    
    await client.query('BEGIN');
    
    // Delete any existing test assignments for this truck
    await client.query('DELETE FROM assignments WHERE truck_id = $1', [truckId]);
    
    // Create a new assignment with NULL assigned_date
    const assignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, loading_location_id, unloading_location_id,
        assigned_date, status, priority, expected_loads_per_day, 
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, NULL, $5, $6, $7, NOW(), NOW())
      RETURNING id
    `, [
      `TEST-ASG-${Date.now()}`,
      truckId,
      loadingLocationId,
      unloadingLocationId,
      'assigned',
      'normal',
      2
    ]);
    
    const assignmentId = assignmentResult.rows[0].id;
    console.log('- Created test assignment with ID:', assignmentId);
    
    // Test the truck scan process
    console.log('\n3. Testing truck scanning with NULL assigned_date assignment...');
    
    const qrData = { type: 'truck', id: 'TEST-TRUCK-99' };
    const locationScanData = { type: 'location', id: 'LOADING-A' };
    const userId = 1; // Default system user
    
    try {
      const scanResult = await processTruckScan(
        client,
        qrData,
        locationScanData,
        userId,
        '127.0.0.1',
        'Test Script'
      );
      
      console.log('✓ Scan processed successfully!');
      console.log('- Result message:', scanResult.message);
      console.log('- Trip ID:', scanResult.trip_log_id);
      console.log('- Next step:', scanResult.next_step);
      
      // Clean up
      console.log('\n4. Cleaning up test data...');
      await client.query('DELETE FROM trip_logs WHERE assignment_id = $1', [assignmentId]);
      await client.query('DELETE FROM assignments WHERE id = $1', [assignmentId]);
      
      console.log('✓ Test completed successfully! The system correctly handles NULL assigned_date');
    } catch (error) {
      console.error('✗ ERROR during truck scan test:', error.message);
      throw error;
    } finally {
      await client.query('COMMIT');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
    await client.query('ROLLBACK');
  } finally {
    client.release();
    pool.end();
  }
}

runTest().catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
});
