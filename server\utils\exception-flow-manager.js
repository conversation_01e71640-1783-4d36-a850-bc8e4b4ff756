/**
 * Optimized Exception Flow Manager
 *
 * This module manages the complete exception flow lifecycle with improved performance:
 * 1. Simplified exception detection and trip status management
 * 2. Streamlined approval workflow with reduced complexity
 * 3. Optimized database queries and transaction handling
 * 4. Enhanced error handling and recovery mechanisms
 * 5. Performance target: <300ms for all operations
 */

const { getClient } = require('../config/database');

/**
 * Exception Flow States
 */
const EXCEPTION_STATES = {
  PENDING: 'exception_pending',
  APPROVED: 'approved', 
  REJECTED: 'rejected'
};

const TRIP_STATES = {
  ASSIGNED: 'assigned',
  LOADING_START: 'loading_start',
  LOADING_END: 'loading_end',
  UNLOADING_START: 'unloading_start',
  UNLOADING_END: 'unloading_end',
  TRIP_COMPLETED: 'trip_completed',
  EXCEPTION_PENDING: 'exception_pending',
  CANCELLED: 'cancelled'
};

/**
 * Enhanced logging for exception flow
 */
function logExceptionFlow(context, message, data = {}) {
  console.log(`[${new Date().toISOString()}] [EXCEPTION_FLOW] [${context}] ${message}`, 
    JSON.stringify(data, null, 2));
}

function logExceptionError(context, error, additionalData = {}) {
  console.error(`[${new Date().toISOString()}] [EXCEPTION_FLOW] [${context}] ERROR:`, {
    message: error.message,
    stack: error.stack,
    ...additionalData
  });
}

/**
 * Check if trip can progress based on current state and approval status
 */
async function validateTripProgression(client, tripLogId) {
  try {
    // Get trip details with approval status
    const tripResult = await client.query(`
      SELECT 
        tl.id,
        tl.status,
        tl.is_exception,
        tl.exception_approved_at,
        tl.exception_approved_by,
        tl.assignment_id,
        tl.notes,
        a.id as approval_id,
        a.status as approval_status,
        a.exception_type,
        a.reviewed_at,
        a.reviewed_by
      FROM trip_logs tl
      LEFT JOIN approvals a ON tl.id = a.trip_log_id AND a.status = 'pending'
      WHERE tl.id = $1
    `, [tripLogId]);

    if (tripResult.rows.length === 0) {
      throw new Error('Trip not found');
    }

    const trip = tripResult.rows[0];
    
    logExceptionFlow('VALIDATE_PROGRESSION', 'Checking trip progression', {
      trip_id: tripLogId,
      status: trip.status,
      is_exception: trip.is_exception,
      has_pending_approval: !!trip.approval_id
    });

    // If trip status is exception_pending, check approval status
    if (trip.status === EXCEPTION_STATES.PENDING) {
      // Trip cannot progress until approved
      if (!trip.approval_id) {
        return {
          canProgress: false,
          reason: 'No approval request found for exception',
          nextAction: 'create_approval_request'
        };
      }

      // Check latest approval status
      const latestApprovalResult = await client.query(`
        SELECT status, reviewed_at, reviewed_by, notes
        FROM approvals 
        WHERE trip_log_id = $1 
        ORDER BY created_at DESC 
        LIMIT 1
      `, [tripLogId]);

      if (latestApprovalResult.rows.length === 0) {
        return {
          canProgress: false,
          reason: 'Exception pending admin approval',
          nextAction: 'await_approval'
        };
      }

      const latestApproval = latestApprovalResult.rows[0];
      
      if (latestApproval.status === 'pending') {
        return {
          canProgress: false,
          reason: 'Exception pending admin approval',
          nextAction: 'await_approval',
          approval_data: latestApproval
        };
      } else if (latestApproval.status === 'rejected') {
        return {
          canProgress: false,
          reason: 'Exception was rejected - trip cancelled',
          nextAction: 'trip_cancelled',
          approval_data: latestApproval
        };
      } else if (latestApproval.status === 'approved') {
        // Exception was approved - trip can progress
        return {
          canProgress: true,
          reason: 'Exception approved - can proceed',
          nextAction: 'continue_trip',
          approval_data: latestApproval,
          requiresStatusUpdate: true
        };
      }
    }

    // For non-exception trips or already processed exceptions
    return {
      canProgress: true,
      reason: 'Normal trip flow',
      nextAction: 'continue_trip'
    };

  } catch (error) {
    logExceptionError('VALIDATE_PROGRESSION', error, { trip_id: tripLogId });
    throw error;
  }
}

/**
 * Create exception and set trip to pending state
 */
async function createExceptionAndSetPending(client, tripLogId, exceptionData) {
  try {
    await client.query('BEGIN');

    logExceptionFlow('CREATE_EXCEPTION', 'Creating exception request', {
      trip_id: tripLogId,
      exception_type: exceptionData.exception_type
    });

    // Check if exception already exists for this trip
    const existingException = await client.query(
      'SELECT id FROM approvals WHERE trip_log_id = $1 AND status = $2',
      [tripLogId, 'pending']
    );

    if (existingException.rows.length > 0) {
      await client.query('ROLLBACK');
      throw new Error('Pending exception already exists for this trip');
    }

    // Create approval request
    const insertQuery = `
      INSERT INTO approvals (
        trip_log_id, exception_type, exception_description, 
        severity, reported_by, status, requested_at,
        created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, 'pending', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `;

    const approvalResult = await client.query(insertQuery, [
      tripLogId,
      exceptionData.exception_type,
      exceptionData.exception_description,
      exceptionData.severity || 'medium',
      exceptionData.reported_by
    ]);

    // Update trip log to mark as exception and set status to exception_pending
    await client.query(`
      UPDATE trip_logs 
      SET is_exception = true, 
          exception_reason = $1, 
          status = 'exception_pending',
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [
      exceptionData.exception_description, 
      JSON.stringify({
        exception_created_at: new Date().toISOString(),
        original_status: exceptionData.original_status || 'loading_start',
        pending_assignment_id: exceptionData.pending_assignment_id
      }),
      tripLogId
    ]);

    await client.query('COMMIT');

    logExceptionFlow('CREATE_EXCEPTION', 'Exception created successfully', {
      trip_id: tripLogId,
      approval_id: approvalResult.rows[0].id
    });

    return {
      success: true,
      approval_id: approvalResult.rows[0].id,
      trip_status: EXCEPTION_STATES.PENDING
    };

  } catch (error) {
    await client.query('ROLLBACK');
    logExceptionError('CREATE_EXCEPTION', error, { trip_id: tripLogId });
    throw error;
  }
}

/**
 * Process admin approval and update trip status
 */
async function processApprovalAndUpdateTrip(client, approvalId, decision, reviewedBy, notes = '') {
  const startTime = Date.now();

  try {
    await client.query('BEGIN');

    logExceptionFlow('PROCESS_APPROVAL', 'Processing approval decision', {
      approval_id: approvalId,
      decision,
      reviewed_by: reviewedBy
    });

    // Get approval details with trip information
    const approvalResult = await client.query(`
      SELECT 
        a.id, a.status as approval_status, a.exception_type, a.exception_description,
        a.trip_log_id, a.severity, a.reported_by,
        tl.id as trip_id, tl.status as trip_status, tl.notes as trip_notes,
        tl.assignment_id, tl.is_exception, tl.exception_reason
      FROM approvals a
      JOIN trip_logs tl ON a.trip_log_id = tl.id
      WHERE a.id = $1
      FOR UPDATE
    `, [approvalId]);

    if (approvalResult.rows.length === 0) {
      await client.query('ROLLBACK');
      throw new Error('Approval request not found');
    }

    const approval = approvalResult.rows[0];

    if (approval.approval_status !== 'pending') {
      await client.query('ROLLBACK');
      throw new Error(`Approval request has already been processed. Current status: ${approval.approval_status}`);
    }

    // Update approval decision
    await client.query(`
      UPDATE approvals 
      SET status = $1, reviewed_by = $2, reviewed_at = CURRENT_TIMESTAMP, 
          notes = $3, updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
    `, [decision, reviewedBy, notes, approvalId]);

    let tripUpdateResult;

    if (decision === 'approved') {
      // Handle approved exception
      tripUpdateResult = await handleApprovedExceptionFlow(client, approval, reviewedBy);
    } else if (decision === 'rejected') {
      // Handle rejected exception
      tripUpdateResult = await handleRejectedExceptionFlow(client, approval, reviewedBy);
    }

    await client.query('COMMIT');

    const processingTime = Date.now() - startTime;

    logExceptionFlow('PROCESS_APPROVAL', 'Approval processed successfully', {
      approval_id: approvalId,
      decision,
      processing_time_ms: processingTime,
      new_trip_status: tripUpdateResult?.new_status
    });

    return {
      success: true,
      decision,
      processing_time: processingTime,
      trip_update: tripUpdateResult
    };

  } catch (error) {
    await client.query('ROLLBACK');
    const processingTime = Date.now() - startTime;
    logExceptionError('PROCESS_APPROVAL', error, {
      approval_id: approvalId,
      processing_time_ms: processingTime
    });
    throw error;
  }
}

/**
 * Handle approved exception flow - mark trip as completed per Trip Flow Logic
 * According to Trip Flow Logic: when truck returns to unassigned location (A→B→C),
 * it has already completed the full cycle, so trip should be marked as 'trip_completed'
 */
async function handleApprovedExceptionFlow(client, approval, reviewedBy) {
  try {
    const { trip_log_id, exception_description } = approval;

    logExceptionFlow('HANDLE_APPROVED', 'Processing approved exception', {
      trip_id: trip_log_id,
      exception_type: approval.exception_type
    });

    // Parse trip notes to get pending assignment information
    let tripNotes = {};
    try {
      if (approval.trip_notes && typeof approval.trip_notes === 'string') {
        tripNotes = JSON.parse(approval.trip_notes);
      } else if (approval.trip_notes && typeof approval.trip_notes === 'object') {
        tripNotes = approval.trip_notes;
      }
    } catch (e) {
      logExceptionError('PARSE_TRIP_NOTES', e, { trip_notes: approval.trip_notes });
      tripNotes = {};
    }

    // Check if this is a route deviation that requires assignment change
    if (exception_description && exception_description.includes('Loading at') && tripNotes.pending_assignment_id) {
      
      // Verify the pending assignment exists
      const assignmentCheck = await client.query(
        'SELECT id, status, truck_id, loading_location_id, unloading_location_id FROM assignments WHERE id = $1',
        [tripNotes.pending_assignment_id]
      );

      logExceptionFlow('HANDLE_APPROVED', 'Assignment check result', {
        pending_assignment_id: tripNotes.pending_assignment_id,
        assignment_found: assignmentCheck.rows.length > 0,
        assignment_status: assignmentCheck.rows[0]?.status
      });

      if (assignmentCheck.rows.length === 0) {
        logExceptionFlow('HANDLE_APPROVED', 'Pending assignment not found, updating trip status only', {
          pending_assignment_id: tripNotes.pending_assignment_id
        });
        
        // Mark trip as completed per Trip Flow Logic
        // The truck has already performed Loading→Unloading→Return cycle
        await client.query(`
          UPDATE trip_logs
          SET status = 'trip_completed',
              trip_completed_time = CURRENT_TIMESTAMP,
              exception_approved_by = $1,
              exception_approved_at = CURRENT_TIMESTAMP,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $3
          AND status = 'exception_pending'
        `, [
          reviewedBy,
          JSON.stringify({
            approval_processed_at: new Date().toISOString(),
            completion_reason: 'exception_approved_trip_completed',
            fallback_reason: 'pending_assignment_not_found'
          }),
          trip_log_id
        ]);

      } else {
        logExceptionFlow('HANDLE_APPROVED', 'Updating trip with new assignment', {
          trip_id: trip_log_id,
          new_assignment_id: tripNotes.pending_assignment_id
        });

        // Update trip to use new assignment and mark as completed per Trip Flow Logic
        // The truck has completed the A→B→C cycle and should be counted as a completed trip
        await client.query(`
          UPDATE trip_logs
          SET assignment_id = $1,
              status = 'trip_completed',
              trip_completed_time = CURRENT_TIMESTAMP,
              exception_approved_by = $2,
              exception_approved_at = CURRENT_TIMESTAMP,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $3::jsonb,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $4
          AND status = 'exception_pending'
        `, [
          tripNotes.pending_assignment_id,
          reviewedBy,
          JSON.stringify({
            approval_processed_at: new Date().toISOString(),
            completion_reason: 'exception_approved_trip_completed',
            original_assignment_id: tripNotes.original_assignment_id || approval.assignment_id,
            revised_flow: tripNotes.revised_flow || 'Custom route approved',
            is_multiple_deviation: tripNotes.is_multiple_deviation,
            previous_deviation_ids: tripNotes.previous_deviation_ids || []
          }),
          trip_log_id
        ]);

        // First, update assignment status from 'pending_approval' to 'assigned' if needed
        const assignedUpdateResult = await client.query(`
          UPDATE assignments
          SET status = 'assigned',
              assigned_date = CURRENT_DATE,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
          AND status = 'pending_approval'
          RETURNING id, status
        `, [tripNotes.pending_assignment_id]);

        logExceptionFlow('HANDLE_APPROVED', 'Assignment status update to assigned', {
          assignment_id: tripNotes.pending_assignment_id,
          rows_updated: assignedUpdateResult.rowCount,
          new_status: assignedUpdateResult.rows[0]?.status
        });

        // Then, update assignment status to 'in_progress' if trip is actually starting
        const inProgressUpdateResult = await client.query(`
          UPDATE assignments
          SET status = 'in_progress',
              start_time = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
          AND status = 'assigned'
          RETURNING id, status
        `, [tripNotes.pending_assignment_id]);

        logExceptionFlow('HANDLE_APPROVED', 'Assignment status update to in_progress', {
          assignment_id: tripNotes.pending_assignment_id,
          rows_updated: inProgressUpdateResult.rowCount,
          new_status: inProgressUpdateResult.rows[0]?.status
        });
      }

    } else {
      // For other types of exceptions, mark trip as completed per Trip Flow Logic
      await client.query(`
        UPDATE trip_logs
        SET status = 'trip_completed',
            trip_completed_time = CURRENT_TIMESTAMP,
            exception_approved_by = $1,
            exception_approved_at = CURRENT_TIMESTAMP,
            notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
        AND status = 'exception_pending'
      `, [
        reviewedBy,
        JSON.stringify({
          approval_processed_at: new Date().toISOString(),
          completion_reason: 'exception_approved_trip_completed',
          exception_type: approval.exception_type
        }),
        trip_log_id
      ]);
    }

    return {
      new_status: TRIP_STATES.TRIP_COMPLETED,
      assignment_updated: !!tripNotes.pending_assignment_id,
      pending_assignment_id: tripNotes.pending_assignment_id
    };

  } catch (error) {
    logExceptionError('HANDLE_APPROVED', error, { trip_id: approval.trip_log_id });
    throw error;
  }
}

/**
 * Handle rejected exception flow - cancel trip
 */
async function handleRejectedExceptionFlow(client, approval, reviewedBy) {
  try {
    const { trip_log_id } = approval;

    logExceptionFlow('HANDLE_REJECTED', 'Processing rejected exception', {
      trip_id: trip_log_id
    });

    // Mark trip as cancelled due to rejected exception
    await client.query(`
      UPDATE trip_logs 
      SET status = 'cancelled',
          exception_approved_by = $1,
          exception_approved_at = CURRENT_TIMESTAMP,
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [
      reviewedBy, 
      JSON.stringify({ 
        cancellation_reason: 'Exception rejected',
        rejected_at: new Date().toISOString()
      }),
      trip_log_id
    ]);

    return {
      new_status: TRIP_STATES.CANCELLED,
      reason: 'Exception rejected by admin'
    };

  } catch (error) {
    logExceptionError('HANDLE_REJECTED', error, { trip_id: approval.trip_log_id });
    throw error;
  }
}

/**
 * Check approval status for a specific trip
 */
async function checkApprovalStatus(client, tripLogId) {
  try {
    const result = await client.query(`
      SELECT status, reviewed_at, reviewed_by, notes
      FROM approvals 
      WHERE trip_log_id = $1 
      ORDER BY created_at DESC 
      LIMIT 1
    `, [tripLogId]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0].status;
  } catch (error) {
    logExceptionError('CHECK_APPROVAL_STATUS', error, { trip_id: tripLogId });
    throw error;
  }
}

/**
 * Get comprehensive trip status including exception information
 */
async function getTripStatus(client, tripLogId) {
  try {
    const result = await client.query(`
      SELECT 
        tl.id,
        tl.status,
        tl.is_exception,
        tl.exception_reason,
        tl.exception_approved_at,
        tl.exception_approved_by,
        tl.assignment_id,
        tl.notes,
        a.id as approval_id,
        a.status as approval_status,
        a.exception_type,
        a.exception_description,
        a.severity,
        a.reviewed_at,
        a.reviewed_by,
        u.full_name as reviewed_by_name
      FROM trip_logs tl
      LEFT JOIN approvals a ON tl.id = a.trip_log_id
      LEFT JOIN users u ON a.reviewed_by = u.id
      WHERE tl.id = $1
      ORDER BY a.created_at DESC
    `, [tripLogId]);

    if (result.rows.length === 0) {
      throw new Error('Trip not found');
    }

    return result.rows[0];
  } catch (error) {
    logExceptionError('GET_TRIP_STATUS', error, { trip_id: tripLogId });
    throw error;
  }
}

/**
 * Unlock trip for normal progression after exception approval
 * Ensures all fields are set so trip can move through loading, traveling, unloading, completed
 */
async function unlockTripForNormalProgression(client, tripLogId) {
  // Remove any exception-blocking flags or fields if present
  await client.query(`
    UPDATE trip_logs
    SET is_exception = false,
        exception_reason = NULL,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = $1
      AND status = 'loading_start'
  `, [tripLogId]);
}

// Note: unlockTripForNormalProgression is no longer needed for exception approval
// since trips are now marked as 'trip_completed' per Trip Flow Logic requirements

module.exports = {
  EXCEPTION_STATES,
  TRIP_STATES,
  validateTripProgression,
  createExceptionAndSetPending,
  processApprovalAndUpdateTrip,
  handleApprovedExceptionFlow,
  handleRejectedExceptionFlow,
  checkApprovalStatus,
  getTripStatus,
  logExceptionFlow,
  logExceptionError,
  unlockTripForNormalProgression // export for use in other modules if needed
};
