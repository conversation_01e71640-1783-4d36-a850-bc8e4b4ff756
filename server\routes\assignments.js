const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas - Updated for flexible assignments
const assignmentSchema = Joi.object({
  assignment_code: Joi.string().max(50).optional(),
  truck_id: Joi.number().integer().positive().required(),
  driver_id: Joi.number().integer().positive().required(),
  loading_location_id: Joi.number().integer().positive().required(),
  unloading_location_id: Joi.number().integer().positive().required(),
  assigned_date: Joi.date().optional(),
  start_time: Joi.date().optional().allow(null),
  end_time: Joi.date().optional().allow(null),
  status: Joi.string().valid('assigned', 'in_progress', 'completed', 'cancelled').optional(),
  priority: Joi.string().valid('low', 'normal', 'high', 'urgent').optional(),
  expected_loads_per_day: Joi.number().integer().min(1).optional(),
  notes: Joi.string().max(1000).optional().allow('')
});

const updateAssignmentSchema = assignmentSchema.fork(['truck_id', 'driver_id', 'loading_location_id', 'unloading_location_id'], (schema) => schema.optional());

// @route   GET /api/assignments
// @desc    Get all assignments with filtering and search
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      priority = '',
      truck_id = '',
      driver_id = '',
      date_from = '',
      date_to = '',
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const validSortColumns = ['assignment_code', 'created_at', 'assigned_date', 'status', 'priority'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const sortDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toUpperCase() : 'DESC';

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Search functionality
    if (search) {
      paramCount++;
      whereConditions.push(`(
        t.truck_number ILIKE $${paramCount} OR
        t.license_plate ILIKE $${paramCount} OR
        d.full_name ILIKE $${paramCount} OR
        d.employee_id ILIKE $${paramCount} OR
        ll.name ILIKE $${paramCount} OR
        ul.name ILIKE $${paramCount} OR
        a.notes ILIKE $${paramCount}
      )`);
      queryParams.push(`%${search}%`);
    }

    // Status filter
    if (status) {
      paramCount++;
      whereConditions.push(`a.status = $${paramCount}`);
      queryParams.push(status);
    }

    // Priority filter
    if (priority) {
      paramCount++;
      whereConditions.push(`a.priority = $${paramCount}`);
      queryParams.push(priority);
    }

    // Truck filter
    if (truck_id) {
      paramCount++;
      whereConditions.push(`a.truck_id = $${paramCount}`);
      queryParams.push(parseInt(truck_id));
    }

    // Driver filter
    if (driver_id) {
      paramCount++;
      whereConditions.push(`a.driver_id = $${paramCount}`);
      queryParams.push(parseInt(driver_id));
    }

    // Date range filter
    if (date_from) {
      paramCount++;
      whereConditions.push(`a.assigned_date >= $${paramCount}`);
      queryParams.push(date_from);
    }

    if (date_to) {
      paramCount++;
      whereConditions.push(`a.assigned_date <= $${paramCount}`);
      queryParams.push(date_to);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) 
      FROM assignments a
      JOIN dump_trucks t ON a.truck_id = t.id
      JOIN drivers d ON a.driver_id = d.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      ${whereClause}
    `;
    const countResult = await query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data with joins
    const dataQuery = `
      SELECT 
        a.id, a.assignment_code, a.assigned_date, a.start_time, a.end_time,
        a.status, a.priority, a.expected_loads_per_day, a.notes,
        a.created_at, a.updated_at,
        t.id as truck_id, t.truck_number, t.license_plate, t.make, t.model,
        d.id as driver_id, d.employee_id, d.full_name as driver_name,
        ll.id as loading_location_id, ll.location_code as loading_code, ll.name as loading_location_name,
        ul.id as unloading_location_id, ul.location_code as unloading_code, ul.name as unloading_location_name
      FROM assignments a
      JOIN dump_trucks t ON a.truck_id = t.id
      JOIN drivers d ON a.driver_id = d.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      ${whereClause}
      ORDER BY a.${sortColumn} ${sortDirection}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
    
    queryParams.push(parseInt(limit), offset);
    const assignmentsResult = await query(dataQuery, queryParams);

    const totalPages = Math.ceil(totalItems / parseInt(limit));

    res.json({
      success: true,
      data: assignmentsResult.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get assignments error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve assignments'
    });
  }
});

// @route   GET /api/assignments/active
// @desc    Get active assignments (assigned or in_progress)
// @access  Private
router.get('/active', auth, async (req, res) => {
  try {
    const activeQuery = `
      SELECT 
        a.id, a.assignment_code, a.assigned_date, a.start_time, a.end_time,
        a.status, a.priority, a.expected_loads_per_day,
        t.truck_number, t.license_plate,
        d.employee_id, d.full_name as driver_name,
        ll.name as loading_location_name, ul.name as unloading_location_name
      FROM assignments a
      JOIN dump_trucks t ON a.truck_id = t.id
      JOIN drivers d ON a.driver_id = d.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.status IN ('assigned', 'in_progress')
      ORDER BY a.assigned_date ASC
    `;

    const result = await query(activeQuery);

    res.json({
      success: true,
      data: result.rows
    });

  } catch (error) {
    console.error('Get active assignments error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve active assignments'
    });
  }
});

// @route   GET /api/assignments/:id
// @desc    Get single assignment by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      `SELECT 
        a.*,
        t.truck_number, t.license_plate, t.make, t.model,
        d.employee_id, d.full_name as driver_name,
        ll.location_code as loading_code, ll.name as loading_location_name,
        ul.location_code as unloading_code, ul.name as unloading_location_name
       FROM assignments a
       JOIN dump_trucks t ON a.truck_id = t.id
       JOIN drivers d ON a.driver_id = d.id
       JOIN locations ll ON a.loading_location_id = ll.id
       JOIN locations ul ON a.unloading_location_id = ul.id
       WHERE a.id = $1`,
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Assignment not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get assignment error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve assignment'
    });
  }
});

// @route   POST /api/assignments
// @desc    Create new assignment - FIXED
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    // Validate input
    const { error } = assignmentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      truck_id,
      driver_id,
      loading_location_id,
      unloading_location_id,
      assigned_date = new Date().toISOString().split('T')[0],
      start_time = null,
      end_time = null,
      status = 'assigned',
      priority = 'normal',
      expected_loads_per_day = 1,
      notes = ''
    } = req.body;    // Generate unique assignment code
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substr(2, 6).toUpperCase();
    const assignment_code = `ASG-${timestamp}-${randomSuffix}`;

    // Check for duplicate assignment (same truck, loading, and unloading locations with active status)
    // Focus on truck-location combination regardless of date to support exception flows
    // Include 'pending_approval' status to prevent duplicates during exception flows
    const duplicateCheck = await query(`
      SELECT id, assignment_code, status, assigned_date FROM assignments
      WHERE truck_id = $1
        AND loading_location_id = $2
        AND unloading_location_id = $3
        AND status IN ('pending_approval', 'assigned', 'in_progress')
      ORDER BY created_at DESC
      LIMIT 1
    `, [truck_id, loading_location_id, unloading_location_id]);

    if (duplicateCheck.rows.length > 0) {
      const existing = duplicateCheck.rows[0];
      return res.status(400).json({
        error: 'Duplicate Assignment',
        message: `Active assignment already exists for this truck with the same loading and unloading locations. Assignment Code: ${existing.assignment_code} (Status: ${existing.status}, Date: ${existing.assigned_date}). Cannot create duplicate active assignments.`
      });
    }

    // Calculate driver rate based on distance between locations
    let calculatedRate = null;
    try {
      const rateCalculation = await calculateDistanceBasedRate(loading_location_id, unloading_location_id);
      calculatedRate = rateCalculation.final_rate;
    } catch (rateError) {
      console.warn('Rate calculation failed:', rateError.message);
      // Continue with assignment creation even if rate calculation fails
    }
    // Validate foreign keys exist
    const [truckCheck, driverCheck, loadingLocationCheck, unloadingLocationCheck] = await Promise.all([
      query('SELECT id FROM dump_trucks WHERE id = $1 AND status = $2', [truck_id, 'active']),
      query('SELECT id FROM drivers WHERE id = $1 AND status = $2', [driver_id, 'active']),
      query('SELECT id FROM locations WHERE id = $1 AND is_active = $2', [loading_location_id, true]),
      query('SELECT id FROM locations WHERE id = $1 AND is_active = $2', [unloading_location_id, true])
    ]);

    if (truckCheck.rows.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Selected truck is not available or inactive'
      });
    }

    if (driverCheck.rows.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Selected driver is not available or inactive'
      });
    }

    if (loadingLocationCheck.rows.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Selected loading location is not available or inactive'
      });
    }

    if (unloadingLocationCheck.rows.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Selected unloading location is not available or inactive'
      });
    }    // Insert new assignment with calculated rate
    const result = await query(
      `INSERT INTO assignments 
       (assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id, 
        assigned_date, start_time, end_time, status, priority, expected_loads_per_day, notes, driver_rate)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
       RETURNING *`,
      [assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id, 
       assigned_date, start_time, end_time, status, priority, expected_loads_per_day, 
       notes + (calculatedRate ? ` [Rate: $${calculatedRate}]` : ''), calculatedRate]
    );    res.status(201).json({
      success: true,
      message: 'Assignment created successfully',
      data: result.rows[0],
      calculated_rate: calculatedRate
    });
  } catch (error) {
    console.error('Create assignment error:', error);
    
    // Handle specific database errors
    if (error.code === '23505') { // Unique constraint violation
      if (error.constraint === 'assignments_assignment_code_key') {
        return res.status(400).json({
          error: 'Duplicate Error',
          message: 'Assignment code already exists. Please try again.'
        });
      } else if (error.constraint === 'idx_assignments_exact_duplicate') {
        return res.status(400).json({
          error: 'Duplicate Assignment',
          message: 'An identical assignment already exists for this truck with the same loading and unloading locations on this date.'
        });
      } else {
        return res.status(400).json({
          error: 'Duplicate Error', 
          message: 'A similar assignment already exists'
        });
      }
    }

    if (error.code === '42703') { // Column does not exist
      return res.status(500).json({
        error: 'Database Error',
        message: 'Database schema mismatch - please run migrations'
      });
    }

    if (error.code === '23503') { // Foreign key constraint violation
      return res.status(400).json({
        error: 'Reference Error',
        message: 'Selected truck, driver, or location is invalid or inactive'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create assignment',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PUT /api/assignments/:id
// @desc    Update assignment
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateAssignmentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    // Check if assignment exists
    const existingAssignment = await query(
      'SELECT * FROM assignments WHERE id = $1',
      [id]
    );

    if (existingAssignment.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Assignment not found'
      });
    }

    const {
      assignment_code,
      truck_id,
      driver_id,
      loading_location_id,
      unloading_location_id,
      assigned_date,
      start_time,
      end_time,
      status,
      priority,
      expected_loads_per_day,
      notes
    } = req.body;

    // Check for duplicates (excluding current assignment)
    if (assignment_code) {
      const duplicateCheck = await query(
        'SELECT id FROM assignments WHERE assignment_code = $1 AND id != $2',
        [assignment_code, id]
      );

      if (duplicateCheck.rows.length > 0) {
        return res.status(400).json({
          error: 'Duplicate Error',
          message: 'Assignment code already exists'
        });
      }
    }

    // Build update query dynamically
    const updates = [];
    const values = [];
    let paramCount = 0;

    const fields = {
      assignment_code,
      truck_id,
      driver_id,
      loading_location_id,
      unloading_location_id,
      assigned_date,
      start_time,
      end_time,
      status,
      priority,
      expected_loads_per_day,
      notes
    };

    Object.entries(fields).forEach(([key, value]) => {
      if (value !== undefined) {
        paramCount++;
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
      }
    });

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No fields to update'
      });
    }

    // Add updated_at
    paramCount++;
    updates.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add id for WHERE clause
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE assignments 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await query(updateQuery, values);

    res.json({
      success: true,
      message: 'Assignment updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update assignment error:', error);
    
    if (error.code === '23505') {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Assignment code already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update assignment'
    });
  }
});

// @route   DELETE /api/assignments/:id
// @desc    Delete assignment
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if assignment exists
    const existingAssignment = await query(
      'SELECT * FROM assignments WHERE id = $1',
      [id]
    );

    if (existingAssignment.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Assignment not found'
      });
    }

    // Check if assignment has active trips
    const activeTrips = await query(
      'SELECT id FROM trip_logs WHERE assignment_id = $1 AND status IN ($2, $3)',
      [id, 'assigned', 'loading_start']
    );

    if (activeTrips.rows.length > 0) {
      return res.status(400).json({
        error: 'Conflict',
        message: 'Cannot delete assignment with active trips'
      });
    }

    // Delete assignment
    await query('DELETE FROM assignments WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Assignment deleted successfully'
    });

  } catch (error) {
    console.error('Delete assignment error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete assignment'
    });
  }
});

module.exports = router;
// @route   POST /api/assignments/calculate-rate
// @desc    Calculate driver rate based on distance between locations
// @access  Private
router.post('/calculate-rate', auth, async (req, res) => {
  try {
    const { loading_location_id, unloading_location_id } = req.body;

    if (!loading_location_id || !unloading_location_id) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Both loading_location_id and unloading_location_id are required'
      });
    }

    // Get coordinates for both locations
    const locationsResult = await query(`
      SELECT 
        id, name, coordinates, type,
        CASE WHEN id = $1 THEN 'loading' ELSE 'unloading' END as location_role
      FROM locations 
      WHERE id IN ($1, $2) AND is_active = true
    `, [loading_location_id, unloading_location_id]);

    if (locationsResult.rows.length !== 2) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'One or both locations not found or inactive'
      });
    }

    const loadingLocation = locationsResult.rows.find(loc => loc.location_role === 'loading');
    const unloadingLocation = locationsResult.rows.find(loc => loc.location_role === 'unloading');

    // Parse coordinates
    let loadingCoords, unloadingCoords;
    try {
      loadingCoords = typeof loadingLocation.coordinates === 'string' 
        ? JSON.parse(loadingLocation.coordinates) 
        : loadingLocation.coordinates;
      unloadingCoords = typeof unloadingLocation.coordinates === 'string' 
        ? JSON.parse(unloadingLocation.coordinates) 
        : unloadingLocation.coordinates;
    } catch (error) {
      return res.status(400).json({
        error: 'Data Error',
        message: 'Invalid coordinates format in location data'
      });
    }

    // Calculate distance using Haversine formula
    const distance = calculateHaversineDistance(
      loadingCoords.lat, loadingCoords.lng,
      unloadingCoords.lat, unloadingCoords.lng
    );

    // Rate calculation logic (customize based on business rules)
    const baseRate = 50; // Base rate per trip
    const ratePerKm = 2.5; // Rate per kilometer
    const minimumRate = 75; // Minimum rate regardless of distance
    
    const distanceRate = distance * ratePerKm;
    const calculatedRate = Math.max(baseRate + distanceRate, minimumRate);
    
    // Round to 2 decimal places
    const finalRate = Math.round(calculatedRate * 100) / 100;

    res.json({
      success: true,
      data: {
        loading_location: {
          id: loadingLocation.id,
          name: loadingLocation.name,
          coordinates: loadingCoords
        },
        unloading_location: {
          id: unloadingLocation.id,
          name: unloadingLocation.name,
          coordinates: unloadingCoords
        },
        distance_km: Math.round(distance * 100) / 100,
        rate_calculation: {
          base_rate: baseRate,
          rate_per_km: ratePerKm,
          distance_rate: Math.round(distanceRate * 100) / 100,
          minimum_rate: minimumRate,
          calculated_rate: finalRate
        },
        final_rate: finalRate,
        currency: 'USD' // or your local currency
      }
    });

  } catch (error) {
    console.error('Calculate rate error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to calculate driver rate'
    });
  }
});

// Helper function to calculate distance between two coordinates using Haversine formula
function calculateHaversineDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in kilometers
  
  return distance;
}

// Helper function to convert degrees to radians
function toRadians(degrees) {
  return degrees * (Math.PI / 180);
}

// Helper function to calculate driver rate based on distance
async function calculateDistanceBasedRate(loading_location_id, unloading_location_id) {
  // Get coordinates for both locations
  const locationsResult = await query(`
    SELECT 
      id, name, coordinates, type,
      CASE WHEN id = $1 THEN 'loading' ELSE 'unloading' END as location_role
    FROM locations 
    WHERE id IN ($1, $2) AND is_active = true
  `, [loading_location_id, unloading_location_id]);

  if (locationsResult.rows.length !== 2) {
    throw new Error('One or both locations not found or inactive');
  }

  const loadingLocation = locationsResult.rows.find(loc => loc.location_role === 'loading');
  const unloadingLocation = locationsResult.rows.find(loc => loc.location_role === 'unloading');

  // Parse coordinates
  let loadingCoords, unloadingCoords;
  try {
    loadingCoords = typeof loadingLocation.coordinates === 'string' 
      ? JSON.parse(loadingLocation.coordinates) 
      : loadingLocation.coordinates;
    unloadingCoords = typeof unloadingLocation.coordinates === 'string' 
      ? JSON.parse(unloadingLocation.coordinates) 
      : unloadingLocation.coordinates;
  } catch (error) {
    throw new Error('Invalid coordinates format in location data');
  }

  // Calculate distance using Haversine formula
  const distance = calculateHaversineDistance(
    loadingCoords.lat, loadingCoords.lng,
    unloadingCoords.lat, unloadingCoords.lng
  );

  // Rate calculation logic
  const baseRate = 50; // Base rate per trip
  const ratePerKm = 2.5; // Rate per kilometer
  const minimumRate = 75; // Minimum rate regardless of distance
  
  const distanceRate = distance * ratePerKm;
  const calculatedRate = Math.max(baseRate + distanceRate, minimumRate);
  
  // Round to 2 decimal places
  const finalRate = Math.round(calculatedRate * 100) / 100;

  return {
    distance_km: Math.round(distance * 100) / 100,
    final_rate: finalRate,
    calculation_details: {
      base_rate: baseRate,
      rate_per_km: ratePerKm,
      distance_rate: Math.round(distanceRate * 100) / 100,
      minimum_rate: minimumRate
    }
  };
}