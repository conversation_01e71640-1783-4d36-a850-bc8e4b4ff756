const { query } = require('../config/database');

async function checkSyntax() {
  console.log('🔍 Testing database connection and syntax...');
  
  try {
    const result = await query('SELECT NOW() as current_time');
    console.log('✅ Connection successful:', result.rows[0].current_time);
    
    // Check the approval #3
    const approval = await query('SELECT id, status FROM approvals WHERE id = 3');
    if (approval.rows.length > 0) {
      console.log('✅ Approval #3 status:', approval.rows[0].status);
    } else {
      console.log('❌ Approval #3 not found');
    }
    
    // Check assignment constraint
    const constraintInfo = await query(`
      SELECT conname, contype, pg_get_constraintdef(c.oid) as def
      FROM pg_constraint c
      JOIN pg_class t ON c.conrelid = t.oid
      JOIN pg_namespace n ON t.relnamespace = n.oid
      WHERE t.relname = 'assignments'
        AND conname = 'idx_assignments_exact_duplicate'
    `);
    
    if (constraintInfo.rows.length > 0) {
      console.log('✅ Found constraint definition:');
      console.log(constraintInfo.rows[0].def);
    } else {
      console.log('❌ Could not find constraint definition');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkSyntax()
  .then(() => console.log('Done'))
  .catch(err => console.error('Fatal error:', err));
