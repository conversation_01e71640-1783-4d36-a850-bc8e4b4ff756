import React, { useState } from 'react';
import QRCode from 'qrcode.react';

const QRTestGenerator = () => {
  const [qrType, setQrType] = useState('location');
  const [locationId, setLocationId] = useState('LOC-001');
  const [locationName, setLocationName] = useState('Point A - Main Loading Site');
  const [truckId, setTruckId] = useState('DT-100');
  const [driverId, setDriverId] = useState('DR-001');

  const generateQRData = () => {
    if (qrType === 'location') {
      return JSON.stringify({
        type: 'location',
        id: locationId,
        name: locationName,
        coordinates: '40.7128,-74.0060',
        timestamp: new Date().toISOString()
      });
    } else {
      return JSON.stringify({
        type: 'truck',
        id: truckId,
        assigned_route: 'A-B',
        driver_id: driverId,
        timestamp: new Date().toISOString()
      });
    }
  };

  const downloadQR = () => {
    const canvas = document.getElementById('qr-canvas');
    const link = document.createElement('a');
    link.download = `${qrType}-${qrType === 'location' ? locationId : truckId}.png`;
    link.href = canvas.toDataURL();
    link.click();
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-secondary-200 p-6">
      <h3 className="text-lg font-medium text-secondary-900 mb-4">🧪 QR Code Test Generator</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              QR Code Type
            </label>
            <select
              value={qrType}
              onChange={(e) => setQrType(e.target.value)}
              className="input"
            >
              <option value="location">Location QR Code</option>
              <option value="truck">Truck QR Code</option>
            </select>
          </div>

          {qrType === 'location' ? (
            <>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Location ID
                </label>
                <select
                  value={locationId}
                  onChange={(e) => {
                    setLocationId(e.target.value);
                    setLocationName(
                      e.target.value === 'LOC-001' ? 'Point A - Main Loading Site' :
                      e.target.value === 'LOC-002' ? 'Point B - Primary Dump Site' :
                      'Point C - Secondary Dump Site'
                    );
                  }}
                  className="input"
                >
                  <option value="LOC-001">LOC-001 - Point A (Loading)</option>
                  <option value="LOC-002">LOC-002 - Point B (Unloading)</option>
                  <option value="LOC-003">LOC-003 - Point C (Unloading)</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Location Name
                </label>
                <input
                  type="text"
                  value={locationName}
                  onChange={(e) => setLocationName(e.target.value)}
                  className="input"
                />
              </div>
            </>
          ) : (
            <>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Truck ID
                </label>
                <select
                  value={truckId}
                  onChange={(e) => setTruckId(e.target.value)}
                  className="input"
                >
                  <option value="DT-100">DT-100</option>
                  <option value="DT-101">DT-101</option>
                  <option value="DT-102">DT-102</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Driver ID
                </label>
                <select
                  value={driverId}
                  onChange={(e) => setDriverId(e.target.value)}
                  className="input"
                >
                  <option value="DR-001">DR-001</option>
                  <option value="DR-002">DR-002</option>
                  <option value="DR-003">DR-003</option>
                </select>
              </div>
            </>
          )}

          <button
            onClick={downloadQR}
            className="btn btn-primary w-full"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-7 7h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Download QR Code
          </button>
        </div>

        <div className="flex flex-col items-center space-y-4">
          <QRCode
            id="qr-canvas"
            value={generateQRData()}
            size={200}
            level="M"
            includeMargin={true}
            renderAs="canvas"
          />
          
          <div className="text-center">
            <p className="text-sm font-medium text-secondary-900">
              {qrType === 'location' ? locationName : `Truck ${truckId}`}
            </p>
            <p className="text-xs text-secondary-500 mt-1">
              {qrType === 'location' ? locationId : `Driver: ${driverId}`}
            </p>
          </div>

          <div className="bg-secondary-50 p-3 rounded-lg max-w-xs">
            <p className="text-xs text-secondary-600 font-mono break-all">
              {generateQRData()}
            </p>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Testing Instructions:</h4>
        <ol className="text-sm text-blue-800 space-y-1">
          <li>1. Generate and download location QR codes (LOC-001, LOC-002, LOC-003)</li>
          <li>2. Generate and download truck QR codes (DT-100, DT-101, DT-102)</li>
          <li>3. Display QR codes on separate devices or print them</li>
          <li>4. Use the QR Scanner to test the two-step scanning workflow</li>
          <li>5. Test multi-operator scenarios by scanning from different browsers/devices</li>
        </ol>
      </div>
    </div>
  );
};

export default QRTestGenerator;