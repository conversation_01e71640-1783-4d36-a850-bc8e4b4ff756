import React, { useState, useEffect, useCallback } from 'react';
import { analyticsAPI, tripsAPI } from '../../services/api';
import { format } from 'date-fns';
import toast from 'react-hot-toast';
import AssignmentFormModal from '../assignments/components/AssignmentFormModal';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [recentTrips, setRecentTrips] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);

  // Load dashboard data from API
  const loadDashboardData = useCallback(async () => {
    try {
      const [analyticsResponse, tripsResponse] = await Promise.all([
        analyticsAPI.getDashboard(),
        tripsAPI.getAll({ 
          params: { 
            page: 1, 
            limit: 5, 
            sortBy: 'created_at', 
            sortOrder: 'desc' 
          } 
        })
      ]);

      setDashboardData(analyticsResponse.data.data);
      setRecentTrips(tripsResponse.data.data || []);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setRefreshing(true);
      loadDashboardData();
    }, 30000);

    return () => clearInterval(interval);
  }, [loadDashboardData]);

  // Prepare stats from real API data
  const getStats = () => {
    if (!dashboardData) return [];

    return [
      {
        title: 'Active Trips',
        value: dashboardData.trips?.todayTrips || '0',
        change: `${dashboardData.trips?.todayCompleted || 0} completed today`,
        icon: '🛣️',
        color: 'bg-primary-500'
      },
      {
        title: 'Total Trucks',
        value: dashboardData.fleet?.totalTrucks || '0',
        change: `${dashboardData.fleet?.activeTrucks || 0} active`,
        icon: '🚛',
        color: 'bg-success-500'
      },
      {
        title: 'Active Drivers',
        value: dashboardData.fleet?.activeDrivers || '0',
        change: `${dashboardData.fleet?.totalDrivers || 0} total drivers`,
        icon: '👤',
        color: 'bg-warning-500'
      },
      {
        title: 'Locations',
        value: dashboardData.fleet?.activeLocations || '0',
        change: `${dashboardData.fleet?.totalLocations || 0} total locations`,
        icon: '📍',
        color: 'bg-info-500'
      }
    ];
  };

  const stats = getStats();
  const getStatusBadge = (status) => {
    const statusClasses = {
      'assigned': 'bg-blue-100 text-blue-800',
      'loading_start': 'bg-yellow-100 text-yellow-800',
      'loading_end': 'bg-orange-100 text-orange-800',
      'unloading_start': 'bg-purple-100 text-purple-800',
      'unloading_end': 'bg-green-100 text-green-800',
      'trip_completed': 'bg-green-100 text-green-800',
      'cancelled': 'bg-red-100 text-red-800'
    };

    const statusLabels = {
      'assigned': 'Assigned',
      'loading_start': 'Loading',
      'loading_end': 'Loaded',
      'unloading_start': 'Unloading',
      'unloading_end': 'Unloaded',
      'trip_completed': 'Completed',
      'cancelled': 'Cancelled'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status] || 'bg-secondary-100 text-secondary-800'}`}>
        {statusLabels[status] || status}
      </span>
    );
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const handleNewAssignment = () => {
    setShowAssignmentModal(true);
  };

  const handleAssignmentModalClose = () => {
    setShowAssignmentModal(false);
  };

  const handleAssignmentSubmit = async (formData) => {
    try {
      // Use assignmentsAPI to create new assignment
      const { assignmentsAPI } = require('../../services/api');
      await assignmentsAPI.create(formData);
      toast.success('Assignment created successfully!');
      setShowAssignmentModal(false);
      // Optionally refresh dashboard data
      handleRefresh();
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to create assignment');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-secondary-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white p-5 rounded-lg border border-secondary-200">
                <div className="h-4 bg-secondary-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-secondary-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-secondary-900 sm:text-3xl sm:truncate">
            Dashboard Overview
          </h1>
          <p className="mt-1 text-sm text-secondary-500">
            Welcome back! Here's what's happening with your hauling operations today.
          </p>
        </div>
        <div className="mt-4 flex space-x-3 md:mt-0 md:ml-4">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn btn-secondary"
          >
            <svg className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </button>
          <button className="btn btn-primary" onClick={handleNewAssignment}>
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            New Assignment
          </button>
        </div>
      </div>

      {/* Performance Metrics */}
      {dashboardData?.performance && (
        <div className="bg-white rounded-lg shadow-sm border border-secondary-200 p-6">
          <h3 className="text-lg font-medium text-secondary-900 mb-4">Performance Metrics (Last 30 Days)</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">
                {Math.round(dashboardData.performance.completionRate || 0)}%
              </div>
              <div className="text-sm text-secondary-600">Completion Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success-600">
                {Math.round(dashboardData.performance.onTimeRate || 0)}%
              </div>
              <div className="text-sm text-secondary-600">On-Time Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-warning-600">
                {Math.round(dashboardData.performance.avgTripTime || 0)} min
              </div>
              <div className="text-sm text-secondary-600">Avg Trip Time</div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Grid - Now showing REAL data from database */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-md ${stat.color} flex items-center justify-center`}>
                    <span className="text-white text-lg">{stat.icon}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-secondary-500 truncate">
                      {stat.title}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-secondary-900">
                        {stat.value}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-3">
                <p className="text-sm text-secondary-600">{stat.change}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Trips - REAL data from database */}
        <div className="bg-white shadow-sm rounded-lg border border-secondary-200">
          <div className="px-6 py-4 border-b border-secondary-200">
            <h2 className="text-lg font-semibold text-secondary-900">Recent Trips</h2>
            <p className="text-sm text-secondary-500">Latest trip activities and status updates</p>
          </div>
          <div className="divide-y divide-secondary-200">
            {recentTrips.length > 0 ? (
              recentTrips.map((trip) => (
                <div key={trip.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <span className="text-lg">🚛</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-secondary-900">
                          {trip.truck_number || 'N/A'} - {trip.driver_name || 'No Driver'}
                        </p>
                        <p className="text-sm text-secondary-500">
                          {trip.loading_location_name || 'Unknown'} → {trip.unloading_location_name || 'Unknown'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(trip.status)}
                      <span className="text-xs text-secondary-400">
                        {trip.created_at ? format(new Date(trip.created_at), 'HH:mm') : ''}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-6 py-8 text-center text-secondary-500">
                <span className="text-4xl block mb-2">📋</span>
                No recent trips found
              </div>
            )}
          </div>
          <div className="px-6 py-3 bg-secondary-50 border-t border-secondary-200">
            <button className="text-sm text-primary-600 hover:text-primary-500 font-medium">
              View all trips →
            </button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow-sm rounded-lg border border-secondary-200">
          <div className="px-6 py-4 border-b border-secondary-200">
            <h2 className="text-lg font-semibold text-secondary-900">Quick Actions</h2>
            <p className="text-sm text-secondary-500">Common tasks and operations</p>
          </div>
          <div className="p-6 space-y-4">
            <a 
              href="/scanner" 
              className="w-full flex items-center p-3 text-left border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors"
            >
              <span className="text-lg mr-3">📱</span>
              <div>
                <p className="text-sm font-medium text-secondary-900">Scan QR Code</p>
                <p className="text-xs text-secondary-500">Process truck or location scan</p>
              </div>
            </a>
            
            <a 
              href="/trucks" 
              className="w-full flex items-center p-3 text-left border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors"
            >
              <span className="text-lg mr-3">🚛</span>
              <div>
                <p className="text-sm font-medium text-secondary-900">Manage Trucks</p>
                <p className="text-xs text-secondary-500">Add, edit, or view truck fleet</p>
              </div>
            </a>
            
            <a 
              href="/assignments" 
              className="w-full flex items-center p-3 text-left border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors"
            >
              <span className="text-lg mr-3">📋</span>
              <div>
                <p className="text-sm font-medium text-secondary-900">Assignments</p>
                <p className="text-xs text-secondary-500">Create and manage route assignments</p>
              </div>
            </a>
            
            <a 
              href="/analytics" 
              className="w-full flex items-center p-3 text-left border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors"
            >
              <span className="text-lg mr-3">📈</span>
              <div>
                <p className="text-sm font-medium text-secondary-900">View Analytics</p>
                <p className="text-xs text-secondary-500">Performance reports and insights</p>
              </div>
            </a>
          </div>
        </div>
      </div>

      {/* System Status - REAL data from database */}
      {dashboardData && (
        <div className="bg-white rounded-lg shadow-sm border border-secondary-200 p-6">
          <h3 className="text-lg font-medium text-secondary-900 mb-4">System Status</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-sm text-secondary-600">Active Assignments</div>
              <div className="text-xl font-semibold text-primary-600">
                {dashboardData.fleet?.activeAssignments || 0}
              </div>
            </div>
            <div>
              <div className="text-sm text-secondary-600">Weekly Trips</div>
              <div className="text-xl font-semibold text-success-600">
                {dashboardData.trips?.weeklyTrips || 0}
              </div>
            </div>
            <div>
              <div className="text-sm text-secondary-600">Monthly Completed</div>
              <div className="text-xl font-semibold text-blue-600">
                {dashboardData.trips?.monthlyCompleted || 0}
              </div>
            </div>
            <div>
              <div className="text-sm text-secondary-600">Exception Rate</div>
              <div className="text-xl font-semibold text-warning-600">
                {Math.round(dashboardData.performance?.exceptionRate || 0)}%
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Assignment Modal */}
      {showAssignmentModal && (
        <AssignmentFormModal
          onClose={handleAssignmentModalClose}
          onSubmit={handleAssignmentSubmit}
        />
      )}
    </div>
  );
};

export default Dashboard;