@echo off
setlocal enabledelayedexpansion

echo.
echo 🚛 Hauling QR Trip System - Production Deployment
echo ==================================================

REM Check if Docker is installed
docker --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Check if .env.production exists
if not exist .env.production (
    echo ⚠️  .env.production file not found!
    echo 📝 Copying .env.production.example to .env.production
    copy .env.production.example .env.production
    echo.
    echo ⚠️  Please edit .env.production with your actual values before proceeding.
    echo 📖 Key variables to update:
    echo    - DB_PASSWORD
    echo    - JWT_SECRET
    echo    - SSL certificate paths (if using HTTPS)
    echo.
    pause
)

echo 🔧 Building Docker images...
docker-compose build --no-cache

echo 🗄️  Setting up database...
echo Starting PostgreSQL container...
docker-compose up -d postgres

echo Waiting for PostgreSQL to be ready...
timeout /t 10 /nobreak >nul

echo 🔄 Running database initialization...
docker-compose exec -T postgres psql -U postgres -d hauling_qr_system -f /docker-entrypoint-initdb.d/init.sql

echo 🚀 Starting all services...
docker-compose up -d

echo ⏳ Waiting for services to be ready...
timeout /t 30 /nobreak >nul

echo 🔍 Performing health checks...
curl -f http://localhost:5000/api/health >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ Backend health check passed
) else (
    echo ❌ Backend health check failed
    echo 📋 Checking logs:
    docker-compose logs app
    pause
    exit /b 1
)

echo.
echo 📊 Deployment Summary:
echo =====================
echo ✅ Application: http://localhost:5000
echo ✅ Database: PostgreSQL on port 5432
echo ✅ Redis: Running on port 6379
echo.
echo 🎉 Deployment completed successfully!
echo 📖 Run 'docker-compose logs -f' to view real-time logs
echo 🛑 Run 'docker-compose down' to stop all services
echo.
pause
