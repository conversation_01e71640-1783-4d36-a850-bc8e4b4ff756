# Production Environment Variables
# Copy this file to .env.production and update with your actual values

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=PostgreSQLPassword

# Application Configuration
NODE_ENV=production
PORT=5000
JWT_SECRET=hauling_qr_jwt_secret_2025_secure_key_for_production 
JWT_EXPIRY=24h

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379

# SSL Configuration
SSL_CERT_PATH=/path/to/ssl/certificate.crt
SSL_KEY_PATH=/path/to/ssl/private.key

# API Configuration
API_BASE_URL=https://yourdomain.com/api
CLIENT_URL=https://yourdomain.com

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# File Upload Limits
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf
