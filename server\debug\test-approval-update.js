const { query, getClient } = require('../config/database');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function testApprovalUpdate() {
  console.log('Testing approval update functionality...');

  try {
    // First, check if we have any pending approvals
    const pendingApprovals = await query(`
      SELECT a.*, tl.trip_number 
      FROM approvals a
      JOIN trip_logs tl ON a.trip_log_id = tl.id
      WHERE a.status = 'pending'
      ORDER BY a.created_at DESC
      LIMIT 1
    `);

    if (pendingApprovals.rows.length === 0) {
      console.log('No pending approvals found. Creating a test approval...');
      
      // Get a test trip log
      const tripLogs = await query(`
        SELECT id FROM trip_logs 
        WHERE status != 'completed' 
        ORDER BY created_at DESC 
        LIMIT 1
      `);

      if (tripLogs.rows.length === 0) {
        console.log('No trip logs available for testing');
        return;
      }

      // Create a test approval
      const createResult = await query(`
        INSERT INTO approvals (
          trip_log_id, exception_type, exception_description, 
          severity, reported_by, status, requested_at,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, 'pending', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *
      `, [
        tripLogs.rows[0].id,
        'route_deviation',
        'Loading at unexpected location for testing',
        'medium',
        1 // assuming user ID 1 exists
      ]);

      console.log('Created test approval:', createResult.rows[0]);
      console.log('Test approval ID:', createResult.rows[0].id);
      return;
    }

    const approval = pendingApprovals.rows[0];
    console.log('Found pending approval:', approval);

    // Test the approval update process
    const client = await getClient();
    
    try {
      await client.query('BEGIN');      // Get approval details with lock (simulating the PUT endpoint logic)
      const approvalResult = await client.query(
        `SELECT a.id as approval_id, a.status as approval_status, a.exception_description,
                a.trip_log_id, a.created_at as approval_created_at, a.updated_at as approval_updated_at,
                tl.id as trip_id, tl.status as trip_status, tl.notes as trip_notes,
                tl.assignment_id, tl.exception_reason
         FROM approvals a
         JOIN trip_logs tl ON a.trip_log_id = tl.id
         WHERE a.id = $1
         FOR UPDATE`,
        [approval.id]
      );

      if (approvalResult.rows.length === 0) {
        console.log('Approval not found');
        await client.query('ROLLBACK');
        return;
      }

      const fullApproval = approvalResult.rows[0];
      console.log('Full approval data:', fullApproval);

      if (fullApproval.approval_status !== 'pending') {
        console.log('Approval is not pending, status:', fullApproval.approval_status);
        await client.query('ROLLBACK');
        return;
      }

      // Test the update query (simulating approved decision)
      const decision = 'approved';
      const userId = 1; // test user
      const notes = 'Test approval';

      const updateResult = await client.query(`
        UPDATE approvals 
        SET status = $1, reviewed_by = $2, reviewed_at = CURRENT_TIMESTAMP, 
            notes = $3, updated_at = CURRENT_TIMESTAMP
        WHERE id = $4
        RETURNING *
      `, [decision, userId, notes, approval.id]);

      console.log('Update result:', updateResult.rows[0]);

      // Test the handleApprovedException logic
      if (decision === 'approved') {
        console.log('Testing approved exception handling...');
        const { trip_log_id, exception_description } = fullApproval;
        
        console.log('Exception description:', exception_description);
        
        if (exception_description && exception_description.includes('Loading at')) {
          console.log('This is a route deviation for loading');
          
          // Parse trip notes
          let tripNotes;
          try {
            tripNotes = fullApproval.trip_notes ? JSON.parse(fullApproval.trip_notes) : {};
          } catch (e) {
            console.log('Failed to parse trip notes:', e.message);
            tripNotes = {};
          }

          console.log('Trip notes:', tripNotes);

          if (tripNotes.pending_assignment_id) {
            console.log('Has pending assignment ID:', tripNotes.pending_assignment_id);
            // This would update trip log and assignment
          } else {
            console.log('No pending assignment, just updating trip status');
            await client.query(`
              UPDATE trip_logs 
              SET status = 'loading_start',
                  exception_approved_by = $1,
                  exception_approved_at = CURRENT_TIMESTAMP,
                  updated_at = CURRENT_TIMESTAMP
              WHERE id = $2
            `, [userId, trip_log_id]);
          }
        } else {
          console.log('Not a route deviation, just marking as approved');
          await client.query(`
            UPDATE trip_logs 
            SET exception_approved_by = $1,
                exception_approved_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $2
          `, [userId, trip_log_id]);
        }
      }

      await client.query('COMMIT');
      
      // Verify the final state
      const finalState = await query('SELECT * FROM approvals WHERE id = $1', [approval.id]);
      console.log('Final approval state:', finalState.rows[0]);

      console.log('✅ Approval update test completed successfully');

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testApprovalUpdate();
