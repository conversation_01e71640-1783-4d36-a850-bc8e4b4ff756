-- Migration: Add missing columns to assignments table
-- Date: 2025-06-20
-- Purpose: Fix 500 errors caused by missing assignment_code and priority columns

-- Add assignment_code column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'assignments' AND column_name = 'assignment_code') THEN
        ALTER TABLE assignments ADD COLUMN assignment_code VARCHAR(50) UNIQUE;
        
        -- Generate assignment codes for existing records
        UPDATE assignments SET assignment_code = 'ASG-' || id || '-' || EXTRACT(EPOCH FROM created_at)::bigint
        WHERE assignment_code IS NULL;
    END IF;
END $$;

-- Add priority column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'assignments' AND column_name = 'priority') THEN
        ALTER TABLE assignments ADD COLUMN priority VARCHAR(20) DEFAULT 'normal' 
        CHECK (priority IN ('low', 'normal', 'high', 'urgent'));
        
        -- Set default priority for existing records
        UPDATE assignments SET priority = 'normal' WHERE priority IS NULL;
    END IF;
END $$;

-- <PERSON>reate index on assignment_code for better performance
CREATE INDEX IF NOT EXISTS idx_assignments_assignment_code ON assignments(assignment_code);

-- Create index on priority for filtering
CREATE INDEX IF NOT EXISTS idx_assignments_priority ON assignments(priority);

COMMIT;
