-- Migration: Remove restrictive unique constraints from assignments table
-- This allows multiple assignments for the same truck/driver with different routes

-- Remove the unique constraints that prevent multiple assignments per truck/driver per day
ALTER TABLE assignments DROP CONSTRAINT IF EXISTS assignments_truck_id_assigned_date_key;
ALTER TABLE assignments DROP CONSTRAINT IF EXISTS assignments_driver_id_assigned_date_key;

-- Add a comment to document the change
COMMENT ON TABLE assignments IS 'Assignments table - allows multiple assignments per truck/driver with different loading/unloading locations';

-- Create a partial unique index that only prevents exact duplicates
CREATE UNIQUE INDEX IF NOT EXISTS idx_assignments_exact_duplicate 
ON assignments (truck_id, loading_location_id, unloading_location_id, assigned_date) 
WHERE status IN ('assigned', 'in_progress');

COMMENT ON INDEX idx_assignments_exact_duplicate IS 'Prevents exact duplicate assignments (same truck, same locations, same date)';
