import React, { useState, useEffect, useCallback } from 'react';
import { trucksAPI } from '../../services/api';
import TrucksTable from './components/TrucksTable';
import TruckFormModal from './components/TruckFormModal';
import QRCodeModal from '../../components/common/QRCodeModal';
import DeleteConfirmModal from '../../components/common/DeleteConfirmModal';
import toast from 'react-hot-toast';

const TrucksManagement = () => {
  const [trucks, setTrucks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  });

  // Modal states
  const [showFormModal, setShowFormModal] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTruck, setSelectedTruck] = useState(null);
  const [qrData, setQrData] = useState(null);

  // Filter and search states
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    make: '',
    sortBy: 'truck_number',
    sortOrder: 'asc'
  });

  // Load trucks
  const loadTrucks = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pagination.itemsPerPage,
        ...filters
      };

      const response = await trucksAPI.getAll({ params });
      setTrucks(response.data.data);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Error loading trucks:', error);
      toast.error('Failed to load trucks');
    } finally {
      setLoading(false);
    }
  }, [pagination.itemsPerPage, filters]);

  // Initial load
  useEffect(() => {
    loadTrucks();
  }, [loadTrucks]);

  // Handle page change
  const handlePageChange = (page) => {
    loadTrucks(page);
  };

  // Handle filter change
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle search
  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  };

  // Handle sort
  const handleSort = (column) => {
    const newOrder = filters.sortBy === column && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: newOrder
    }));
  };

  // Handle create truck
  const handleCreate = () => {
    setSelectedTruck(null);
    setShowFormModal(true);
  };

  // Handle edit truck
  const handleEdit = (truck) => {
    setSelectedTruck(truck);
    setShowFormModal(true);
  };

  // Handle delete truck
  const handleDelete = (truck) => {
    setSelectedTruck(truck);
    setShowDeleteModal(true);
  };

  // Handle view QR code
  const handleViewQR = async (truck) => {
    try {
      const response = await trucksAPI.generateQR(truck.id);
      setQrData(response.data.data);
      setShowQRModal(true);
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error('Failed to generate QR code');
    }
  };

  // Handle form submit
  const handleFormSubmit = async (formData) => {
    try {
      if (selectedTruck) {
        // Update
        await trucksAPI.update(selectedTruck.id, formData);
        toast.success('Truck updated successfully');
      } else {
        // Create
        await trucksAPI.create(formData);
        toast.success('Truck created successfully');
      }
      
      setShowFormModal(false);
      loadTrucks(pagination.currentPage);
    } catch (error) {
      console.error('Error saving truck:', error);
      const message = error.response?.data?.message || 'Failed to save truck';
      toast.error(message);
    }
  };

  // Handle delete confirm
  const handleDeleteConfirm = async () => {
    try {
      await trucksAPI.delete(selectedTruck.id);
      toast.success('Truck deleted successfully');
      setShowDeleteModal(false);
      loadTrucks(pagination.currentPage);
    } catch (error) {
      console.error('Error deleting truck:', error);
      const message = error.response?.data?.message || 'Failed to delete truck';
      toast.error(message);
    }
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      make: '',
      sortBy: 'truck_number',
      sortOrder: 'asc'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold leading-7 text-secondary-900 sm:text-3xl sm:truncate">
            🚛 Trucks Management
          </h1>
          <p className="mt-1 text-sm text-secondary-500">
            Manage dump trucks, generate QR codes, and track vehicle status.
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button
            onClick={handleCreate}
            className="btn btn-primary"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add New Truck
          </button>
        </div>
      </div>

      {/* Step 5 Complete Banner */}
      <div className="bg-success-50 border border-success-200 rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <span className="text-2xl">✅</span>
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-success-800">
              Step 5: CRUD Operations - Complete!
            </h3>
            <p className="mt-1 text-sm text-success-700">
              Full CRUD operations with advanced filters, search, pagination, and table grid view.
            </p>
            <div className="mt-2 text-xs text-success-600">
              <p><strong>Features:</strong> Create, Read, Update, Delete, Search, Filter, Sort, Paginate, QR Generation</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white shadow-sm rounded-lg border border-secondary-200">
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search trucks..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              {/* Status Filter */}
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange({ status: e.target.value })}
                className="block w-full sm:w-auto px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="maintenance">Maintenance</option>
                <option value="retired">Retired</option>
              </select>

              {/* Make Filter */}
              <input
                type="text"
                placeholder="Filter by make..."
                value={filters.make}
                onChange={(e) => handleFilterChange({ make: e.target.value })}
                className="block w-full sm:w-auto px-3 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
              />

              {/* Clear Filters */}
              <button
                onClick={clearFilters}
                className="btn btn-secondary whitespace-nowrap"
              >
                Clear Filters
              </button>
            </div>
          </div>

          {/* Active Filters Display */}
          {(filters.search || filters.status || filters.make) && (
            <div className="mt-4 flex flex-wrap gap-2">
              {filters.search && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800">
                  Search: "{filters.search}"
                  <button
                    onClick={() => handleSearch('')}
                    className="ml-2 text-primary-600 hover:text-primary-800"
                  >
                    ×
                  </button>
                </span>
              )}
              {filters.status && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800">
                  Status: {filters.status}
                  <button
                    onClick={() => handleFilterChange({ status: '' })}
                    className="ml-2 text-primary-600 hover:text-primary-800"
                  >
                    ×
                  </button>
                </span>
              )}
              {filters.make && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800">
                  Make: {filters.make}
                  <button
                    onClick={() => handleFilterChange({ make: '' })}
                    className="ml-2 text-primary-600 hover:text-primary-800"
                  >
                    ×
                  </button>
                </span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Trucks Table */}
      <TrucksTable
        trucks={trucks}
        loading={loading}
        pagination={pagination}
        filters={filters}
        onPageChange={handlePageChange}
        onSort={handleSort}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onViewQR={handleViewQR}
      />

      {/* Modals */}
      {showFormModal && (
        <TruckFormModal
          truck={selectedTruck}
          onClose={() => setShowFormModal(false)}
          onSubmit={handleFormSubmit}
        />
      )}

      {showQRModal && qrData && (
        <QRCodeModal
          qrData={qrData}
          onClose={() => setShowQRModal(false)}
        />
      )}

      {showDeleteModal && selectedTruck && (
        <DeleteConfirmModal
          title="Delete Truck"
          message={`Are you sure you want to delete truck ${selectedTruck.truck_number}? This action cannot be undone.`}
          onConfirm={handleDeleteConfirm}
          onCancel={() => setShowDeleteModal(false)}
        />
      )}
    </div>
  );
};

export default TrucksManagement;