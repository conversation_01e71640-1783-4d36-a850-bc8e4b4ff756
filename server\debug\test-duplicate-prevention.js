const { query, getClient } = require('../config/database');

/**
 * Test the duplicate assignment prevention logic
 * This script simulates the scenario that was causing the duplicate key constraint error
 */

async function testDuplicateAssignmentPrevention() {
  console.log('🧪 Testing Duplicate Assignment Prevention Logic');
  console.log('='.repeat(60));

  const client = await getClient();
  
  try {
    // Step 1: Create test data
    console.log('\n📋 Step 1: Setting up test data...');
    
    // Get first active truck
    const truckResult = await client.query(`
      SELECT id, truck_number FROM dump_trucks 
      WHERE status = 'active' 
      ORDER BY id 
      LIMIT 1
    `);
    
    if (truckResult.rows.length === 0) {
      throw new Error('No active trucks found for testing');
    }
    
    const truck = truckResult.rows[0];
    console.log(`✅ Using truck: ${truck.truck_number} (ID: ${truck.id})`);
    
    // Get test locations
    const locationsResult = await client.query(`
      SELECT id, location_code, name, type 
      FROM locations 
      WHERE is_active = true 
      ORDER BY id 
      LIMIT 3
    `);
    
    if (locationsResult.rows.length < 2) {
      throw new Error('Need at least 2 active locations for testing');
    }
    
    const loadingLocation = locationsResult.rows[0];
    const unloadingLocation = locationsResult.rows[1];
    
    console.log(`✅ Loading location: ${loadingLocation.name} (ID: ${loadingLocation.id})`);
    console.log(`✅ Unloading location: ${unloadingLocation.name} (ID: ${unloadingLocation.id})`);
    
    // Clean up any existing assignments for this truck today
    console.log('\n🧹 Step 2: Cleaning up existing test assignments...');
    const deleteResult = await client.query(`
      DELETE FROM assignments 
      WHERE truck_id = $1 
        AND assigned_date = CURRENT_DATE
        AND assignment_code LIKE '%TEST%'
    `, [truck.id]);
    
    console.log(`✅ Cleaned up ${deleteResult.rowCount} existing test assignments`);
    
    // Step 3: Test the exact duplicate prevention
    console.log('\n🔍 Step 3: Testing exact duplicate prevention...');
    
    const testDate = new Date().toISOString().split('T')[0];
    
    // First, check if exact assignment already exists
    const existingCheck = await client.query(`
      SELECT id, assignment_code, status FROM assignments 
      WHERE truck_id = $1 
        AND loading_location_id = $2 
        AND unloading_location_id = $3
        AND assigned_date = $4
        AND status IN ('assigned', 'in_progress')
    `, [truck.id, loadingLocation.id, unloadingLocation.id, testDate]);
    
    console.log(`🔍 Existing assignment check: Found ${existingCheck.rows.length} assignments`);
    
    if (existingCheck.rows.length > 0) {
      console.log(`✅ Assignment already exists: ${existingCheck.rows[0].assignment_code} (Status: ${existingCheck.rows[0].status})`);
      console.log('🎯 This confirms the duplicate prevention logic is working - no duplicate would be created');
    } else {
      console.log('📝 No existing assignment found - would create new one');
      
      // Create a test assignment to verify the constraint works
      const assignment_code = `ASG-TEST-${Date.now()}`;
      const newAssignmentResult = await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, 
          loading_location_id, unloading_location_id, 
          assigned_date, status, priority, 
          expected_loads_per_day, notes, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING id, assignment_code
      `, [
        assignment_code,
        truck.id,
        1, // Use first driver
        loadingLocation.id,
        unloadingLocation.id,
        testDate,
        'assigned',
        'normal',
        1,
        JSON.stringify({ type: 'test_assignment', test_run: true }),
        new Date(),
        new Date()
      ]);
      
      console.log(`✅ Created test assignment: ${newAssignmentResult.rows[0].assignment_code}`);
      
      // Now try to create a duplicate - this should be prevented by our logic
      console.log('\n🚫 Step 4: Attempting to create duplicate (should be prevented)...');
      
      const duplicateCheck = await client.query(`
        SELECT id, assignment_code, status FROM assignments 
        WHERE truck_id = $1 
          AND loading_location_id = $2 
          AND unloading_location_id = $3
          AND assigned_date = $4
          AND status IN ('assigned', 'in_progress')
      `, [truck.id, loadingLocation.id, unloadingLocation.id, testDate]);
      
      if (duplicateCheck.rows.length > 0) {
        console.log(`✅ PREVENTION WORKING: Found existing assignment ${duplicateCheck.rows[0].assignment_code}`);
        console.log('🎯 The scanner logic would use this existing assignment instead of creating a duplicate');
      } else {
        console.log('❌ ERROR: No assignment found - duplicate prevention might not be working');
      }
      
      // Clean up test assignment
      await client.query(`
        DELETE FROM assignments 
        WHERE assignment_code = $1
      `, [assignment_code]);
      
      console.log('🧹 Cleaned up test assignment');
    }
    
    // Step 5: Test the database constraint itself
    console.log('\n🔒 Step 5: Testing database constraint directly...');
    
    const testAssignment1Code = `ASG-TEST-1-${Date.now()}`;
    const testAssignment2Code = `ASG-TEST-2-${Date.now()}`;
    
    // Create first assignment
    await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id, 
        loading_location_id, unloading_location_id, 
        assigned_date, status, priority, 
        expected_loads_per_day, notes, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
    `, [
      testAssignment1Code,
      truck.id,
      1,
      loadingLocation.id,
      unloadingLocation.id,
      testDate,
      'assigned',
      'normal',
      1,
      JSON.stringify({ type: 'constraint_test', test_run: true }),
      new Date(),
      new Date()
    ]);
    
    console.log(`✅ Created first test assignment: ${testAssignment1Code}`);
    
    // Attempt to create duplicate - this should fail with constraint error
    try {
      await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, 
          loading_location_id, unloading_location_id, 
          assigned_date, status, priority, 
          expected_loads_per_day, notes, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      `, [
        testAssignment2Code,
        truck.id,
        1,
        loadingLocation.id,
        unloadingLocation.id,
        testDate,
        'assigned',
        'normal',
        1,
        JSON.stringify({ type: 'constraint_test_duplicate', test_run: true }),
        new Date(),
        new Date()
      ]);
      
      console.log('❌ ERROR: Duplicate assignment was created - database constraint is not working!');
    } catch (error) {
      if (error.message.includes('idx_assignments_exact_duplicate')) {
        console.log('✅ CONSTRAINT WORKING: Database prevented duplicate assignment creation');
        console.log(`   Error: ${error.message}`);
      } else {
        console.log(`❌ UNEXPECTED ERROR: ${error.message}`);
      }
    }
    
    // Clean up test assignments
    await client.query(`
      DELETE FROM assignments 
      WHERE assignment_code IN ($1, $2)
    `, [testAssignment1Code, testAssignment2Code]);
    
    console.log('🧹 Cleaned up constraint test assignments');
    
    console.log('\n🎉 Test completed successfully!');
    console.log('✅ The duplicate assignment prevention logic has been verified');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  } finally {
    client.release();
  }
}

// Run the test
if (require.main === module) {
  testDuplicateAssignmentPrevention()
    .then(() => {
      console.log('\n✅ All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testDuplicateAssignmentPrevention };
