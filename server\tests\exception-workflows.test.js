/**
 * Comprehensive Exception Workflow Tests
 * 
 * Tests the refactored exception handling system including:
 * - Route deviation exceptions
 * - Unassigned trip exceptions
 * - Exception approval workflows
 * - Performance requirements (<300ms)
 * - Data integrity and consistency
 */

const request = require('supertest');
const { app } = require('../server');
const { getClient } = require('../config/database');
const { exceptionFactory } = require('../utils/ExceptionFactory');
const { assignmentValidator } = require('../utils/AssignmentValidator');

describe('Exception Workflow Tests', () => {
  let authToken;
  let testTruck;
  let testLocation1;
  let testLocation2;
  let testAssignment;
  let testUser;

  beforeAll(async () => {
    // Setup test data
    const client = await getClient();
    
    try {
      await client.query('BEGIN');

      // Use existing test user or create one
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('testpassword', 10);

      const userResult = await client.query(`
        INSERT INTO users (username, email, password_hash, full_name, role, is_active)
        VALUES ('testadmin2', '<EMAIL>', $1, 'Test Admin 2', 'admin', true)
        ON CONFLICT (username) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
        RETURNING *
      `, [hashedPassword]);
      testUser = userResult.rows[0];

      // Create test truck
      const truckResult = await client.query(`
        INSERT INTO dump_trucks (truck_number, license_plate, model, capacity_tons, status, qr_code_data)
        VALUES ('TEST-001', 'TEST123', 'Test Model', 10, 'active', '{"type":"truck","id":"TEST-001"}')
        RETURNING *
      `);
      testTruck = truckResult.rows[0];

      // Create test locations
      const location1Result = await client.query(`
        INSERT INTO locations (location_code, name, type, coordinates, status, qr_code_data)
        VALUES ('LOC-TEST1', 'Test Location 1', 'loading', '40.7128,-74.0060', 'active', '{"type":"location","id":"LOC-TEST1"}')
        RETURNING *
      `);
      testLocation1 = location1Result.rows[0];

      const location2Result = await client.query(`
        INSERT INTO locations (location_code, name, type, coordinates, status, qr_code_data)
        VALUES ('LOC-TEST2', 'Test Location 2', 'unloading', '40.7589,-73.9851', 'active', '{"type":"location","id":"LOC-TEST2"}')
        RETURNING *
      `);
      testLocation2 = location2Result.rows[0];

      // Create test driver
      const driverResult = await client.query(`
        INSERT INTO drivers (employee_id, full_name, license_number, license_expiry, phone, email, hire_date, status)
        VALUES ('DRV-TEST', 'Test Driver', 'LIC123', '2026-12-31', '555-0123', '<EMAIL>', CURRENT_DATE, 'active')
        RETURNING *
      `);
      const testDriver = driverResult.rows[0];

      // Create test assignment
      const assignmentResult = await client.query(`
        INSERT INTO assignments (assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id, assigned_date, status)
        VALUES ('ASG-TEST-001', $1, $2, $3, $4, CURRENT_DATE, 'assigned')
        RETURNING *
      `, [testTruck.id, testDriver.id, testLocation1.id, testLocation2.id]);
      testAssignment = assignmentResult.rows[0];

      await client.query('COMMIT');

      // Get auth token
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testadmin2',
          password: 'testpassword'
        });
      
      authToken = loginResponse.body.token;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  });

  afterAll(async () => {
    // Cleanup test data
    const client = await getClient();
    
    try {
      await client.query('BEGIN');
      
      // Delete in reverse order of dependencies
      await client.query('DELETE FROM approvals WHERE trip_log_id IN (SELECT id FROM trip_logs WHERE assignment_id = $1)', [testAssignment.id]);
      await client.query('DELETE FROM trip_logs WHERE assignment_id = $1', [testAssignment.id]);
      await client.query('DELETE FROM assignments WHERE id = $1', [testAssignment.id]);
      await client.query('DELETE FROM locations WHERE id IN ($1, $2)', [testLocation1.id, testLocation2.id]);
      await client.query('DELETE FROM dump_trucks WHERE id = $1', [testTruck.id]);
      await client.query('DELETE FROM drivers WHERE employee_id = $1', ['DRV-TEST']);
      await client.query('DELETE FROM users WHERE id = $1', [testUser.id]);
      
      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Cleanup error:', error);
    } finally {
      client.release();
    }
  });

  describe('Route Deviation Exception', () => {
    test('should create route deviation exception when truck scans at wrong location', async () => {
      const startTime = Date.now();
      
      const client = await getClient();
      
      try {
        const result = await exceptionFactory.createRouteDeviationException({
          truck: testTruck,
          expectedLocation: testLocation1,
          actualLocation: testLocation2,
          assignment: testAssignment,
          userId: testUser.id,
          client
        });

        const processingTime = Date.now() - startTime;

        // Verify performance requirement
        expect(processingTime).toBeLessThan(300);

        // Verify exception creation
        expect(result.success).toBe(true);
        expect(result.trip).toBeDefined();
        expect(result.approval).toBeDefined();
        expect(result.message).toContain('Route deviation detected');
        expect(result.next_step).toBe('await_approval');

        // Verify trip status
        expect(result.trip.status).toBe('exception_pending');
        expect(result.trip.is_exception).toBe(true);
        expect(result.trip.actual_loading_location_id).toBe(testLocation2.id);

        // Verify approval creation
        expect(result.approval.exception_type).toBe('route_deviation');
        expect(result.approval.status).toBe('pending');
        expect(result.approval.severity).toBe('medium');

      } finally {
        client.release();
      }
    });

    test('should handle route deviation approval workflow', async () => {
      const startTime = Date.now();

      // First create a route deviation
      const client = await getClient();
      
      try {
        await client.query('BEGIN');

        const exceptionResult = await exceptionFactory.createRouteDeviationException({
          truck: testTruck,
          expectedLocation: testLocation1,
          actualLocation: testLocation2,
          assignment: testAssignment,
          userId: testUser.id,
          client
        });

        // Test approval via API
        const approvalResponse = await request(app)
          .put(`/api/approvals/${exceptionResult.approval.id}`)
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            decision: 'approved',
            notes: 'Test approval'
          });

        const processingTime = Date.now() - startTime;

        // Verify performance requirement
        expect(processingTime).toBeLessThan(300);

        // Verify approval response
        expect(approvalResponse.status).toBe(200);
        expect(approvalResponse.body.success).toBe(true);
        expect(approvalResponse.body.decision).toBe('approved');

        // Verify trip status updated
        const tripResult = await client.query('SELECT * FROM trip_logs WHERE id = $1', [exceptionResult.trip.id]);
        expect(tripResult.rows[0].status).toBe('loading_start');
        expect(tripResult.rows[0].exception_approved_at).toBeDefined();

        await client.query('COMMIT');

      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      } finally {
        client.release();
      }
    });
  });

  describe('Unassigned Trip Exception', () => {
    test('should create unassigned trip exception when truck has no assignment', async () => {
      const startTime = Date.now();
      
      const client = await getClient();
      
      try {
        // Create a truck without assignment
        const truckResult = await client.query(`
          INSERT INTO dump_trucks (truck_number, license_plate, model, capacity_tons, status, qr_code_data)
          VALUES ('TEST-002', 'TEST456', 'Test Model 2', 15, 'active', '{"type":"truck","id":"TEST-002"}')
          RETURNING *
        `);
        const unassignedTruck = truckResult.rows[0];

        const result = await exceptionFactory.createUnassignedTripException({
          truck: unassignedTruck,
          location: testLocation1,
          userId: testUser.id,
          client
        });

        const processingTime = Date.now() - startTime;

        // Verify performance requirement
        expect(processingTime).toBeLessThan(300);

        // Verify exception creation
        expect(result.success).toBe(true);
        expect(result.trip).toBeDefined();
        expect(result.approval).toBeDefined();
        expect(result.tempAssignment).toBeDefined();
        expect(result.message).toContain('Unassigned trip detected');

        // Verify temporary assignment
        expect(result.tempAssignment.status).toBe('pending_approval');
        expect(result.tempAssignment.loading_location_id).toBe(testLocation1.id);

        // Cleanup
        await client.query('DELETE FROM dump_trucks WHERE id = $1', [unassignedTruck.id]);

      } finally {
        client.release();
      }
    });
  });

  describe('Assignment Validation', () => {
    test('should validate truck assignment correctly', async () => {
      const startTime = Date.now();

      const result = await assignmentValidator.validateTruckAssignment({
        truckNumber: testTruck.truck_number,
        locationId: testLocation1.id
      });

      const processingTime = Date.now() - startTime;

      // Verify performance requirement
      expect(processingTime).toBeLessThan(100);

      // Verify validation result
      expect(result.isValid).toBe(true);
      expect(result.result).toBe('valid');
      expect(result.truck).toBeDefined();
      expect(result.assignment).toBeDefined();
      expect(result.location).toBeDefined();
    });

    test('should detect wrong location for route deviation', async () => {
      const result = await assignmentValidator.validateTruckAssignment({
        truckNumber: testTruck.truck_number,
        locationId: testLocation2.id // Wrong location (unloading instead of loading)
      });

      expect(result.isValid).toBe(false);
      expect(result.result).toBe('wrong_location');
      expect(result.expectedLocation).toBeDefined();
    });
  });

  describe('Exception Statistics API', () => {
    test('should return exception statistics', async () => {
      const response = await request(app)
        .get('/api/trips/stats/exceptions')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(typeof response.body.data.total_exceptions).toBe('number');
      expect(typeof response.body.data.pending_exceptions).toBe('number');
      expect(typeof response.body.data.approved_exceptions).toBe('number');
      expect(typeof response.body.data.rejected_exceptions).toBe('number');
    });
  });

  describe('Performance Requirements', () => {
    test('all exception operations should complete within 300ms', async () => {
      const operations = [
        () => assignmentValidator.validateTruckAssignment({
          truckNumber: testTruck.truck_number,
          locationId: testLocation1.id
        }),
        () => exceptionFactory.createRouteDeviationException({
          truck: testTruck,
          expectedLocation: testLocation1,
          actualLocation: testLocation2,
          assignment: testAssignment,
          userId: testUser.id
        })
      ];

      for (const operation of operations) {
        const startTime = Date.now();
        await operation();
        const processingTime = Date.now() - startTime;
        expect(processingTime).toBeLessThan(300);
      }
    });
  });
});
