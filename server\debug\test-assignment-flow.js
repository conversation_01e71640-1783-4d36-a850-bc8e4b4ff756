/**
 * Test script to validate the assignment flow logic in the QR Trip System
 * Focuses on ensuring assignments are created by admins and scanner only uses existing assignments
 */
const { Pool } = require('pg');
const config = require('../config/database');

// Initialize DB connection
const pool = new Pool(config);

async function runTest() {
  const client = await pool.connect();
  
  try {
    console.log('🚛 Testing Assignment Flow Logic');
    console.log('=================================');
    
    // Step 1: Check if system correctly looks up assignments for a truck at a specific location
    console.log('\n1️⃣ TEST: Assignment Lookup');
    
    const truck = await getRandomActiveTruck(client);
    if (!truck) {
      throw new Error('No active trucks found for testing');
    }
    console.log(`Using truck ${truck.truck_number} for testing`);
    
    const location = await getRandomActiveLocation(client, 'loading');
    if (!location) {
      throw new Error('No loading locations found for testing');
    }
    console.log(`Using location ${location.name} for testing`);
    
    // Check if there's an existing assignment for this truck at this location
    const assignment = await getAssignment(client, truck.id, location.id);
    console.log('Assignment check result:');
    console.log(assignment ? 
      `✅ Found assignment: ID ${assignment.id} (${assignment.loading_location} → ${assignment.unloading_location})` : 
      '❌ No assignment found for this truck at this location'
    );
    
    // Step 2: Simulate what happens in scanner.js when a truck is scanned
    console.log('\n2️⃣ TEST: Scanner Flow Logic');
    
    // Create mock QR data similar to what would be passed to processTruckScan
    const qrData = { id: truck.truck_number, type: 'truck' };
    const locationData = { id: location.location_code, type: 'location' };
    
    // First, get current trip and assignment (similar to getCurrentTripAndAssignment in scanner.js)
    const tripData = await getCurrentTripAndAssignment(client, truck.id);
    
    console.log('Current trip data:');
    console.log(tripData.trip ? 
      `✅ Active trip found: ID ${tripData.trip.id}, status: ${tripData.trip.status}` : 
      '❌ No active trip found'
    );
    
    console.log('Current assignment data:');
    if (tripData.assignment) {
      console.log(`✅ Assignment found: ID ${tripData.assignment.id}`);
      console.log(`   Loading: ${tripData.assignment.loading_location_name}`);
      console.log(`   Unloading: ${tripData.assignment.unloading_location_name}`);
    } else {
      console.log('❌ No assignment found from trip lookup');
    }
    
    // Try scanner flow logic - check for assignment at this location
    console.log('\n3️⃣ TEST: Scanner Assignment Location Lookup');
    
    // This is similar to the code in processTruckScan where it checks for assignment at location
    const assignmentAtThisLocation = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.loading_location_id = $2
        AND a.assigned_date = CURRENT_DATE
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [truck.truck_number, location.id]);
    
    console.log('Assignment at scanned location:');
    if (assignmentAtThisLocation.rows.length > 0) {
      const locationAssignment = assignmentAtThisLocation.rows[0];
      console.log(`✅ Found assignment at scanned location: ID ${locationAssignment.id}`);
      console.log(`   Loading: ${locationAssignment.loading_location}`);
      console.log(`   Unloading: ${locationAssignment.unloading_location}`);
    } else {
      console.log('❌ No assignment found at scanned location');
    }
    
    // Step 3: Check any assignment for this truck today (would trigger route deviation)
    console.log('\n4️⃣ TEST: Any Assignment Check (Route Deviation)');
    
    const anyAssignmentToday = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.assigned_date = CURRENT_DATE
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [truck.truck_number]);
    
    console.log('Any assignment for truck today:');
    if (anyAssignmentToday.rows.length > 0) {
      const todayAssignment = anyAssignmentToday.rows[0];
      console.log(`✅ Found assignment for today: ID ${todayAssignment.id}`);
      console.log(`   Loading: ${todayAssignment.loading_location}`);
      console.log(`   Unloading: ${todayAssignment.unloading_location}`);
      
      // Is this a route deviation?
      if (todayAssignment.loading_location_id !== location.id) {
        console.log(`❗️ ROUTE DEVIATION DETECTED: Truck is at ${location.name} but assigned to ${todayAssignment.loading_location}`);
      } else {
        console.log('✅ Truck is at correct assigned location');
      }
    } else {
      console.log('❌ No assignment found for truck today');
    }
    
    // Step 4: Test for duplicate constraint
    console.log('\n5️⃣ TEST: Duplicate Assignment Prevention');
    
    // Try to create a duplicate assignment to see if constraint works
    if (assignment) {
      console.log(`Testing duplicate prevention for assignment ID ${assignment.id}`);
      
      try {
        // Try creating an identical assignment
        const duplicateResult = await client.query(`
          INSERT INTO assignments (
            assignment_code, truck_id, driver_id, loading_location_id, 
            unloading_location_id, assigned_date, status
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING id
        `, [
          'ASG-' + Date.now(), // New code to avoid code duplicate constraint
          assignment.truck_id,
          assignment.driver_id,
          assignment.loading_location_id,
          assignment.unloading_location_id,
          assignment.assigned_date,
          'assigned'
        ]);
        
        console.log('❌ FAILED: Duplicate assignment was created! This should not happen.');
        console.log(`New duplicate assignment ID: ${duplicateResult.rows[0].id}`);
        
        // Clean up the duplicate
        await client.query('DELETE FROM assignments WHERE id = $1', [duplicateResult.rows[0].id]);
      } catch (error) {
        if (error.code === '23505') { // Unique constraint violation
          console.log('✅ SUCCESS: Duplicate prevention constraint blocked creation of identical assignment');
          console.log(`Error: ${error.detail}`);
        } else {
          console.log(`❌ ERROR: Unexpected error during duplicate test: ${error.message}`);
        }
      }
    } else {
      console.log('Skipping duplicate test as no existing assignment was found');
    }
    
    // Summary
    console.log('\n🔍 SUMMARY');
    console.log('=================================');
    if (assignmentAtThisLocation.rows.length > 0) {
      console.log('✅ System correctly looks up existing assignments for truck at scanned location');
    } else {
      console.log('⚠️ No assignment found for truck at scanned location');
    }
    
    if (anyAssignmentToday.rows.length > 0 && anyAssignmentToday.rows[0].loading_location_id !== location.id) {
      console.log('✅ System correctly detects route deviation when truck is at wrong location');
    } else if (anyAssignmentToday.rows.length > 0) {
      console.log('✅ System correctly finds truck\'s assignment for today');
    } else {
      console.log('⚠️ No assignment found for truck today');
    }
    
    console.log('=================================');
    console.log('Test completed!');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Helper functions
async function getRandomActiveTruck(client) {
  const result = await client.query(`
    SELECT id, truck_number, license_plate 
    FROM dump_trucks 
    WHERE status = 'active' 
    ORDER BY RANDOM() 
    LIMIT 1
  `);
  return result.rows.length ? result.rows[0] : null;
}

async function getRandomActiveLocation(client, type) {
  const result = await client.query(`
    SELECT id, location_code, name, type 
    FROM locations 
    WHERE status = 'active' 
    AND type = $1
    ORDER BY RANDOM() 
    LIMIT 1
  `, [type]);
  return result.rows.length ? result.rows[0] : null;
}

async function getAssignment(client, truckId, locationId) {
  const result = await client.query(`
    SELECT a.id, a.assignment_code, a.truck_id, a.driver_id, 
           a.loading_location_id, a.unloading_location_id, a.assigned_date,
           ll.name AS loading_location, ul.name AS unloading_location
    FROM assignments a
    JOIN locations ll ON a.loading_location_id = ll.id
    JOIN locations ul ON a.unloading_location_id = ul.id
    WHERE a.truck_id = $1 
      AND a.loading_location_id = $2
      AND a.assigned_date = CURRENT_DATE
      AND a.status IN ('assigned', 'in_progress')
  `, [truckId, locationId]);
  return result.rows.length ? result.rows[0] : null;
}

async function getCurrentTripAndAssignment(client, truckId) {
  // This replicates the logic in the scanner.js file
  const assignmentResult = await client.query(`
    SELECT 
      a.id, a.assignment_code, a.truck_id, a.driver_id, 
      a.loading_location_id, a.unloading_location_id, a.assigned_date, a.status,
      a.priority, a.expected_loads_per_day, a.notes,
      dt.truck_number, dt.status as truck_status,
      ll.name as loading_location_name, ul.name as unloading_location_name,
      d.full_name as driver_name
    FROM assignments a
    JOIN dump_trucks dt ON a.truck_id = dt.id
    LEFT JOIN locations ll ON a.loading_location_id = ll.id
    LEFT JOIN locations ul ON a.unloading_location_id = ul.id
    LEFT JOIN drivers d ON a.driver_id = d.id
    WHERE dt.id = $1
      AND a.assigned_date = CURRENT_DATE
      AND a.status IN ('assigned', 'in_progress')
    ORDER BY a.created_at DESC
    LIMIT 1
  `, [truckId]);
  
  const assignment = assignmentResult.rows.length > 0 ? assignmentResult.rows[0] : null;
  
  // If no assignment, return early
  if (!assignment) {
    return { assignment: null, trip: null };
  }
  
  // Get active trip for this assignment
  const tripResult = await client.query(`
    SELECT *
    FROM trip_logs
    WHERE assignment_id = $1
      AND status NOT IN ('completed', 'cancelled')
    ORDER BY created_at DESC
    LIMIT 1
  `, [assignment.id]);
  
  const trip = tripResult.rows.length > 0 ? tripResult.rows[0] : null;
  
  return { assignment, trip };
}

// Run the test
runTest().catch(err => {
  console.error('Test execution failed:', err);
  process.exit(1);
});
