# 🚀 Production Deployment Guide - Hauling QR Trip System

**Version: 2.0 Final**  
**Status: Production Ready**  
**Last Updated: June 25, 2025**

---

## 📋 Quick Deployment Checklist

- [ ] **Database Setup**: PostgreSQL 12+ with required extensions
- [ ] **Migration Execution**: All schema updates applied
- [ ] **Environment Configuration**: Production variables set
- [ ] **Dependency Installation**: All packages installed
- [ ] **Health Checks**: System validation completed
- [ ] **Process Management**: PM2 or equivalent configured
- [ ] **Monitoring Setup**: Logging and alerts configured
- [ ] **Backup Strategy**: Automated backups enabled

---

## 🗄️ Database Setup

### Prerequisites
```bash
# PostgreSQL Version
PostgreSQL 12.0 or higher

# Required Extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;  # For text search indexing
```

### Database Creation
```sql
-- Create database and user
CREATE DATABASE hauling_qr_system;
CREATE USER hauling_app WITH PASSWORD 'your_secure_password';
GRA<PERSON> ALL PRIVILEGES ON DATABASE hauling_qr_system TO hauling_app;

-- Connect to the database
\c hauling_qr_system

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO hauling_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hauling_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hauling_app;
```

### Schema Initialization
```bash
# Navigate to database directory
cd database

# Initialize database with base schema
psql -d hauling_qr_system -f init.sql

# Run all migrations
node run-migration.js
```

---

## ⚙️ Server Configuration

### Environment Setup
```bash
# Create production environment file
cp .env.example .env

# Edit environment variables
nano .env
```

### Environment Variables
```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=hauling_app
DB_PASSWORD=your_secure_password

# Application Settings
NODE_ENV=production
PORT=3001
JWT_SECRET=your_jwt_secret_min_32_chars

# Connection Pool Settings
DB_POOL_MAX=25
DB_POOL_MIN=5

# Security Settings
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=/var/log/hauling-qr/app.log
```

### Dependencies Installation
```bash
# Server dependencies
cd server
npm ci --production

# Client dependencies (if serving from same server)
cd ../client
npm ci --production
npm run build
```

---

## 🔄 Process Management

### PM2 Configuration
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'hauling-qr-server',
    script: './server.js',
    cwd: './server',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    log_file: '/var/log/hauling-qr/combined.log',
    out_file: '/var/log/hauling-qr/out.log',
    error_file: '/var/log/hauling-qr/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

### Process Commands
```bash
# Start application
pm2 start ecosystem.config.js --env production

# Monitor processes
pm2 monit

# View logs
pm2 logs hauling-qr-server

# Restart application
pm2 restart hauling-qr-server

# Stop application
pm2 stop hauling-qr-server

# Save PM2 configuration
pm2 save
pm2 startup
```

---

## 🌐 Web Server Configuration

### Nginx Configuration
```nginx
# /etc/nginx/sites-available/hauling-qr
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Client (React App)
    location / {
        root /var/www/hauling-qr/build;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API Server (Node.js)
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket Support
    location /socket.io/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Security - Block access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(log|conf)$ {
        deny all;
    }
}
```

### Enable Nginx Site
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/hauling-qr /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

---

## 📊 Health Monitoring

### Application Health Checks
```bash
# Health check endpoint
curl -f http://localhost:3001/api/health || exit 1

# Database health check
curl -f http://localhost:3001/api/health/database || exit 1

# Performance metrics
curl -s http://localhost:3001/api/health/metrics | jq .
```

### Monitoring Script
```bash
#!/bin/bash
# /opt/hauling-qr/health-check.sh

LOG_FILE="/var/log/hauling-qr/health-check.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Check application health
if curl -f -s http://localhost:3001/api/health > /dev/null; then
    echo "$DATE - Application: HEALTHY" >> $LOG_FILE
else
    echo "$DATE - Application: UNHEALTHY - Restarting..." >> $LOG_FILE
    pm2 restart hauling-qr-server
    # Send alert notification
    /opt/hauling-qr/send-alert.sh "Application health check failed - restarted"
fi

# Check database connectivity
if curl -f -s http://localhost:3001/api/health/database > /dev/null; then
    echo "$DATE - Database: HEALTHY" >> $LOG_FILE
else
    echo "$DATE - Database: UNHEALTHY - Check connection" >> $LOG_FILE
    # Send critical alert
    /opt/hauling-qr/send-alert.sh "Database connection failed - immediate attention required"
fi
```

### Cron Job Setup
```bash
# Add to crontab (every 5 minutes)
*/5 * * * * /opt/hauling-qr/health-check.sh

# Daily log rotation
0 0 * * * /opt/hauling-qr/rotate-logs.sh
```

---

## 🔄 Backup Strategy

### Database Backup Script
```bash
#!/bin/bash
# /opt/hauling-qr/backup-database.sh

BACKUP_DIR="/var/backups/hauling-qr"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="hauling_qr_system"
BACKUP_FILE="$BACKUP_DIR/hauling_qr_backup_$DATE.sql"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create database backup
pg_dump $DB_NAME > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_FILE.gz"
```

### Automated Backup Schedule
```bash
# Add to crontab
# Daily backup at 2 AM
0 2 * * * /opt/hauling-qr/backup-database.sh

# Weekly full system backup
0 3 * * 0 /opt/hauling-qr/backup-full-system.sh
```

---

## 🔍 Logging Configuration

### Log Directory Structure
```bash
/var/log/hauling-qr/
├── app.log              # Application logs
├── error.log            # Error logs
├── access.log           # Access logs
├── database.log         # Database operation logs
├── health-check.log     # Health monitoring logs
└── audit.log            # Security audit logs
```

### Logrotate Configuration
```bash
# /etc/logrotate.d/hauling-qr
/var/log/hauling-qr/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        pm2 reloadLogs
    endscript
}
```

---

## 🔐 Security Hardening

### Firewall Configuration
```bash
# UFW firewall rules
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow from localhost to any port 3001
sudo ufw deny 3001
sudo ufw deny 5432
sudo ufw enable
```

### SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal cron job
0 12 * * * /usr/bin/certbot renew --quiet
```

### Database Security
```sql
-- Restrict database access
REVOKE ALL ON SCHEMA public FROM public;
GRANT USAGE ON SCHEMA public TO hauling_app;

-- Limit connection attempts
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;
ALTER SYSTEM SET log_statement = 'mod';
```

---

## 📈 Performance Optimization

### Database Optimization
```sql
-- Update statistics
ANALYZE;

-- Vacuum and reindex
VACUUM ANALYZE assignments;
VACUUM ANALYZE trip_logs;
REINDEX DATABASE hauling_qr_system;
```

### Connection Pool Tuning
```bash
# In .env file
DB_POOL_MAX=25        # Maximum connections
DB_POOL_MIN=5         # Minimum connections
DB_POOL_IDLE=60000    # Idle timeout (1 minute)
DB_POOL_ACQUIRE=30000 # Acquire timeout (30 seconds)
```

### Node.js Optimization
```bash
# PM2 cluster mode for multi-core utilization
pm2 start ecosystem.config.js --env production

# Memory optimization
export NODE_OPTIONS="--max-old-space-size=1024"
```

---

## 🚀 Deployment Execution

### Step-by-Step Deployment
```bash
# 1. System preparation
sudo apt update && sudo apt upgrade -y
sudo apt install postgresql nginx nodejs npm -y

# 2. Application deployment
git clone https://github.com/your-repo/hauling-qr-trip-system.git
cd hauling-qr-trip-system

# 3. Database setup
cd database
sudo -u postgres createdb hauling_qr_system
sudo -u postgres psql -d hauling_qr_system -f init.sql
node run-migration.js

# 4. Server configuration
cd ../server
npm ci --production
cp .env.example .env
# Edit .env with production values

# 5. Client build
cd ../client
npm ci --production
npm run build
sudo cp -r build/* /var/www/hauling-qr/

# 6. Process management
npm install -g pm2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup

# 7. Web server configuration
sudo cp nginx.conf /etc/nginx/sites-available/hauling-qr
sudo ln -s /etc/nginx/sites-available/hauling-qr /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx

# 8. SSL setup
sudo certbot --nginx -d yourdomain.com

# 9. Final verification
curl -f https://yourdomain.com/api/health
```

---

## ✅ Post-Deployment Verification

### System Health Verification
```bash
# 1. Application health
curl -f https://yourdomain.com/api/health

# 2. Database connectivity
curl -f https://yourdomain.com/api/health/database

# 3. WebSocket functionality
# Use browser dev tools to test socket.io connection

# 4. QR scanner functionality
# Test through web interface

# 5. Exception workflow
# Create test assignment and simulate route deviation
```

### Performance Testing
```bash
# Load testing with Apache Bench
ab -n 1000 -c 10 https://yourdomain.com/api/health

# Database performance
psql -d hauling_qr_system -c "SELECT * FROM get_database_performance_metrics();"
```

---

## 🔧 Troubleshooting Guide

### Common Issues

#### Application Won't Start
```bash
# Check logs
pm2 logs hauling-qr-server

# Check environment variables
pm2 env hauling-qr-server

# Restart with debug
pm2 restart hauling-qr-server --env production --log-type json
```

#### Database Connection Issues
```bash
# Test database connection
psql -h localhost -p 5432 -U hauling_app -d hauling_qr_system

# Check PostgreSQL status
sudo systemctl status postgresql

# Check database logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

#### Performance Issues
```bash
# Check server resources
htop
df -h
iostat 1

# Database query analysis
psql -d hauling_qr_system -c "SELECT * FROM pg_stat_activity;"

# Application metrics
curl -s https://yourdomain.com/api/health/metrics | jq .
```

---

## 📞 Support & Maintenance

### Regular Maintenance Tasks
- **Daily**: Health check monitoring, log review
- **Weekly**: Performance metrics analysis, backup verification
- **Monthly**: Security updates, database optimization
- **Quarterly**: Full system review, capacity planning

### Emergency Contacts
- **System Administrator**: <EMAIL>
- **Database Administrator**: <EMAIL>
- **Development Team**: <EMAIL>

### Support Documentation
- API Documentation: `/docs/api`
- User Manual: `/docs/user-guide`
- Admin Guide: `/docs/admin-guide`

---

**🎯 Deployment Status: ✅ PRODUCTION READY**

*This guide provides complete instructions for deploying the Hauling QR Trip System in a production environment with enterprise-grade security, monitoring, and performance optimization.*
