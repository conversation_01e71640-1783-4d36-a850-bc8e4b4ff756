/**
 * Enhanced Exception Flow Database Validation Script
 * 
 * This script validates the database schema and constraints
 * to ensure the exception flow works correctly with PostgreSQL
 */

const { getClient } = require('../config/database');

async function validateDatabaseSchema() {
  const client = await getClient();
  
  try {
    console.log('🔍 Validating Database Schema for Exception Flow');
    console.log('='.repeat(60));

    // 1. Check trip_logs table structure
    console.log('\n1️⃣ Validating trip_logs table...');
    
    const tripLogsColumns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'trip_logs'
      ORDER BY ordinal_position
    `);

    const requiredColumns = [
      'id', 'assignment_id', 'trip_number', 'status', 
      'is_exception', 'exception_reason', 'exception_approved_by', 
      'exception_approved_at', 'notes'
    ];

    console.log('   📋 Required columns check:');
    for (const col of requiredColumns) {
      const exists = tripLogsColumns.rows.find(row => row.column_name === col);
      console.log(`   ${exists ? '✅' : '❌'} ${col}: ${exists ? exists.data_type : 'MISSING'}`);
      if (!exists) {
        throw new Error(`Required column ${col} missing from trip_logs table`);
      }
    }

    // 2. Check approvals table structure
    console.log('\n2️⃣ Validating approvals table...');
    
    const approvalsColumns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'approvals'
      ORDER BY ordinal_position
    `);

    const requiredApprovalColumns = [
      'id', 'trip_log_id', 'exception_type', 'exception_description',
      'severity', 'reported_by', 'reviewed_by', 'reviewed_at', 'status'
    ];

    console.log('   📋 Required approval columns check:');
    for (const col of requiredApprovalColumns) {
      const exists = approvalsColumns.rows.find(row => row.column_name === col);
      console.log(`   ${exists ? '✅' : '❌'} ${col}: ${exists ? exists.data_type : 'MISSING'}`);
      if (!exists) {
        throw new Error(`Required column ${col} missing from approvals table`);
      }
    }

    // 3. Check enum values
    console.log('\n3️⃣ Validating enum types...');
    
    // Check trip_status enum
    const tripStatusEnum = await client.query(`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'trip_status'
      )
      ORDER BY enumsortorder
    `);

    const requiredTripStatuses = [
      'assigned', 'loading_start', 'loading_end', 
      'unloading_start', 'unloading_end', 'trip_completed',
      'exception_pending', 'cancelled'
    ];

    console.log('   📋 Trip status enum values:');
    for (const status of requiredTripStatuses) {
      const exists = tripStatusEnum.rows.find(row => row.enumlabel === status);
      console.log(`   ${exists ? '✅' : '❌'} ${status}`);
      if (!exists) {
        console.warn(`   ⚠️  Missing trip status: ${status}`);
      }
    }

    // Check approval_status enum  
    const approvalStatusEnum = await client.query(`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'approval_status'
      )
      ORDER BY enumsortorder
    `);

    const requiredApprovalStatuses = ['pending', 'approved', 'rejected'];

    console.log('   📋 Approval status enum values:');
    for (const status of requiredApprovalStatuses) {
      const exists = approvalStatusEnum.rows.find(row => row.enumlabel === status);
      console.log(`   ${exists ? '✅' : '❌'} ${status}`);
      if (!exists) {
        console.warn(`   ⚠️  Missing approval status: ${status}`);
      }
    }

    // 4. Check foreign key constraints
    console.log('\n4️⃣ Validating foreign key constraints...');
    
    const foreignKeys = await client.query(`
      SELECT 
        tc.constraint_name,
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name IN ('trip_logs', 'approvals')
      ORDER BY tc.table_name, tc.constraint_name
    `);

    console.log('   📋 Foreign key constraints:');
    foreignKeys.rows.forEach(fk => {
      console.log(`   ✅ ${fk.table_name}.${fk.column_name} → ${fk.foreign_table_name}.${fk.foreign_column_name}`);
    });

    // 5. Check indexes for performance
    console.log('\n5️⃣ Validating indexes for exception flow...');
    
    const indexes = await client.query(`
      SELECT 
        indexname,
        tablename,
        indexdef
      FROM pg_indexes 
      WHERE tablename IN ('trip_logs', 'approvals')
        AND indexname NOT LIKE '%_pkey'
      ORDER BY tablename, indexname
    `);

    console.log('   📋 Indexes:');
    indexes.rows.forEach(idx => {
      console.log(`   ✅ ${idx.tablename}.${idx.indexname}`);
    });

    // 6. Test data integrity
    console.log('\n6️⃣ Testing data integrity...');
    
    // Check for orphaned approvals
    const orphanedApprovals = await client.query(`
      SELECT COUNT(*) as count
      FROM approvals a
      LEFT JOIN trip_logs tl ON a.trip_log_id = tl.id
      WHERE tl.id IS NULL
    `);

    console.log(`   📊 Orphaned approvals: ${orphanedApprovals.rows[0].count}`);

    // Check for trips with exception flags but no approvals
    const exceptionTripsWithoutApprovals = await client.query(`
      SELECT COUNT(*) as count
      FROM trip_logs tl
      LEFT JOIN approvals a ON tl.id = a.trip_log_id
      WHERE tl.is_exception = true 
        AND tl.status = 'exception_pending'
        AND a.id IS NULL
    `);

    console.log(`   📊 Exception trips without approvals: ${exceptionTripsWithoutApprovals.rows[0].count}`);

    // 7. Check business logic constraints
    console.log('\n7️⃣ Validating business logic constraints...');
    
    // Check for multiple pending approvals per trip
    const multiplePendingApprovals = await client.query(`
      SELECT trip_log_id, COUNT(*) as pending_count
      FROM approvals
      WHERE status = 'pending'
      GROUP BY trip_log_id
      HAVING COUNT(*) > 1
    `);

    console.log(`   📊 Trips with multiple pending approvals: ${multiplePendingApprovals.rows.length}`);
    
    if (multiplePendingApprovals.rows.length > 0) {
      console.log('   ⚠️  Found trips with multiple pending approvals:');
      multiplePendingApprovals.rows.forEach(row => {
        console.log(`      Trip ${row.trip_log_id}: ${row.pending_count} pending approvals`);
      });
    }

    // 8. Test exception flow states
    console.log('\n8️⃣ Testing exception flow state transitions...');
    
    const stateTransitions = await client.query(`
      SELECT 
        tl.status as trip_status,
        tl.is_exception,
        a.status as approval_status,
        COUNT(*) as count
      FROM trip_logs tl
      LEFT JOIN approvals a ON tl.id = a.trip_log_id
      WHERE tl.is_exception = true
      GROUP BY tl.status, tl.is_exception, a.status
      ORDER BY count DESC
    `);

    console.log('   📊 Exception flow state distribution:');
    stateTransitions.rows.forEach(state => {
      console.log(`   📈 ${state.trip_status} + ${state.approval_status || 'no_approval'}: ${state.count} trips`);
    });

    console.log('\n✅ Database schema validation completed successfully!');
    
    return {
      valid: true,
      issues: [],
      recommendations: [
        'Consider adding a composite index on (trip_log_id, status) for approvals table',
        'Add check constraint to prevent multiple pending approvals per trip',
        'Consider adding audit triggers for approval status changes'
      ]
    };

  } catch (error) {
    console.error('❌ Database validation failed:', error.message);
    return {
      valid: false,
      error: error.message,
      issues: [error.message]
    };
  } finally {
    client.release();
  }
}

async function validateExceptionFlowQueries() {
  const client = await getClient();
  
  try {
    console.log('\n🔍 Validating Exception Flow Queries');
    console.log('='.repeat(50));

    // Test query performance for common exception flow operations
    const queries = [
      {
        name: 'Get pending approvals for trip',
        sql: `
          SELECT a.*, tl.status as trip_status
          FROM approvals a
          JOIN trip_logs tl ON a.trip_log_id = tl.id
          WHERE a.trip_log_id = $1 AND a.status = 'pending'
        `,
        params: [1]
      },
      {
        name: 'Get trip with approval status',
        sql: `
          SELECT 
            tl.*,
            a.status as approval_status,
            a.reviewed_at,
            a.reviewed_by
          FROM trip_logs tl
          LEFT JOIN approvals a ON tl.id = a.trip_log_id
          WHERE tl.id = $1
          ORDER BY a.created_at DESC
          LIMIT 1
        `,
        params: [1]
      },
      {
        name: 'Update trip status after approval',
        sql: `
          UPDATE trip_logs 
          SET status = 'loading_start',
              exception_approved_by = $1,
              exception_approved_at = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
          AND status = 'exception_pending'
        `,
        params: [1, 1]
      }
    ];

    for (const query of queries) {
      console.log(`   🔎 Testing: ${query.name}`);
      try {
        const start = Date.now();
        const result = await client.query(query.sql, query.params);
        const duration = Date.now() - start;
        console.log(`   ✅ Query executed in ${duration}ms`);
        
        if (duration > 100) {
          console.log(`   ⚠️  Query took ${duration}ms - consider optimization`);
        }
      } catch (error) {
        console.log(`   ❌ Query failed: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Query validation failed:', error.message);
  } finally {
    client.release();
  }
}

// Export validation functions
module.exports = {
  validateDatabaseSchema,
  validateExceptionFlowQueries
};

// Run validation if script is called directly
if (require.main === module) {
  (async () => {
    await validateDatabaseSchema();
    await validateExceptionFlowQueries();
  })().catch(console.error);
}
