/**
 * Test script to verify the duplicate prevention and proper assignment workflow after fixes
 */
const { Pool } = require('pg');
const config = require('../config/database');
const { processTruckScan } = require('../routes/scanner');

// Initialize DB connection
const pool = new Pool(config);

async function testDuplicatePrevention() {
  const client = await pool.connect();
  
  try {
    console.log('🧪 Testing Duplicate Prevention & Assignment Flow After Fix');
    console.log('='.repeat(60));
    
    // Step 1: Get test data
    console.log('\n1️⃣ Setting up test data');
    
    const truck = await getRandomActiveTruck(client);
    if (!truck) {
      throw new Error('No active trucks found for testing');
    }
    console.log(`Using truck ${truck.truck_number} for test`);
    
    const loadingLocation = await getRandomActiveLocation(client, 'loading');
    if (!loadingLocation) {
      throw new Error('No loading locations found for testing');
    }
    console.log(`Using loading location ${loadingLocation.name} for test`);
    
    // Step 2: Clear any existing assignments for this truck at this location today
    console.log('\n2️⃣ Clearing existing test assignments');
    await client.query(`
      DELETE FROM assignments
      WHERE truck_id = $1
        AND loading_location_id = $2
        AND assigned_date = CURRENT_DATE
    `, [truck.id, loadingLocation.id]);
    console.log('Cleared existing assignments');
    
    // Step 3: Create a new test assignment (as an admin would do)
    console.log('\n3️⃣ Creating test assignment (simulating admin action)');
    const driver = await getRandomActiveDriver(client);
    const unloadingLocation = await getRandomActiveLocation(client, 'unloading');
    
    const assignmentCode = `TEST-${Date.now()}`;
    const assignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        assigned_date, status, priority,
        expected_loads_per_day, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING id
    `, [
      assignmentCode,
      truck.id,
      driver.id,
      loadingLocation.id,
      unloadingLocation.id,
      new Date().toISOString().split('T')[0], // today
      'assigned',
      'normal',
      1,
      new Date(),
      new Date()
    ]);
    
    const assignmentId = assignmentResult.rows[0].id;
    console.log(`Created test assignment with ID ${assignmentId}`);
    
    // Step 4: Simulate duplicating this assignment (should fail due to constraint)
    console.log('\n4️⃣ Testing duplicate constraint');
    try {
      const duplicateResult = await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id,
          loading_location_id, unloading_location_id,
          assigned_date, status, priority,
          expected_loads_per_day, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING id
      `, [
        `DUPE-${Date.now()}`,
        truck.id,
        driver.id,
        loadingLocation.id,
        unloadingLocation.id,
        new Date().toISOString().split('T')[0], // today
        'assigned',
        'normal',
        1,
        new Date(),
        new Date()
      ]);
      
      console.log('❌ ERROR: Duplicate prevention constraint failed!');
      console.log(`Created duplicate assignment with ID ${duplicateResult.rows[0].id}`);
      
      // Clean up the duplicate
      await client.query('DELETE FROM assignments WHERE id = $1', [duplicateResult.rows[0].id]);
    } catch (error) {
      if (error.code === '23505') { // Unique constraint violation
        console.log('✅ SUCCESS: Duplicate constraint prevented duplicate assignment!');
        console.log(`Error: ${error.detail}`);
      } else {
        console.log(`❌ ERROR: Unexpected error during duplicate test: ${error.message}`);
      }
    }
    
    // Step 5: Test scanner flow - simulate truck scanning at the loadingLocation
    console.log('\n5️⃣ Testing scanner flow');
    
    // Create mock QR data
    const truckQrData = {
      type: 'truck',
      id: truck.truck_number
    };
    
    const locationQrData = {
      type: 'location',
      id: loadingLocation.location_code
    };
    
    // Attempt to process the truck scan (should use the existing assignment)
    try {
      const scanResult = await processTruckScan(
        client,
        truckQrData,
        locationQrData,
        1, // mock userId
        '127.0.0.1', // mock IP
        'Test Agent' // mock userAgent
      );
      
      console.log('✅ Scanner processed truck scan successfully');
      console.log(`Message: ${scanResult.message}`);
      console.log(`Trip log ID: ${scanResult.trip_log_id}`);
      console.log(`Next step: ${scanResult.next_step}`);
      
      // Check if the scanner used existing assignment
      if (scanResult.data && scanResult.data.assignment && 
          scanResult.data.assignment.id === assignmentId) {
        console.log('✅ SUCCESS: Scanner used existing assignment!');
      } else {
        console.log('❌ ERROR: Scanner did not use the expected assignment');
      }
    } catch (error) {
      console.log(`Scanner error: ${error.message}`);
    }
    
    // Step 6: Clean up
    console.log('\n6️⃣ Cleaning up test data');
    await client.query('DELETE FROM assignments WHERE id = $1', [assignmentId]);
    console.log('Test data cleaned up');
    
    console.log('\n✅ TEST COMPLETED');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Helper functions
async function getRandomActiveTruck(client) {
  const result = await client.query(`
    SELECT id, truck_number, license_plate 
    FROM dump_trucks 
    WHERE status = 'active' 
    ORDER BY RANDOM() 
    LIMIT 1
  `);
  return result.rows.length ? result.rows[0] : null;
}

async function getRandomActiveLocation(client, type) {
  const result = await client.query(`
    SELECT id, location_code, name, type 
    FROM locations 
    WHERE status = 'active' 
    AND type = $1
    ORDER BY RANDOM() 
    LIMIT 1
  `, [type]);
  return result.rows.length ? result.rows[0] : null;
}

async function getRandomActiveDriver(client) {
  const result = await client.query(`
    SELECT id, full_name
    FROM drivers
    WHERE status = 'active'
    ORDER BY RANDOM()
    LIMIT 1
  `);
  return result.rows.length ? result.rows[0] : null;
}

// Run the test
testDuplicatePrevention().catch(err => {
  console.error('Test execution failed:', err);
  process.exit(1);
});
