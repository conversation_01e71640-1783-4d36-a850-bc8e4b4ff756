SELECT
    dt.truck_number,
    dt.model,
    dt.license_plate,
    dt.capacity_tons,
    d.id AS driver_id,
    d.full_name AS driver_name,
    COUNT(tl.id) AS completed_trip_count
FROM
    dump_trucks dt
JOIN
    assignments a ON dt.id = a.truck_id
JOIN
    drivers d ON a.driver_id = d.id
JOIN
    trip_logs tl ON a.id = tl.assignment_id
WHERE
    tl.status = 'trip_completed'
GROUP BY
    dt.truck_number, dt.model, dt.license_plate, dt.capacity_tons, d.id, d.full_name
ORDER BY
    completed_trip_count DESC;