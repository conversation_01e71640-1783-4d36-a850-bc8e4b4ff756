-- Truck Trip Summary: Total trips per day per dump truck (with columns similar to Trip Monitoring Data Table)
SELECT
    dt.truck_number,
    dt.model,
    dt.license_plate,
    dt.capacity_tons,
    DATE(tl.trip_completed_time) AS trip_date,
    COUNT(tl.id) AS total_trips,
    MIN(tl.trip_completed_time) AS first_trip_time,
    MAX(tl.trip_completed_time) AS last_trip_time
FROM
    dump_trucks dt
JOIN
    assignments a ON dt.id = a.truck_id
JOIN
    trip_logs tl ON a.id = tl.assignment_id
WHERE
    tl.status = 'trip_completed'
GROUP BY
    dt.truck_number, dt.model, dt.license_plate, dt.capacity_tons, DATE(tl.trip_completed_time)
ORDER BY
    trip_date DESC, dt.truck_number ASC;