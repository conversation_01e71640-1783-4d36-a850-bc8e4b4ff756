const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');

// Validation schemas
const driverSchema = Joi.object({
  employee_id: Joi.string().min(3).max(20).required(),
  full_name: Joi.string().min(2).max(100).required(),
  license_number: Joi.string().min(5).max(30).required(),
  license_expiry: Joi.date().greater('now').required(),
  phone: Joi.string().min(10).max(20).optional().allow(''),
  email: Joi.string().email().optional().allow(''),
  address: Joi.string().max(500).optional().allow(''),
  hire_date: Joi.date().required(),
  status: Joi.string().valid('active', 'inactive', 'on_leave', 'terminated').optional(),
  notes: Joi.string().max(1000).optional().allow('')
});

const updateDriverSchema = driverSchema.fork(['employee_id', 'full_name', 'license_number', 'license_expiry', 'hire_date'], (schema) => schema.optional());

// @route   GET /api/drivers
// @desc    Get all drivers with filtering and search
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      sortBy = 'full_name',
      sortOrder = 'asc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const validSortColumns = ['employee_id', 'full_name', 'license_number', 'license_expiry', 'phone', 'status', 'created_at'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'full_name';
    const sortDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toUpperCase() : 'ASC';

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Search functionality
    if (search) {
      paramCount++;
      whereConditions.push(`(
        employee_id ILIKE $${paramCount} OR
        full_name ILIKE $${paramCount} OR
        license_number ILIKE $${paramCount} OR
        phone ILIKE $${paramCount} OR
        email ILIKE $${paramCount} OR
        notes ILIKE $${paramCount}
      )`);
      queryParams.push(`%${search}%`);
    }

    // Status filter
    if (status) {
      paramCount++;
      whereConditions.push(`status = $${paramCount}`);
      queryParams.push(status);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM drivers ${whereClause}`;
    const countResult = await query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data
    const dataQuery = `
      SELECT
        id, employee_id, full_name, license_number, license_expiry,
        phone, email, address, hire_date, status, notes, created_at, updated_at
      FROM drivers
      ${whereClause}
      ORDER BY ${sortColumn} ${sortDirection}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
    
    queryParams.push(parseInt(limit), offset);
    const driversResult = await query(dataQuery, queryParams);

    const totalPages = Math.ceil(totalItems / parseInt(limit));

    res.json({
      success: true,
      data: driversResult.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get drivers error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve drivers'
    });
  }
});

// @route   GET /api/drivers/:id
// @desc    Get single driver by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      'SELECT * FROM drivers WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Driver not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get driver error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve driver'
    });
  }
});

// @route   POST /api/drivers
// @desc    Create new driver
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    // Validate input
    const { error } = driverSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      employee_id,
      full_name,
      license_number,
      license_expiry,
      phone = '',
      email = '',
      address = '',
      hire_date,
      status = 'active',
      notes = ''
    } = req.body;

    // Check for duplicate employee ID or license number
    const duplicateCheck = await query(
      'SELECT id FROM drivers WHERE employee_id = $1 OR license_number = $2',
      [employee_id, license_number]
    );

    if (duplicateCheck.rows.length > 0) {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Employee ID or license number already exists'
      });
    }

    // Insert new driver
    const result = await query(
      `INSERT INTO drivers
       (employee_id, full_name, license_number, license_expiry, phone, email, address, hire_date, status, notes)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
       RETURNING *`,
      [employee_id, full_name, license_number, license_expiry, phone, email, address, hire_date, status, notes]
    );

    res.status(201).json({
      success: true,
      message: 'Driver created successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Create driver error:', error);
    
    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Employee ID or license number already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create driver'
    });
  }
});

// @route   PUT /api/drivers/:id
// @desc    Update driver
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateDriverSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    // Check if driver exists
    const existingDriver = await query(
      'SELECT * FROM drivers WHERE id = $1',
      [id]
    );

    if (existingDriver.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Driver not found'
      });
    }

    const {
      employee_id,
      full_name,
      license_number,
      license_expiry,
      phone,
      email,
      address,
      hire_date,
      status,
      notes
    } = req.body;

    // Check for duplicates (excluding current driver)
    if (employee_id || license_number) {
      const duplicateCheck = await query(
        'SELECT id FROM drivers WHERE (employee_id = $1 OR license_number = $2) AND id != $3',
        [employee_id || existingDriver.rows[0].employee_id,
         license_number || existingDriver.rows[0].license_number,
         id]
      );

      if (duplicateCheck.rows.length > 0) {
        return res.status(400).json({
          error: 'Duplicate Error',
          message: 'Employee ID or license number already exists'
        });
      }
    }

    // Build update query dynamically
    const updates = [];
    const values = [];
    let paramCount = 0;

    const fields = {
      employee_id,
      full_name,
      license_number,
      license_expiry,
      phone,
      email,
      address,
      hire_date,
      status,
      notes
    };

    Object.entries(fields).forEach(([key, value]) => {
      if (value !== undefined) {
        paramCount++;
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
      }
    });

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No fields to update'
      });
    }

    // Add updated_at
    paramCount++;
    updates.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add id for WHERE clause
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE drivers 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await query(updateQuery, values);

    res.json({
      success: true,
      message: 'Driver updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update driver error:', error);
    
    if (error.code === '23505') {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Employee ID or license number already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update driver'
    });
  }
});

// @route   DELETE /api/drivers/:id
// @desc    Delete driver
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if driver exists
    const existingDriver = await query(
      'SELECT * FROM drivers WHERE id = $1',
      [id]
    );

    if (existingDriver.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Driver not found'
      });
    }

    // Check if driver has active assignments
    const activeAssignments = await query(
      'SELECT id FROM assignments WHERE driver_id = $1 AND status IN ($2, $3)',
      [id, 'assigned', 'in_progress']
    );

    if (activeAssignments.rows.length > 0) {
      return res.status(400).json({
        error: 'Conflict',
        message: 'Cannot delete driver with active assignments'
      });
    }

    // Delete driver
    await query('DELETE FROM drivers WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Driver deleted successfully'
    });

  } catch (error) {
    console.error('Delete driver error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete driver'
    });
  }
});

module.exports = router;