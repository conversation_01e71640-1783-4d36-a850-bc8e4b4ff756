-- Migration: Add driver_rate column to assignments table
-- This migration adds support for automatic rate calculation based on distance

ALTER TABLE assignments 
ADD COLUMN IF NOT EXISTS driver_rate DECIMAL(10,2);

-- Add comment to explain the column
COMMENT ON COLUMN assignments.driver_rate IS 'Calculated driver rate based on distance between loading and unloading locations';

-- Create index for performance if needed
CREATE INDEX IF NOT EXISTS idx_assignments_driver_rate ON assignments(driver_rate);
