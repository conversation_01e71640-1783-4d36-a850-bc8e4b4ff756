const { query, getClient } = require('../config/database');

async function simulateApprovalUpdate() {
  console.log('🧪 Simulating the exact approval update process...');
  
  const client = await getClient();
  
  try {
    const approvalId = 3;
    const decision = 'approved';
    const notes = 'Test approval';
    const userId = 1; // Simulate user ID
    
    await client.query('BEGIN');
    
    console.log('Step 1: Getting approval details with lock...');
    const approvalResult = await client.query(
      `SELECT a.*, tl.*, tl.notes as trip_notes
       FROM approvals a
       JOIN trip_logs tl ON a.trip_log_id = tl.id
       WHERE a.id = $1
       FOR UPDATE`,
      [approvalId]
    );

    if (approvalResult.rows.length === 0) {
      console.log('❌ Approval not found');
      await client.query('ROLLBACK');
      return;
    }

    const approval = approvalResult.rows[0];
    console.log('✅ Found approval:', {
      id: approval.id,
      status: approval.status,
      exception_type: approval.exception_type,
      trip_status: approval.trip_status
    });

    if (approval.status !== 'pending') {
      console.log('❌ Approval is not pending, status:', approval.status);
      await client.query('ROLLBACK');
      return;
    }

    console.log('Step 2: Updating approval decision...');
    const updateApprovalResult = await client.query(`
      UPDATE approvals 
      SET status = $1, reviewed_by = $2, reviewed_at = CURRENT_TIMESTAMP, 
          notes = $3, updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
      RETURNING *
    `, [decision, userId, notes || '', approvalId]);

    console.log('✅ Approval updated:', updateApprovalResult.rows[0].status);

    console.log('Step 3: Handling approved exception logic...');
    if (decision === 'approved') {
      await simulateHandleApprovedException(client, approval);
    }

    await client.query('COMMIT');
    console.log('✅ Transaction completed successfully');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Error during simulation:', error);
  } finally {
    client.release();
  }
}

async function simulateHandleApprovedException(client, approval) {
  const { trip_log_id, exception_description } = approval;
  
  console.log('  Checking if this is a route deviation...');
  console.log('  Exception description:', exception_description);
  
  // Check if this is a route deviation for loading
  if (exception_description && exception_description.includes('Loading at')) {
    console.log('  ✅ This is a route deviation for loading');
    
    // Parse trip notes to get pending assignment ID
    let tripNotes;
    try {
      tripNotes = approval.trip_notes ? JSON.parse(approval.trip_notes) : {};
      console.log('  Trip notes parsed:', tripNotes);
    } catch (e) {
      console.log('  ❌ Failed to parse trip notes:', e.message);
      tripNotes = {};
    }

    if (tripNotes.pending_assignment_id) {
      console.log('  ✅ Has pending assignment ID:', tripNotes.pending_assignment_id);
      
      console.log('  Updating trip log to use new assignment...');
      await client.query(`
        UPDATE trip_logs 
        SET assignment_id = $1, 
            status = 'loading_start',
            exception_approved_by = $2,
            exception_approved_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
      `, [tripNotes.pending_assignment_id, 1, trip_log_id]);

      console.log('  Updating the new assignment to in_progress...');
      await client.query(`
        UPDATE assignments
        SET status = 'in_progress',
            start_time = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [tripNotes.pending_assignment_id]);

      console.log('  ✅ Route deviation handled with new assignment');
    } else {
      console.log('  No pending assignment, just updating trip status...');
      await client.query(`
        UPDATE trip_logs 
        SET status = 'loading_start',
            exception_approved_by = $1,
            exception_approved_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [1, trip_log_id]);
      console.log('  ✅ Trip status updated to loading_start');
    }
  } else {
    console.log('  Not a route deviation, just marking as approved...');
    await client.query(`
      UPDATE trip_logs 
      SET exception_approved_by = $1,
          exception_approved_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [1, trip_log_id]);
    console.log('  ✅ Exception marked as approved');
  }
}

simulateApprovalUpdate().then(() => {
  console.log('✅ Simulation completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Simulation failed:', error);
  process.exit(1);
});
