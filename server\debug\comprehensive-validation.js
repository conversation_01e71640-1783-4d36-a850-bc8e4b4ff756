#!/usr/bin/env node

/**
 * Comprehensive Database Schema & Flow Logic Validation Script
 * Tests all optimizations and ensures seamless functionality
 */

const { query, getClient, transaction, healthCheck, getPerformanceReport } = require('../config/database');

// Test configuration
const TEST_CONFIG = {
  verbose: true,
  skipSlowTests: false,
  testDataCleanup: true
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  skipped: 0,
  errors: [],
  details: []
};

// Utility functions
function logTest(testName, status, message = '', duration = 0) {
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : status === 'SKIP' ? '⏭️' : '🔄';
  const durationText = duration > 0 ? ` (${duration}ms)` : '';
  
  console.log(`${statusIcon} ${testName}${durationText}: ${message}`);
  
  testResults.details.push({
    test: testName,
    status,
    message,
    duration,
    timestamp: new Date().toISOString()
  });
  
  if (status === 'PASS') testResults.passed++;
  else if (status === 'FAIL') {
    testResults.failed++;
    testResults.errors.push({ test: testName, message });
  }
  else if (status === 'SKIP') testResults.skipped++;
}

async function runTest(testName, testFunction) {
  const start = Date.now();
  try {
    const result = await testFunction();
    const duration = Date.now() - start;
    
    if (result === true || result === undefined) {
      logTest(testName, 'PASS', 'Test completed successfully', duration);
    } else {
      logTest(testName, 'FAIL', result || 'Test returned false', duration);
    }
  } catch (error) {
    const duration = Date.now() - start;
    logTest(testName, 'FAIL', error.message, duration);
  }
}

// Test 1: Database Connection & Health
async function testDatabaseConnection() {
  const health = await healthCheck();
  
  if (health.status !== 'healthy') {
    throw new Error(`Database health check failed: ${health.error || 'Unknown error'}`);
  }
  
  // Verify connection pool metrics
  if (health.pool.totalCount === 0) {
    throw new Error('No database connections in pool');
  }
  
  return true;
}

// Test 2: Schema Integrity
async function testSchemaIntegrity() {
  const requiredTables = [
    'users', 'dump_trucks', 'drivers', 'locations', 
    'assignments', 'trip_logs', 'approvals', 'scan_logs'
  ];
  
  for (const table of requiredTables) {
    const result = await query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_name = $1 AND table_schema = 'public'
    `, [table]);
    
    if (parseInt(result.rows[0].count) === 0) {
      throw new Error(`Required table '${table}' not found`);
    }
  }
  
  // Verify enums exist
  const requiredEnums = [
    'user_role', 'truck_status', 'driver_status', 'location_type',
    'assignment_status', 'trip_status', 'approval_status', 'scan_type'
  ];
  
  for (const enumType of requiredEnums) {
    const result = await query(`
      SELECT COUNT(*) as count 
      FROM pg_type 
      WHERE typname = $1
    `, [enumType]);
    
    if (parseInt(result.rows[0].count) === 0) {
      throw new Error(`Required enum type '${enumType}' not found`);
    }
  }
  
  return true;
}

// Test 3: Index Performance
async function testIndexPerformance() {
  // Test critical indexes exist
  const criticalIndexes = [
    'idx_trips_assignment_status_date',
    'idx_assignments_truck_driver_date',
    'idx_approvals_trip_status_created',
    'idx_scan_logs_timestamp_user',
    'idx_locations_active_type'
  ];
  
  for (const indexName of criticalIndexes) {
    const result = await query(`
      SELECT COUNT(*) as count
      FROM pg_indexes 
      WHERE indexname = $1
    `, [indexName]);
    
    if (parseInt(result.rows[0].count) === 0) {
      throw new Error(`Critical index '${indexName}' not found`);
    }
  }
  
  // Test query performance with indexes
  const start = Date.now();
  await query(`
    SELECT t.id, t.status, a.truck_id, dt.truck_number
    FROM trip_logs t
    JOIN assignments a ON t.assignment_id = a.id
    JOIN dump_trucks dt ON a.truck_id = dt.id
    WHERE t.status IN ('assigned', 'loading_start', 'loading_end')
    AND DATE(t.created_at) = CURRENT_DATE
    ORDER BY t.created_at DESC
    LIMIT 100
  `);
  const duration = Date.now() - start;
  
  if (duration > 500) { // Should complete in under 500ms with proper indexes
    console.warn(`⚠️ Query performance warning: ${duration}ms (expected < 500ms)`);
  }
  
  return true;
}

// Test 4: Foreign Key Constraints
async function testForeignKeyConstraints() {
  const constraintChecks = [
    {
      table: 'assignments',
      column: 'truck_id',
      referenced_table: 'dump_trucks'
    },
    {
      table: 'trip_logs',
      column: 'assignment_id',
      referenced_table: 'assignments'
    },
    {
      table: 'approvals',
      column: 'trip_log_id',
      referenced_table: 'trip_logs'
    },
    {
      table: 'scan_logs',
      column: 'scanned_location_id',
      referenced_table: 'locations'
    }
  ];
  
  for (const check of constraintChecks) {
    const result = await query(`
      SELECT COUNT(*) as count
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name = $1 
        AND kcu.column_name = $2
        AND tc.constraint_type = 'FOREIGN KEY'
    `, [check.table, check.column]);
    
    if (parseInt(result.rows[0].count) === 0) {
      throw new Error(`Foreign key constraint missing: ${check.table}.${check.column}`);
    }
  }
  
  return true;
}

// Test 5: Data Integrity Constraints
async function testDataIntegrityConstraints() {
  // Test trip timing sequence constraint
  try {
    await transaction(async (client) => {
      // This should fail due to timing constraint
      await client.query(`
        INSERT INTO trip_logs (assignment_id, trip_number, status,
                              loading_start_time, loading_end_time)
        VALUES (1, 999, 'loading_start',
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP - INTERVAL '1 hour')
      `);
      throw new Error('Expected constraint violation did not occur');
    });
  } catch (error) {
    if (!error.message.includes('chk_trip_timing_sequence') && !error.message.includes('chk_duration_non_negative')) {
      throw new Error(`Unexpected error: ${error.message}`);
    }
    // Expected constraint violation - this is good
  }
  
  return true;
}

// Test 6: Materialized View Performance
async function testMaterializedViews() {
  // Check if materialized view exists
  const mvResult = await query(`
    SELECT COUNT(*) as count
    FROM pg_matviews 
    WHERE matviewname = 'mv_trip_performance_summary'
  `);
  
  if (parseInt(mvResult.rows[0].count) === 0) {
    throw new Error('Materialized view mv_trip_performance_summary not found');
  }
  
  // Test querying the materialized view
  const start = Date.now();
  const result = await query(`
    SELECT COUNT(*) as row_count
    FROM mv_trip_performance_summary
  `);
  const duration = Date.now() - start;
  
  if (duration > 100) { // Should be very fast
    console.warn(`⚠️ Materialized view query slower than expected: ${duration}ms`);
  }
  
  return true;
}

// Test 7: Analytics Functions (Optional - for Enhanced Features)
async function testAnalyticsFunctions() {
  console.log('ℹ️ Testing optional analytics functions...');
  
  // Test basic analytics queries instead of complex functions
  try {
    // Test basic exception analytics
    const basicExceptionStats = await query(`
      SELECT
        COUNT(*) as total_approvals,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count
      FROM approvals
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
    `);
    
    if (basicExceptionStats.rows.length === 0) {
      console.warn('⚠️ No analytics data available');
    } else {
      console.log('✅ Basic exception analytics working');
    }
    
    // Test basic performance metrics
    const basicPerformanceStats = await query(`
      SELECT
        COUNT(*) as total_tables
      FROM information_schema.tables
      WHERE table_schema = 'public'
    `);
    
    if (parseInt(basicPerformanceStats.rows[0].total_tables) > 0) {
      console.log('✅ Basic performance metrics accessible');
    }
    
  } catch (error) {
    console.warn(`⚠️ Analytics functions test warning: ${error.message}`);
    // Don't fail the test for optional analytics features
  }
  
  return true;
}

// Test 8: Real-time Dashboard View
async function testRealtimeDashboard() {
  const dashboardData = await query(`
    SELECT * FROM v_realtime_dashboard
  `);
  
  if (dashboardData.rows.length !== 1) {
    throw new Error('Real-time dashboard view should return exactly one row');
  }
  
  const row = dashboardData.rows[0];
  
  // Verify all expected columns are present
  const expectedColumns = [
    'active_trips', 'pending_exceptions', 'completed_trips_today',
    'exception_rate_today', 'avg_trip_duration_today', 'active_trucks',
    'last_updated'
  ];
  
  for (const column of expectedColumns) {
    if (!(column in row)) {
      throw new Error(`Dashboard view missing column: ${column}`);
    }
  }
  
  return true;
}

// Test 9: QR Scanner Integration
async function testQRScannerIntegration() {
  // Verify scanner-related tables and functions
  const scannerTables = ['scan_logs', 'locations', 'dump_trucks'];
  
  for (const table of scannerTables) {
    const count = await query(`SELECT COUNT(*) as count FROM ${table}`);
    if (parseInt(count.rows[0].count) === 0) {
      console.warn(`⚠️ Scanner table '${table}' is empty - may need seed data`);
    }
  }
  
  // Test QR code data format validation
  const qrLocationData = await query(`
    SELECT qr_code_data FROM locations 
    WHERE qr_code_data IS NOT NULL 
    LIMIT 1
  `);
  
  if (qrLocationData.rows.length > 0) {
    try {
      JSON.parse(qrLocationData.rows[0].qr_code_data);
    } catch (error) {
      throw new Error('Invalid QR code data format in locations table');
    }
  }
  
  return true;
}

// Test 10: Exception Workflow Integration
async function testExceptionWorkflow() {
  // Verify exception handling tables and relationships
  const exceptionTables = ['approvals', 'trip_logs'];
  
  for (const table of exceptionTables) {
    const result = await query(`
      SELECT COUNT(*) as count
      FROM information_schema.columns
      WHERE table_name = $1 AND column_name = 'exception_type'
    `, [table === 'trip_logs' ? table : 'approvals']);
    
    if (table === 'approvals' && parseInt(result.rows[0].count) === 0) {
      throw new Error('Approvals table missing exception_type column');
    }
  }
  
  // Test exception type enum values
  const enumValues = await query(`
    SELECT enumlabel
    FROM pg_enum
    WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'approval_status')
  `);
  
  const expectedStatuses = ['pending', 'approved', 'rejected'];
  const actualStatuses = enumValues.rows.map(row => row.enumlabel);
  
  for (const status of expectedStatuses) {
    if (!actualStatuses.includes(status)) {
      throw new Error(`Missing approval status enum value: ${status}`);
    }
  }
  
  return true;
}

// Test 11: Transaction Integrity
async function testTransactionIntegrity() {
  return await transaction(async (client) => {
    // Create a test assignment
    const assignmentResult = await client.query(`
      INSERT INTO assignments (assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id)
      VALUES ('TEST-TX-' || EXTRACT(EPOCH FROM NOW()), 1, 1, 1, 2)
      RETURNING id
    `);
    
    const assignmentId = assignmentResult.rows[0].id;
    
    // Create a test trip
    await client.query(`
      INSERT INTO trip_logs (assignment_id, trip_number, status)
      VALUES ($1, 1, 'assigned')
    `, [assignmentId]);
    
    // Verify both records exist within transaction
    const verifyResult = await client.query(`
      SELECT COUNT(*) as count
      FROM assignments a
      JOIN trip_logs t ON a.id = t.assignment_id
      WHERE a.id = $1
    `, [assignmentId]);
    
    if (parseInt(verifyResult.rows[0].count) !== 1) {
      throw new Error('Transaction integrity test failed');
    }
    
    // Clean up test data
    await client.query('DELETE FROM assignments WHERE id = $1', [assignmentId]);
    
    return true;
  });
}

// Test 12: Performance Benchmarks
async function testPerformanceBenchmarks() {
  if (TEST_CONFIG.skipSlowTests) {
    logTest('Performance Benchmarks', 'SKIP', 'Skipped due to configuration');
    return;
  }
  
  const benchmarks = [
    {
      name: 'Complex Trip Query',
      query: `
        SELECT t.id, t.status, a.assigned_date, dt.truck_number, d.full_name,
               ll.name as loading_location, ul.name as unloading_location
        FROM trip_logs t
        JOIN assignments a ON t.assignment_id = a.id
        JOIN dump_trucks dt ON a.truck_id = dt.id
        LEFT JOIN drivers d ON a.driver_id = d.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        WHERE t.created_at >= CURRENT_DATE - INTERVAL '7 days'
        ORDER BY t.created_at DESC
        LIMIT 100
      `,
      maxDuration: 300
    },
    {
      name: 'Exception Analytics Query',
      query: `
        SELECT a.exception_type, COUNT(*) as count,
               AVG(EXTRACT(EPOCH FROM (COALESCE(a.reviewed_at, CURRENT_TIMESTAMP) - a.requested_at))/3600) as avg_hours
        FROM approvals a
        WHERE a.created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY a.exception_type
      `,
      maxDuration: 200
    }
  ];
  
  for (const benchmark of benchmarks) {
    const start = Date.now();
    await query(benchmark.query);
    const duration = Date.now() - start;
    
    if (duration > benchmark.maxDuration) {
      throw new Error(`${benchmark.name} took ${duration}ms (expected < ${benchmark.maxDuration}ms)`);
    }
  }
  
  return true;
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Database Schema & Flow Logic Validation');
  console.log('=' .repeat(80));
  
  const allTests = [
    ['Database Connection & Health', testDatabaseConnection],
    ['Schema Integrity', testSchemaIntegrity],
    ['Index Performance', testIndexPerformance],
    ['Foreign Key Constraints', testForeignKeyConstraints],
    ['Data Integrity Constraints', testDataIntegrityConstraints],
    ['Materialized View Performance', testMaterializedViews],
    ['Analytics Functions', testAnalyticsFunctions],
    ['Real-time Dashboard View', testRealtimeDashboard],
    ['QR Scanner Integration', testQRScannerIntegration],
    ['Exception Workflow Integration', testExceptionWorkflow],
    ['Transaction Integrity', testTransactionIntegrity],
    ['Performance Benchmarks', testPerformanceBenchmarks]
  ];
  
  const startTime = Date.now();
  
  for (const [testName, testFunction] of allTests) {
    await runTest(testName, testFunction);
  }
  
  const totalDuration = Date.now() - startTime;
  
  // Generate performance report
  const performanceReport = getPerformanceReport();
  
  // Summary
  console.log('\n' + '=' .repeat(80));
  console.log('📊 VALIDATION SUMMARY');
  console.log('=' .repeat(80));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`⏭️ Skipped: ${testResults.skipped}`);
  console.log(`⏱️ Total Duration: ${totalDuration}ms`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED - Database schema is optimized and ready for production!');
  } else {
    console.log('\n🚨 SOME TESTS FAILED - Review errors below:');
    testResults.errors.forEach(error => {
      console.log(`   ❌ ${error.test}: ${error.message}`);
    });
  }
  
  // Performance insights
  console.log('\n📈 PERFORMANCE INSIGHTS');
  console.log('=' .repeat(80));
  console.log(`Database Connections: ${performanceReport.connections.total} total, ${performanceReport.connections.active} active`);
  console.log(`Query Performance: ${performanceReport.queries.total} queries, ${performanceReport.queries.avg_duration_ms}ms avg`);
  console.log(`Error Rate: ${performanceReport.queries.error_rate}%`);
  
  if (performanceReport.queries.slow_queries_count > 0) {
    console.log(`⚠️ Slow Queries Detected: ${performanceReport.queries.slow_queries_count}`);
  }
  
  return testResults.failed === 0;
}

// Run if called directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Validation failed with error:', error.message);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testResults
};