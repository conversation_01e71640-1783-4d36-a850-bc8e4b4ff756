const { getClient } = require('../config/database');

/**
 * Debug script to check the assignment logic
 * This will help us understand why the system is trying to create duplicate assignments
 * instead of using existing assignments from Assignment Management
 */
async function checkAssignmentLogic() {
  const client = await getClient();
  
  try {
    console.log('========== DEBUG: ASSIGNMENT CHECKING LOGIC ==========');
    
    // 1. Get all active trucks
    const trucks = await client.query(`
      SELECT id, truck_number FROM dump_trucks WHERE status = 'active' LIMIT 5
    `);
    
    console.log(`Found ${trucks.rows.length} active trucks`);
    
    // 2. For each truck, check today's assignments
    for (const truck of trucks.rows) {
      console.log(`\n===== Checking assignments for Truck: ${truck.truck_number} (ID: ${truck.id}) =====`);
      
      const assignments = await client.query(`
        SELECT 
          a.id, a.assignment_code, a.status, 
          a.loading_location_id, a.unloading_location_id,
          l_load.name AS loading_location_name, 
          l_unload.name AS unloading_location_name
        FROM assignments a
        JOIN locations l_load ON a.loading_location_id = l_load.id
        JOIN locations l_unload ON a.unloading_location_id = l_unload.id
        WHERE a.truck_id = $1 
        AND a.assigned_date = CURRENT_DATE
      `, [truck.id]);
      
      if (assignments.rows.length === 0) {
        console.log(`  ❌ No assignments found for today`);
      } else {
        console.log(`  ✓ Found ${assignments.rows.length} assignments for today:`);
        assignments.rows.forEach((assignment, idx) => {
          console.log(`    ${idx + 1}. Assignment ID: ${assignment.id}`);
          console.log(`       Code: ${assignment.assignment_code}`);
          console.log(`       Status: ${assignment.status}`);
          console.log(`       Loading: ${assignment.loading_location_name} (ID: ${assignment.loading_location_id})`);
          console.log(`       Unloading: ${assignment.unloading_location_name} (ID: ${assignment.unloading_location_id})`);
        });
      }
      
      // 3. Check if this truck has active trips
      const trips = await client.query(`        SELECT 
          tl.id, tl.status, tl.trip_number, tl.assignment_id, 
          tl.loading_start_time, tl.is_exception
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE a.truck_id = $1 
        AND tl.status NOT IN ('trip_completed', 'cancelled')
        AND DATE(tl.created_at) = CURRENT_DATE
        ORDER BY tl.created_at DESC
      `, [truck.id]);
      
      if (trips.rows.length === 0) {
        console.log(`  ❌ No active trips found`);
      } else {
        console.log(`  ✓ Found ${trips.rows.length} active trips:`);
        trips.rows.forEach((trip, idx) => {
          console.log(`    ${idx + 1}. Trip ID: ${trip.id}`);
          console.log(`       Status: ${trip.status}`);
          console.log(`       Trip Number: ${trip.trip_number}`);
          console.log(`       Assignment ID: ${trip.assignment_id}`);
          console.log(`       Is Exception: ${trip.is_exception}`);
        });
      }
    }
    
    // 4. Check recent scans to see what's happening
    console.log('\n===== Recent QR Scans =====');
    const recentScans = await client.query(`
      SELECT 
        sl.id, sl.scan_type, sl.scanned_location_id, sl.scanned_truck_id,
        sl.created_at, sl.is_valid, sl.validation_error,
        l.name AS location_name, dt.truck_number
      FROM scan_logs sl
      LEFT JOIN locations l ON sl.scanned_location_id = l.id
      LEFT JOIN dump_trucks dt ON sl.scanned_truck_id = dt.id
      ORDER BY sl.created_at DESC
      LIMIT 10
    `);
    
    console.log(`Found ${recentScans.rows.length} recent scans:`);
    recentScans.rows.forEach((scan, idx) => {
      console.log(`  ${idx + 1}. Scan ID: ${scan.id}`);
      console.log(`     Type: ${scan.scan_type}`);
      console.log(`     Valid: ${scan.is_valid}`);
      console.log(`     Time: ${new Date(scan.created_at).toLocaleString()}`);
      console.log(`     Location: ${scan.location_name || 'N/A'}`);
      console.log(`     Truck: ${scan.truck_number || 'N/A'}`);
      if (scan.validation_error) {
        console.log(`     Error: ${scan.validation_error}`);
      }
    });
    
    // 5. Check database constraints
    console.log('\n===== Assignment Constraint Check =====');
    console.log('Checking for idx_assignments_exact_duplicate constraint:');
    
    const constraintCheck = await client.query(`
      SELECT
        indexname,
        indexdef
      FROM pg_indexes
      WHERE tablename = 'assignments' 
      AND indexname = 'idx_assignments_exact_duplicate'
    `);
    
    if (constraintCheck.rows.length === 0) {
      console.log('  ❌ Constraint not found in database!');
    } else {
      console.log('  ✓ Constraint exists:');
      console.log(`     ${constraintCheck.rows[0].indexdef}`);
    }
    
    console.log('\n========== DEBUG COMPLETED ==========');
  } catch (error) {
    console.error('Error during debug:', error);
  } finally {
    client.release();
  }
}

// Run the debug script
if (require.main === module) {
  checkAssignmentLogic()
    .then(() => {
      console.log('Debug script completed.');
      process.exit(0);
    })
    .catch(err => {
      console.error('Debug script failed:', err);
      process.exit(1);
    });
}

module.exports = { checkAssignmentLogic };
