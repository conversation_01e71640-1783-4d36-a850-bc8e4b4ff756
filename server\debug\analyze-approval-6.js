/**
 * Analyze Approval ID 6 to understand the real issue
 */

const { getClient } = require('../config/database');

async function analyzeApproval6() {
  console.log('🔍 Analyzing Approval ID 6...\n');
  
  const client = await getClient();
  
  try {
    // Get detailed information about Approval ID 6
    const approvalResult = await client.query(`
      SELECT 
          ap.id,
          ap.trip_log_id,
          ap.exception_description,
          ap.status,
          ap.created_at,
          ap.reviewed_at,
          ap.reviewed_by,
          tl.assignment_id,
          tl.notes,
          a.assignment_code,
          a.status as assignment_status,
          a.loading_location_id,
          a.unloading_location_id,
          ll.name as loading_location_name,
          ul.name as unloading_location_name,
          dt.truck_number
      FROM approvals ap
      JOIN trip_logs tl ON ap.trip_log_id = tl.id
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE ap.id = 6
    `);
    
    if (approvalResult.rows.length === 0) {
      console.log('❌ Approval ID 6 not found');
      return;
    }
    
    const approval = approvalResult.rows[0];
    
    console.log('📋 Approval ID 6 Details:');
    console.log(`   Exception Description: "${approval.exception_description}"`);
    console.log(`   Status: ${approval.status}`);
    console.log(`   Created: ${approval.created_at}`);
    console.log(`   Reviewed: ${approval.reviewed_at}`);
    console.log(`   Reviewed By: ${approval.reviewed_by}`);
    console.log(`   Truck: ${approval.truck_number}`);
    console.log(`   Assignment: ${approval.assignment_code} (Status: ${approval.assignment_status})`);
    console.log(`   Loading Location ID: ${approval.loading_location_id}`);
    console.log(`   Loading Location Name: "${approval.loading_location_name}"`);
    console.log(`   Unloading Location ID: ${approval.unloading_location_id}`);
    console.log(`   Unloading Location Name: "${approval.unloading_location_name}"`);
    
    // Check trip notes
    if (approval.notes) {
      try {
        const notes = JSON.parse(approval.notes);
        console.log(`   Trip Notes:`, JSON.stringify(notes, null, 4));
      } catch (e) {
        console.log(`   Trip Notes (raw): ${approval.notes}`);
      }
    }
    
    // The key insight: The assignment has proper location names NOW
    // But the approval description was created with "undefined" 
    // This means the issue was in the CREATION of the exception, not the current state
    
    console.log('\n🔍 Analysis:');
    if (approval.loading_location_name && approval.loading_location_name !== 'undefined') {
      console.log('✅ Current assignment has proper loading location name');
      console.log('🔍 The "undefined" was in the exception description when it was CREATED');
      console.log('🎯 This confirms the issue was in the exception creation logic, not current data');
    }
    
    // Check when our fixes were applied vs when this approval was created
    const createdDate = new Date(approval.created_at);
    const today = new Date();
    const daysDiff = Math.floor((today - createdDate) / (1000 * 60 * 60 * 24));
    
    console.log(`\n📅 Timeline Analysis:`);
    console.log(`   Approval created: ${createdDate.toISOString()}`);
    console.log(`   Days ago: ${daysDiff}`);
    console.log(`   This was created BEFORE our fixes were implemented`);
    
    // Test current exception creation logic
    console.log('\n🧪 Testing Current Exception Creation Logic:');
    const testMessage = `Truck ${approval.truck_number} loading at POINT C - LOADING instead of assigned ${approval.loading_location_name || 'Unknown Location'}`;
    console.log(`   Current logic would generate: "${testMessage}"`);
    
    if (testMessage.includes('undefined')) {
      console.log('❌ Current logic still has issues');
    } else {
      console.log('✅ Current logic works correctly');
    }
    
  } catch (error) {
    console.error('Analysis failed:', error.message);
  } finally {
    client.release();
  }
}

analyzeApproval6();
