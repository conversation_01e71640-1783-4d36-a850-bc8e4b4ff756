-- Fix script for NULL driver_id issues in assignments table
-- Run this to fix any existing assignments with NULL driver_id

-- First, check if there are any NULL driver_id assignments
SELECT 
    id, 
    assignment_code, 
    truck_id, 
    driver_id, 
    status, 
    assigned_date,
    created_at
FROM assignments 
WHERE driver_id IS NULL
ORDER BY created_at DESC;

-- If there are any, we need to fix them
-- Get the first active driver to use as default
DO $$
DECLARE
    default_driver_id INTEGER;
    assignment_record RECORD;
BEGIN
    -- Find the first active driver
    SELECT id INTO default_driver_id 
    FROM drivers 
    WHERE status = 'active' 
    ORDER BY created_at ASC 
    LIMIT 1;
    
    IF default_driver_id IS NOT NULL THEN
        -- Update all assignments with NULL driver_id
        FOR assignment_record IN 
            SELECT id, assignment_code 
            FROM assignments 
            WHERE driver_id IS NULL
        LOOP
            UPDATE assignments 
            SET driver_id = default_driver_id, 
                updated_at = CURRENT_TIMESTAMP,
                notes = COALESCE(notes || ' ', '') || '[Driver auto-assigned during fix]'
            WHERE id = assignment_record.id;
            
            RAISE NOTICE 'Fixed assignment % with driver_id %', assignment_record.assignment_code, default_driver_id;
        END LOOP;
    ELSE
        -- Create a default driver if none exists
        INSERT INTO drivers (full_name, license_number, phone, status, created_at, updated_at)
        VALUES ('Default Driver', 'DEFAULT-001', '************', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id INTO default_driver_id;
        
        -- Now update assignments
        UPDATE assignments 
        SET driver_id = default_driver_id, 
            updated_at = CURRENT_TIMESTAMP,
            notes = COALESCE(notes || ' ', '') || '[Auto-assigned to default driver during fix]'
        WHERE driver_id IS NULL;
        
        RAISE NOTICE 'Created default driver with ID % and fixed assignments', default_driver_id;
    END IF;
END $$;

-- Verify the fix
SELECT 
    'After Fix' as status,
    COUNT(*) as total_assignments,
    COUNT(CASE WHEN driver_id IS NULL THEN 1 END) as null_driver_assignments
FROM assignments;

-- Show recent assignments to verify
SELECT 
    id,
    assignment_code,
    truck_id,
    driver_id,
    status,
    assigned_date,
    notes
FROM assignments 
ORDER BY created_at DESC 
LIMIT 10;
