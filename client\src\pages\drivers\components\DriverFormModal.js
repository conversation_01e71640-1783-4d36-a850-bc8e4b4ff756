import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';

const DriverFormModal = ({ driver, onClose, onSubmit }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = !!driver;

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    defaultValues: {
      employee_id: '',
      full_name: '',
      license_number: '',
      license_expiry: '',
      phone: '',
      email: '',
      address: '',
      hire_date: '',
      status: 'active',
      notes: ''
    }
  });

  // Populate form when editing
  useEffect(() => {
    if (driver) {
      // Format dates for input fields
      const formattedLicenseExpiry = driver.license_expiry
        ? new Date(driver.license_expiry).toISOString().split('T')[0]
        : '';
      const formattedHireDate = driver.hire_date
        ? new Date(driver.hire_date).toISOString().split('T')[0]
        : '';

      reset({
        employee_id: driver.employee_id || '',
        full_name: driver.full_name || '',
        license_number: driver.license_number || '',
        license_expiry: formattedLicenseExpiry,
        phone: driver.phone || '',
        email: driver.email || '',
        address: driver.address || '',
        hire_date: formattedHireDate,
        status: driver.status || 'active',
        notes: driver.notes || ''
      });
    }
  }, [driver, reset]);

  const onFormSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // Convert date strings to ISO format for backend
      const formData = {
        ...data,
        license_expiry: data.license_expiry ? new Date(data.license_expiry).toISOString() : null,
        hire_date: data.hire_date ? new Date(data.hire_date).toISOString() : null
      };

      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Get minimum date for license expiry (today)
  const getMinDate = () => {
    return new Date().toISOString().split('T')[0];
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      {/* Backdrop */}
      <div 
        className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
        onClick={handleBackdropClick}
      >
        <div className="fixed inset-0 bg-secondary-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        {/* Modal */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-secondary-900" id="modal-title">
              {isEditing ? 'Edit Driver' : 'Add New Driver'}
            </h3>
            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="text-secondary-400 hover:text-secondary-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
            {/* Employee ID */}
            <div>
              <label htmlFor="employee_id" className="block text-sm font-medium text-secondary-700 mb-1">
                Employee ID *
              </label>
              <input
                type="text"
                id="employee_id"
                {...register('employee_id', {
                  required: 'Employee ID is required',
                  minLength: {
                    value: 3,
                    message: 'Employee ID must be at least 3 characters'
                  },
                  maxLength: {
                    value: 20,
                    message: 'Employee ID must not exceed 20 characters'
                  }
                })}
                className={`input ${errors.employee_id ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                placeholder="e.g., DR-001"
                disabled={isSubmitting}
              />
              {errors.employee_id && (
                <p className="text-danger-600 text-sm mt-1">{errors.employee_id.message}</p>
              )}
            </div>

            {/* Full Name */}
            <div>
              <label htmlFor="full_name" className="block text-sm font-medium text-secondary-700 mb-1">
                Full Name *
              </label>
              <input
                type="text"
                id="full_name"
                {...register('full_name', {
                  required: 'Full name is required',
                  minLength: {
                    value: 2,
                    message: 'Full name must be at least 2 characters'
                  },
                  maxLength: {
                    value: 100,
                    message: 'Full name must not exceed 100 characters'
                  }
                })}
                className={`input ${errors.full_name ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                placeholder="e.g., John Smith"
                disabled={isSubmitting}
              />
              {errors.full_name && (
                <p className="text-danger-600 text-sm mt-1">{errors.full_name.message}</p>
              )}
            </div>

            {/* License Number and Expiry */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="license_number" className="block text-sm font-medium text-secondary-700 mb-1">
                  License Number *
                </label>
                <input
                  type="text"
                  id="license_number"
                  {...register('license_number', {
                    required: 'License number is required',
                    minLength: {
                      value: 5,
                      message: 'License number must be at least 5 characters'
                    },
                    maxLength: {
                      value: 50,
                      message: 'License number must not exceed 50 characters'
                    }
                  })}
                  className={`input ${errors.license_number ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                  placeholder="e.g., CDL123456789"
                  disabled={isSubmitting}
                />
                {errors.license_number && (
                  <p className="text-danger-600 text-sm mt-1">{errors.license_number.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="license_expiry" className="block text-sm font-medium text-secondary-700 mb-1">
                  License Expiry *
                </label>
                <input
                  type="date"
                  id="license_expiry"
                  min={getMinDate()}
                  {...register('license_expiry', {
                    required: 'License expiry date is required',
                    validate: {
                      futureDate: (value) => {
                        const selectedDate = new Date(value);
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        return selectedDate > today || 'License expiry must be in the future';
                      }
                    }
                  })}
                  className={`input ${errors.license_expiry ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                  disabled={isSubmitting}
                />
                {errors.license_expiry && (
                  <p className="text-danger-600 text-sm mt-1">{errors.license_expiry.message}</p>
                )}
              </div>
            </div>

            {/* Address */}
            <div>
              <label htmlFor="address" className="block text-sm font-medium text-secondary-700 mb-1">
                Address
              </label>
              <textarea
                id="address"
                rows={2}
                {...register('address', {
                  maxLength: {
                    value: 500,
                    message: 'Address must not exceed 500 characters'
                  }
                })}
                className={`input ${errors.address ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                placeholder="e.g., 123 Main St, City, State"
                disabled={isSubmitting}
              />
              {errors.address && (
                <p className="text-danger-600 text-sm mt-1">{errors.address.message}</p>
              )}
            </div>

            {/* Hire Date */}
            <div>
              <label htmlFor="hire_date" className="block text-sm font-medium text-secondary-700 mb-1">
                Hire Date *
              </label>
              <input
                type="date"
                id="hire_date"
                {...register('hire_date', {
                  required: 'Hire date is required'
                })}
                className={`input ${errors.hire_date ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                disabled={isSubmitting}
              />
              {errors.hire_date && (
                <p className="text-danger-600 text-sm mt-1">{errors.hire_date.message}</p>
              )}
            </div>

            {/* Phone and Email */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-secondary-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  {...register('phone', {
                    minLength: {
                      value: 10,
                      message: 'Phone number must be at least 10 digits'
                    },
                    maxLength: {
                      value: 20,
                      message: 'Phone number must not exceed 20 characters'
                    },
                    pattern: {
                      value: /^[\d\s\-\\(\\)\\+]+$/,
                      message: 'Please enter a valid phone number'
                    }
                  })}
                  className={`input ${errors.phone ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                  placeholder="e.g., (*************"
                  disabled={isSubmitting}
                />
                {errors.phone && (
                  <p className="text-danger-600 text-sm mt-1">{errors.phone.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-secondary-700 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  {...register('email', {
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Please enter a valid email address'
                    }
                  })}
                  className={`input ${errors.email ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                  placeholder="e.g., <EMAIL>"
                  disabled={isSubmitting}
                />
                {errors.email && (
                  <p className="text-danger-600 text-sm mt-1">{errors.email.message}</p>
                )}
              </div>
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-secondary-700 mb-1">
                Status
              </label>
              <select
                id="status"
                {...register('status')}
                className="input"
                disabled={isSubmitting}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="on_leave">On Leave</option>
                <option value="terminated">Terminated</option>
              </select>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-secondary-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                rows={3}
                {...register('notes', {
                  maxLength: {
                    value: 1000,
                    message: 'Notes must not exceed 1000 characters'
                  }
                })}
                className={`input ${errors.notes ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                placeholder="Optional notes about this driver..."
                disabled={isSubmitting}
              />
              {errors.notes && (
                <p className="text-danger-600 text-sm mt-1">{errors.notes.message}</p>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className="btn btn-secondary disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`btn btn-primary disabled:opacity-50 ${isSubmitting ? 'cursor-not-allowed' : ''}`}
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </div>
                ) : (
                  isEditing ? 'Update Driver' : 'Create Driver'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default DriverFormModal;