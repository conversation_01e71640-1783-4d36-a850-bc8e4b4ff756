/**
 * Test New Assignment Creation Workflow
 * 
 * Test the new approach where approved exceptions create brand new assignments
 * with 'assigned' status instead of trying to update pending assignments
 */

const { getClient } = require('../config/database');

async function testNewAssignmentWorkflow() {
  console.log('🧪 Testing New Assignment Creation Workflow...\n');
  
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // 1. Create test data for the workflow
    console.log('1️⃣ CREATING TEST DATA');
    const testData = await createTestData(client);
    
    // 2. Simulate route deviation exception creation
    console.log('\n2️⃣ SIMULATING ROUTE DEVIATION EXCEPTION');
    const exceptionData = await simulateRouteDeviationException(client, testData);
    
    // 3. Test the new assignment creation workflow
    console.log('\n3️⃣ TESTING NEW ASSIGNMENT CREATION WORKFLOW');
    await testAssignmentCreationWorkflow(client, exceptionData);
    
    // 4. Verify the results
    console.log('\n4️⃣ VERIFYING WORKFLOW RESULTS');
    await verifyWorkflowResults(client, exceptionData, testData);
    
    await client.query('ROLLBACK'); // Clean up test data
    
    console.log('\n🎉 New assignment creation workflow test completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    throw error;
  } finally {
    client.release();
  }
}

async function createTestData(client) {
  console.log('   📋 Creating test truck, driver, and locations...');
  
  // Create test truck with unique identifier
  const timestamp = Date.now();
  const shortId = timestamp.toString().slice(-6); // Last 6 digits
  const truckResult = await client.query(`
    INSERT INTO dump_trucks (truck_number, license_plate, make, model, status, qr_code_data)
    VALUES ($1, $2, 'Test Make', 'Test Model', 'active', '{}')
    RETURNING *
  `, [`DT-WF-${shortId}`, `TEST-${shortId}`]);
  
  // Create test driver
  const driverResult = await client.query(`
    INSERT INTO drivers (employee_id, full_name, license_number, license_expiry, hire_date, status)
    VALUES ($1, 'Workflow Test Driver', $2, '2026-12-31', '2024-01-01', 'active')
    RETURNING *
  `, [`EMP-WF-${shortId}`, `LIC-WF-${shortId}`]);
  
  // Create test locations
  const locationAResult = await client.query(`
    INSERT INTO locations (location_code, name, type, status, coordinates, qr_code_data)
    VALUES ($1, 'Test Loading Site', 'loading', 'active', '0.0,0.0', '{}')
    RETURNING *
  `, [`WF-A-${shortId}`]);

  const locationBResult = await client.query(`
    INSERT INTO locations (location_code, name, type, status, coordinates, qr_code_data)
    VALUES ($1, 'Test Unloading Site', 'unloading', 'active', '0.0,0.0', '{}')
    RETURNING *
  `, [`WF-B-${shortId}`]);

  const locationCResult = await client.query(`
    INSERT INTO locations (location_code, name, type, status, coordinates, qr_code_data)
    VALUES ($1, 'Test Deviation Site', 'loading', 'active', '0.0,0.0', '{}')
    RETURNING *
  `, [`WF-C-${shortId}`]);
  
  // Create original assignment (assigned status)
  const originalAssignmentResult = await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
      assigned_date, status, priority, expected_loads_per_day
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING *
  `, [
    `WORKFLOW-ORIGINAL-${Date.now()}`,
    truckResult.rows[0].id,
    driverResult.rows[0].id,
    locationAResult.rows[0].id,
    locationBResult.rows[0].id,
    new Date().toISOString().split('T')[0],
    'assigned',
    'normal',
    1
  ]);
  
  // Create pending assignment (this will be replaced by new assignment)
  const pendingAssignmentResult = await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
      assigned_date, status, priority, expected_loads_per_day
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING *
  `, [
    `WORKFLOW-PENDING-${Date.now()}`,
    truckResult.rows[0].id,
    driverResult.rows[0].id,
    locationCResult.rows[0].id, // Different location (deviation)
    locationBResult.rows[0].id,
    new Date().toISOString().split('T')[0],
    'pending_approval', // This status should be bypassed in new workflow
    'normal',
    1
  ]);
  
  console.log(`   ✅ Created test data:`);
  console.log(`      Truck: ${truckResult.rows[0].truck_number}`);
  console.log(`      Driver: ${driverResult.rows[0].full_name}`);
  console.log(`      Original Assignment: ${originalAssignmentResult.rows[0].assignment_code} (${originalAssignmentResult.rows[0].status})`);
  console.log(`      Pending Assignment: ${pendingAssignmentResult.rows[0].assignment_code} (${pendingAssignmentResult.rows[0].status})`);
  
  return {
    truck: truckResult.rows[0],
    driver: driverResult.rows[0],
    locationA: locationAResult.rows[0],
    locationB: locationBResult.rows[0],
    locationC: locationCResult.rows[0],
    originalAssignment: originalAssignmentResult.rows[0],
    pendingAssignment: pendingAssignmentResult.rows[0]
  };
}

async function simulateRouteDeviationException(client, testData) {
  console.log('   📋 Creating route deviation exception...');
  
  // Create trip log with exception
  const tripResult = await client.query(`
    INSERT INTO trip_logs (
      assignment_id, trip_number, status, loading_start_time,
      is_exception, exception_reason, notes
    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    RETURNING *
  `, [
    testData.originalAssignment.id,
    1,
    'exception_pending',
    new Date(),
    true,
    'Route deviation test',
    JSON.stringify({
      pending_assignment_id: testData.pendingAssignment.id,
      new_assignment_id: testData.pendingAssignment.id,
      exception_type: 'Route Deviation',
      original_assignment_id: testData.originalAssignment.id,
      actual_location: {
        id: testData.locationC.id,
        name: testData.locationC.name,
        type: testData.locationC.type
      }
    })
  ]);
  
  // Create approval record
  const approvalResult = await client.query(`
    INSERT INTO approvals (
      trip_log_id, exception_type, exception_description,
      severity, reported_by, status,
      created_at, updated_at
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    RETURNING *
  `, [
    tripResult.rows[0].id,
    'Route Deviation',
    `Truck ${testData.truck.truck_number} loading at ${testData.locationC.name} instead of assigned ${testData.locationA.name}`,
    'medium',
    1,
    'pending', // Will be approved in the test
    new Date(),
    new Date()
  ]);
  
  console.log(`   ✅ Created exception:`);
  console.log(`      Trip ID: ${tripResult.rows[0].id}`);
  console.log(`      Approval ID: ${approvalResult.rows[0].id}`);
  console.log(`      Description: "${approvalResult.rows[0].exception_description}"`);
  
  return {
    trip: tripResult.rows[0],
    approval: approvalResult.rows[0]
  };
}

async function testAssignmentCreationWorkflow(client, exceptionData) {
  console.log('   📋 Testing assignment creation workflow...');
  
  // Import and test the exception flow manager
  const { processApprovalAndUpdateTrip } = require('../utils/exception-flow-manager');
  
  console.log('   🔄 Processing approval with decision="approved"...');
  
  // This should trigger the new assignment creation workflow
  const result = await processApprovalAndUpdateTrip(
    client,
    exceptionData.approval.id,
    'approved',
    1, // reviewedBy (user ID)
    'Test approval for new workflow'
  );
  
  console.log('   ✅ Approval processed successfully');
  console.log(`      Result:`, JSON.stringify(result, null, 2));
  
  return result;
}

async function verifyWorkflowResults(client, exceptionData, testData) {
  console.log('   📋 Verifying workflow results...');
  
  // 1. Check that approval was updated
  const approvalResult = await client.query(`
    SELECT * FROM approvals WHERE id = $1
  `, [exceptionData.approval.id]);
  
  const approval = approvalResult.rows[0];
  console.log(`\n   📊 Approval Status:`);
  console.log(`      Status: ${approval.status} ${approval.status === 'approved' ? '✅' : '❌'}`);
  console.log(`      Reviewed By: ${approval.reviewed_by}`);
  console.log(`      Reviewed At: ${approval.reviewed_at}`);
  
  // 2. Check that trip was updated
  const tripResult = await client.query(`
    SELECT * FROM trip_logs WHERE id = $1
  `, [exceptionData.trip.id]);
  
  const trip = tripResult.rows[0];
  console.log(`\n   📊 Trip Status:`);
  console.log(`      Status: ${trip.status}`);
  console.log(`      Assignment ID: ${trip.assignment_id}`);
  console.log(`      Exception Approved By: ${trip.exception_approved_by}`);
  console.log(`      Exception Approved At: ${trip.exception_approved_at}`);
  
  // 3. Check if new assignment was created
  const newAssignmentResult = await client.query(`
    SELECT * FROM assignments 
    WHERE truck_id = $1 
      AND status = 'assigned'
      AND id != $2 
      AND id != $3
    ORDER BY created_at DESC
    LIMIT 1
  `, [testData.truck.id, testData.originalAssignment.id, testData.pendingAssignment.id]);
  
  console.log(`\n   📊 New Assignment Creation:`);
  if (newAssignmentResult.rows.length > 0) {
    const newAssignment = newAssignmentResult.rows[0];
    console.log(`      ✅ New assignment created:`);
    console.log(`         ID: ${newAssignment.id}`);
    console.log(`         Code: ${newAssignment.assignment_code}`);
    console.log(`         Status: ${newAssignment.status}`);
    console.log(`         Created: ${newAssignment.created_at}`);
    
    // Check if trip is linked to new assignment
    if (trip.assignment_id == newAssignment.id) {
      console.log(`      ✅ Trip is linked to new assignment`);
    } else {
      console.log(`      ❌ Trip is NOT linked to new assignment`);
      console.log(`         Trip Assignment ID: ${trip.assignment_id}`);
      console.log(`         New Assignment ID: ${newAssignment.id}`);
    }
  } else {
    console.log(`      ❌ No new assignment was created`);
  }
  
  // 4. Check pending assignment status (should remain unchanged)
  const pendingAssignmentResult = await client.query(`
    SELECT * FROM assignments WHERE id = $1
  `, [testData.pendingAssignment.id]);
  
  const pendingAssignment = pendingAssignmentResult.rows[0];
  console.log(`\n   📊 Original Pending Assignment:`);
  console.log(`      Status: ${pendingAssignment.status} ${pendingAssignment.status === 'pending_approval' ? '✅ (unchanged)' : '⚠️ (modified)'}`);
  console.log(`      Updated: ${pendingAssignment.updated_at}`);
  
  // 5. Summary
  console.log(`\n   🎯 WORKFLOW VERIFICATION SUMMARY:`);
  const checks = [
    { name: 'Approval approved', passed: approval.status === 'approved' },
    { name: 'Trip status updated', passed: trip.status === 'trip_completed' },
    { name: 'New assignment created', passed: newAssignmentResult.rows.length > 0 },
    { name: 'Trip linked to new assignment', passed: newAssignmentResult.rows.length > 0 && trip.assignment_id == newAssignmentResult.rows[0].id },
    { name: 'Original pending assignment unchanged', passed: pendingAssignment.status === 'pending_approval' }
  ];
  
  let passedChecks = 0;
  checks.forEach((check, index) => {
    const icon = check.passed ? '✅' : '❌';
    console.log(`      ${index + 1}. ${check.name}: ${icon}`);
    if (check.passed) passedChecks++;
  });
  
  console.log(`\n   📊 Overall Result: ${passedChecks}/${checks.length} checks passed`);
  
  if (passedChecks === checks.length) {
    console.log('   🎉 NEW ASSIGNMENT CREATION WORKFLOW IS WORKING CORRECTLY!');
  } else {
    console.log('   ⚠️ Some aspects of the workflow need attention');
  }
}

// Run the test
if (require.main === module) {
  testNewAssignmentWorkflow()
    .then(() => {
      console.log('\n🎯 New assignment creation workflow test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testNewAssignmentWorkflow };
