-- Migration: Remove assigned_date from assignment duplicate constraint
-- This allows assignments to be used regardless of the assigned date during scanning

-- Drop the existing constraint
DROP INDEX IF EXISTS idx_assignments_exact_duplicate;

-- Create a new constraint without the assigned_date field
CREATE UNIQUE INDEX idx_assignments_exact_duplicate 
ON assignments (truck_id, loading_location_id, unloading_location_id) 
WHERE status IN ('assigned', 'in_progress');

-- Add an updated comment to document the change
COMMENT ON INDEX idx_assignments_exact_duplicate IS 'Prevents exact duplicate assignments (same truck, same loading and unloading locations)';
