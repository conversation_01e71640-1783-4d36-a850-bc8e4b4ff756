// Diagnostic script for assignment creation 400 error
const express = require('express');
const { query } = require('../config/database');

async function diagnoseAssignmentError() {
  console.log('🔍 Diagnosing Assignment Creation Error...\n');
  
  try {
    // 1. Check if driver_rate column exists
    console.log('1. Checking database schema...');
    const schemaCheck = await query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'assignments' 
      ORDER BY column_name
    `);
    
    console.log('   Assignments table columns:');
    schemaCheck.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
    });
    
    const hasDriverRate = schemaCheck.rows.some(col => col.column_name === 'driver_rate');
    console.log(`   ✅ driver_rate column exists: ${hasDriverRate}\n`);
    
    // 2. Check constraints
    console.log('2. Checking constraints...');
    const constraintCheck = await query(`
      SELECT constraint_name, constraint_type 
      FROM information_schema.table_constraints 
      WHERE table_name = 'assignments'
    `);
    
    console.log('   Current constraints:');
    constraintCheck.rows.forEach(constraint => {
      console.log(`   - ${constraint.constraint_name}: ${constraint.constraint_type}`);
    });
    
    // Check for problematic unique constraints
    const hasOldConstraints = constraintCheck.rows.some(c => 
      c.constraint_name.includes('truck_id_assigned_date') || 
      c.constraint_name.includes('driver_id_assigned_date')
    );
    console.log(`   ❌ Old restrictive constraints still exist: ${hasOldConstraints}\n`);
    
    // 3. Test assignment creation logic
    console.log('3. Testing duplicate detection logic...');
    
    // Check for existing assignments for DT-100
    const existingAssignments = await query(`
      SELECT 
        a.id, a.assignment_code, a.truck_id, a.loading_location_id, 
        a.unloading_location_id, a.assigned_date, a.status,
        dt.truck_number, ll.name as loading_location, ul.name as unloading_location
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
      ORDER BY a.created_at DESC
      LIMIT 5
    `);
    
    console.log('   Existing assignments for DT-100:');
    if (existingAssignments.rows.length === 0) {
      console.log('   - No existing assignments found');
    } else {
      existingAssignments.rows.forEach(assignment => {
        console.log(`   - ${assignment.assignment_code}: ${assignment.loading_location} → ${assignment.unloading_location} (${assignment.status})`);
      });
    }
    
    // 4. Check available trucks, drivers, locations
    console.log('\n4. Checking available resources...');
    
    const [trucks, drivers, locations] = await Promise.all([
      query('SELECT id, truck_number, status FROM dump_trucks WHERE status = $1 LIMIT 3', ['active']),
      query('SELECT id, full_name, status FROM drivers WHERE status = $1 LIMIT 3', ['active']),
      query('SELECT id, name, type, is_active FROM locations WHERE is_active = true LIMIT 5')
    ]);
    
    console.log('   Available trucks:');
    trucks.rows.forEach(truck => console.log(`   - ID: ${truck.id}, Number: ${truck.truck_number}`));
    
    console.log('   Available drivers:');
    drivers.rows.forEach(driver => console.log(`   - ID: ${driver.id}, Name: ${driver.full_name}`));
    
    console.log('   Available locations:');
    locations.rows.forEach(location => console.log(`   - ID: ${location.id}, Name: ${location.name} (${location.type})`));
    
    console.log('\n✅ Diagnosis complete!');
    console.log('\n📋 Summary of findings will help identify the 400 error cause.');
    
  } catch (error) {
    console.error('❌ Diagnosis failed:', error.message);
    console.error('Full error:', error);
  }
}

// Test assignment creation with different scenarios
async function testAssignmentScenarios() {
  console.log('\n🧪 Testing Assignment Creation Scenarios...\n');
  
  try {
    // Get available resources first
    const [truck, driver, loadingLoc, unloadingLoc] = await Promise.all([
      query('SELECT id FROM dump_trucks WHERE status = $1 LIMIT 1', ['active']),
      query('SELECT id FROM drivers WHERE status = $1 LIMIT 1', ['active']),
      query('SELECT id FROM locations WHERE type = $1 AND is_active = true LIMIT 1', ['loading']),
      query('SELECT id FROM locations WHERE type = $1 AND is_active = true LIMIT 1', ['unloading'])
    ]);
    
    if (truck.rows.length === 0 || driver.rows.length === 0 || loadingLoc.rows.length === 0 || unloadingLoc.rows.length === 0) {
      console.log('❌ Not enough resources available for testing');
      return;
    }
    
    const testData = {
      truck_id: truck.rows[0].id,
      driver_id: driver.rows[0].id,
      loading_location_id: loadingLoc.rows[0].id,
      unloading_location_id: unloadingLoc.rows[0].id,
      assigned_date: new Date().toISOString().split('T')[0]
    };
    
    console.log('Test data prepared:');
    console.log(`   Truck ID: ${testData.truck_id}`);
    console.log(`   Driver ID: ${testData.driver_id}`);
    console.log(`   Loading Location ID: ${testData.loading_location_id}`);
    console.log(`   Unloading Location ID: ${testData.unloading_location_id}`);
    console.log(`   Date: ${testData.assigned_date}\n`);
    
    // Test 1: Check for existing duplicates
    console.log('1. Checking for existing duplicates...');
    const duplicateCheck = await query(`
      SELECT id, assignment_code FROM assignments 
      WHERE truck_id = $1 
        AND loading_location_id = $2 
        AND unloading_location_id = $3 
        AND assigned_date = $4 
        AND status IN ('assigned', 'in_progress')
    `, [testData.truck_id, testData.loading_location_id, testData.unloading_location_id, testData.assigned_date]);
    
    if (duplicateCheck.rows.length > 0) {
      console.log(`   ❌ Duplicate found: ${duplicateCheck.rows[0].assignment_code}`);
      console.log('   This would cause a 400 error: "Assignment already exists"');
    } else {
      console.log('   ✅ No duplicates found - assignment creation should succeed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

if (require.main === module) {
  diagnoseAssignmentError()
    .then(() => testAssignmentScenarios())
    .then(() => {
      console.log('\n🎯 Next steps:');
      console.log('1. Check the diagnosis results above');
      console.log('2. If driver_rate column is missing, run the migration');
      console.log('3. If old constraints exist, remove them');
      console.log('4. Test assignment creation in the frontend');
      process.exit(0);
    })
    .catch(error => {
      console.error('Diagnostic script failed:', error);
      process.exit(1);
    });
}

module.exports = { diagnoseAssignmentError, testAssignmentScenarios };
