/**
 * Enhanced Exception Flow Test Script
 * 
 * This script tests the complete exception flow:
 * 1. Create a trip with exception_pending status
 * 2. Verify trip cannot progress until approval
 * 3. Test admin approval process
 * 4. Verify automatic transition to loading_start
 * 5. Test location changes and assignment updates
 */

const { getClient } = require('../config/database');
const { 
  validateTripProgression,
  createExceptionAndSetPending,
  processApprovalAndUpdateTrip,
  getTripStatus,
  EXCEPTION_STATES,
  TRIP_STATES 
} = require('../utils/exception-flow-manager');

async function testEnhancedExceptionFlow() {
  const client = await getClient();
  
  try {
    console.log('🚀 Starting Enhanced Exception Flow Test');
    console.log('='.repeat(60));

    // Step 1: Create test data
    console.log('\n1️⃣ Setting up test data...');
    
    // Get a test truck and assignment
    const truckResult = await client.query(`
      SELECT id, truck_number FROM dump_trucks 
      WHERE status = 'active' LIMIT 1
    `);
    
    if (truckResult.rows.length === 0) {
      throw new Error('No active trucks found for testing');
    }
    
    const truck = truckResult.rows[0];
    console.log(`   ✅ Using truck: ${truck.truck_number} (ID: ${truck.id})`);    // Get test assignment or create one
    let assignmentResult = await client.query(`
      SELECT a.id, a.truck_id, a.driver_id, a.loading_location_id, a.unloading_location_id,
             l1.name as loading_location, l2.name as unloading_location
      FROM assignments a
      JOIN locations l1 ON a.loading_location_id = l1.id
      JOIN locations l2 ON a.unloading_location_id = l2.id
      WHERE a.truck_id = $1 AND a.status = 'assigned'
      LIMIT 1
    `, [truck.id]);

    let assignment;
    
    if (assignmentResult.rows.length === 0) {
      console.log('   📝 No existing assignment found, creating test assignment...');
      
      // Get test locations
      const locationsResult = await client.query(`
        SELECT id, name FROM locations 
        ORDER BY id 
        LIMIT 2
      `);
      
      if (locationsResult.rows.length < 2) {
        throw new Error('Need at least 2 locations for testing');
      }
      
      const [location1, location2] = locationsResult.rows;
      
      // Get a driver
      const driverResult = await client.query(`
        SELECT id, full_name FROM drivers 
        WHERE status = 'active' 
        LIMIT 1
      `);
      
      if (driverResult.rows.length === 0) {
        throw new Error('No active drivers found for testing');
      }
      
      const driver = driverResult.rows[0];
      console.log(`   👤 Using driver: ${driver.full_name} (ID: ${driver.id})`);

      // Create test assignment
      const newAssignmentResult = await client.query(`
        INSERT INTO assignments (
          truck_id, driver_id, loading_location_id, unloading_location_id, 
          assigned_date, status, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, CURRENT_DATE, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING *
      `, [truck.id, driver.id, location1.id, location2.id]);

      assignment = {
        ...newAssignmentResult.rows[0],
        loading_location: location1.name,
        unloading_location: location2.name
      };
    } else {
      assignment = assignmentResult.rows[0];
    }
    console.log(`   ✅ Using assignment: ${assignment.loading_location} → ${assignment.unloading_location}`);

    // Create a test trip log
    const tripResult = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, 
        loading_start_time, is_exception, exception_reason,
        created_at, updated_at
      ) VALUES ($1, 1, 'loading_start', CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [assignment.id]);

    const tripId = tripResult.rows[0].id;
    console.log(`   ✅ Created test trip: ID ${tripId}`);

    // Step 2: Create exception and set to pending
    console.log('\n2️⃣ Creating exception and setting to pending status...');
    
    const exceptionData = {
      exception_type: 'Route Deviation',
      exception_description: `Loading at different location instead of assigned ${assignment.loading_location}`,
      severity: 'medium',
      reported_by: 1, // Test user
      original_status: 'loading_start'
    };

    const exceptionResult = await createExceptionAndSetPending(client, tripId, exceptionData);
    console.log(`   ✅ Exception created: Approval ID ${exceptionResult.approval_id}`);
    console.log(`   ✅ Trip status set to: ${exceptionResult.trip_status}`);

    // Step 3: Test trip progression validation (should be blocked)
    console.log('\n3️⃣ Testing trip progression validation (should be blocked)...');
    
    const progressionCheck1 = await validateTripProgression(client, tripId);
    console.log(`   ✅ Can progress: ${progressionCheck1.canProgress}`);
    console.log(`   ✅ Reason: ${progressionCheck1.reason}`);
    console.log(`   ✅ Next action: ${progressionCheck1.nextAction}`);

    if (progressionCheck1.canProgress) {
      throw new Error('Trip should not be able to progress with pending exception!');
    }

    // Step 4: Test admin approval
    console.log('\n4️⃣ Testing admin approval process...');
    
    const approvalResult = await processApprovalAndUpdateTrip(
      client, 
      exceptionResult.approval_id, 
      'approved', 
      1, // Admin user ID
      'Test approval - route deviation approved'
    );

    console.log(`   ✅ Approval processed: ${approvalResult.decision}`);
    console.log(`   ✅ New trip status: ${approvalResult.trip_update.new_status}`);

    // Step 5: Test trip progression after approval (should be allowed)
    console.log('\n5️⃣ Testing trip progression after approval (should be allowed)...');
    
    const progressionCheck2 = await validateTripProgression(client, tripId);
    console.log(`   ✅ Can progress: ${progressionCheck2.canProgress}`);
    console.log(`   ✅ Reason: ${progressionCheck2.reason}`);
    console.log(`   ✅ Next action: ${progressionCheck2.nextAction}`);

    if (!progressionCheck2.canProgress) {
      throw new Error('Trip should be able to progress after approval!');
    }

    // Step 6: Verify trip status is now loading_start
    console.log('\n6️⃣ Verifying final trip status...');
    
    const finalTripStatus = await getTripStatus(client, tripId);
    console.log(`   ✅ Final trip status: ${finalTripStatus.status}`);
    console.log(`   ✅ Exception approved: ${finalTripStatus.exception_approved_at ? 'Yes' : 'No'}`);
    console.log(`   ✅ Approval status: ${finalTripStatus.approval_status}`);

    if (finalTripStatus.status !== TRIP_STATES.LOADING_START) {
      throw new Error(`Expected trip status to be ${TRIP_STATES.LOADING_START}, got ${finalTripStatus.status}`);
    }

    // Step 7: Test rejection flow
    console.log('\n7️⃣ Testing rejection flow...');
    
    // Create another test trip for rejection
    const rejectTripResult = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, 
        loading_start_time, is_exception, exception_reason,
        created_at, updated_at
      ) VALUES ($1, 2, 'loading_start', CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [assignment.id]);

    const rejectTripId = rejectTripResult.rows[0].id;

    // Create exception for rejection test
    const rejectExceptionResult = await createExceptionAndSetPending(client, rejectTripId, {
      ...exceptionData,
      exception_description: 'Test rejection scenario'
    });

    // Process rejection
    const rejectionResult = await processApprovalAndUpdateTrip(
      client, 
      rejectExceptionResult.approval_id, 
      'rejected', 
      1,
      'Test rejection - not approved'
    );

    console.log(`   ✅ Rejection processed: ${rejectionResult.decision}`);
    console.log(`   ✅ Trip status after rejection: ${rejectionResult.trip_update.new_status}`);

    if (rejectionResult.trip_update.new_status !== TRIP_STATES.CANCELLED) {
      throw new Error(`Expected trip status to be ${TRIP_STATES.CANCELLED} after rejection`);
    }

    // Cleanup test data
    console.log('\n8️⃣ Cleaning up test data...');
    await client.query('DELETE FROM trip_logs WHERE id IN ($1, $2)', [tripId, rejectTripId]);
    console.log('   ✅ Test data cleaned up');

    console.log('\n✅ All tests passed! Enhanced Exception Flow is working correctly.');
    console.log('='.repeat(60));
    
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Exception creation and pending status');
    console.log('   ✅ Trip progression blocking during pending status');
    console.log('   ✅ Admin approval process');
    console.log('   ✅ Automatic status transition to loading_start');
    console.log('   ✅ Trip progression after approval');
    console.log('   ✅ Exception rejection and trip cancellation');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    client.release();
  }
}

// Test specific exception flow scenarios
async function testLocationChangeScenario() {
  const client = await getClient();
  
  try {
    console.log('\n🌟 Testing Location Change Scenario');
    console.log('='.repeat(50));

    // Get different locations for testing
    const locationsResult = await client.query(`
      SELECT id, name, type FROM locations 
      WHERE is_active = true 
      ORDER BY type, name 
      LIMIT 3
    `);

    if (locationsResult.rows.length < 2) {
      throw new Error('Need at least 2 locations for testing');
    }

    const [location1, location2] = locationsResult.rows;
    console.log(`   📍 Original location: ${location1.name}`);
    console.log(`   📍 New location: ${location2.name}`);

    // Create assignment and trip for location change test
    const truckResult = await client.query('SELECT id, truck_number FROM dump_trucks WHERE status = \'active\' LIMIT 1');
    const truck = truckResult.rows[0];    // Get an active driver for the assignment
    const driverResult = await client.query(`
      SELECT id, full_name FROM drivers 
      WHERE status = 'active' 
      ORDER BY created_at ASC 
      LIMIT 1
    `);

    if (driverResult.rows.length === 0) {
      throw new Error('No active drivers found for testing');
    }

    const driver = driverResult.rows[0];
    console.log(`   ✅ Using driver: ${driver.full_name} (ID: ${driver.id})`);

    // Create test assignment with driver
    const assignmentResult = await client.query(`
      INSERT INTO assignments (
        truck_id, driver_id, loading_location_id, unloading_location_id, 
        assigned_date, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, CURRENT_DATE, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [truck.id, driver.id, location1.id, location2.id]);

    const assignment = assignmentResult.rows[0];

    // Create trip
    const tripResult = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, 
        loading_start_time, actual_loading_location_id,
        created_at, updated_at
      ) VALUES ($1, 1, 'loading_start', CURRENT_TIMESTAMP, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [assignment.id, location2.id]); // Different from assigned location

    const tripId = tripResult.rows[0].id;

    // Create exception with location change information
    const exceptionData = {
      exception_type: 'Route Deviation',
      exception_description: `Loading at ${location2.name} instead of assigned ${location1.name}`,
      severity: 'medium',
      reported_by: 1,
      pending_assignment_id: assignment.id + 1000 // Simulate new assignment ID
    };

    const exceptionResult = await createExceptionAndSetPending(client, tripId, exceptionData);
    console.log(`   ✅ Location change exception created`);

    // Test approval with location change
    const approvalResult = await processApprovalAndUpdateTrip(
      client, 
      exceptionResult.approval_id, 
      'approved', 
      1,
      'Approved location change'
    );

    console.log(`   ✅ Location change approved`);
    console.log(`   ✅ Assignment updated: ${approvalResult.trip_update.assignment_updated}`);

    // Cleanup
    await client.query('DELETE FROM trip_logs WHERE id = $1', [tripId]);
    await client.query('DELETE FROM assignments WHERE id = $1', [assignment.id]);
    
    console.log('   ✅ Location change test completed successfully');

  } catch (error) {
    console.error('❌ Location change test failed:', error.message);
  } finally {
    client.release();
  }
}

// Run tests
async function runAllTests() {
  console.log('🎯 Enhanced Exception Flow System - Comprehensive Test Suite');
  console.log('=' .repeat(80));
  
  await testEnhancedExceptionFlow();
  await testLocationChangeScenario();
  
  console.log('\n🎉 All tests completed!');
}

// Export for use in other scripts
module.exports = {
  testEnhancedExceptionFlow,
  testLocationChangeScenario,
  runAllTests
};

// Run tests if script is called directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
