-- Migration: Ensure scan_logs.scanned_location_id foreign key is ON DELETE SET NULL

-- 1. Drop the existing foreign key constraint (if it exists)
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT tc.constraint_name INTO constraint_name
    FROM information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    WHERE tc.table_name = 'scan_logs'
      AND kcu.column_name = 'scanned_location_id'
      AND tc.constraint_type = 'FOREIGN KEY';
    IF constraint_name IS NOT NULL THEN
        EXECUTE format('ALTER TABLE scan_logs DROP CONSTRAINT %I', constraint_name);
    END IF;
END $$;

-- 2. Add the foreign key constraint with ON DELETE SET NULL
ALTER TABLE scan_logs
  ADD CONSTRAINT scan_logs_scanned_location_id_fkey
  FOREIGN KEY (scanned_location_id)
  REFERENCES locations(id)
  ON DELETE SET NULL;
