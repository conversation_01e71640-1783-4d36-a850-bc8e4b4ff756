const { query } = require('../config/database');
const { EnhancedLogger } = require('../utils/logger');

async function testRouteDeviationScenario() {
  console.log('🔍 Testing Route Deviation Scenario: DT-100 at Point C');
  console.log('=' .repeat(60));

  try {
    // Step 1: Check if DT-100 exists and is active
    console.log('\n📋 Step 1: Checking truck DT-100...');
    const truckResult = await query(`
      SELECT id, truck_number, status, qr_code_data
      FROM dump_trucks 
      WHERE truck_number = 'DT-100'
    `);

    if (truckResult.rows.length === 0) {
      console.log('❌ Truck DT-100 not found in database');
      return;
    }

    const truck = truckResult.rows[0];
    console.log('✅ Truck found:', {
      id: truck.id,
      number: truck.truck_number,
      status: truck.status
    });

    // Step 2: Check assignments for today
    console.log('\n📋 Step 2: Checking assignments for DT-100 today...');
    const assignmentsResult = await query(`
      SELECT 
        a.id, a.assignment_code, a.status, a.assigned_date,
        a.created_at, a.updated_at,
        dt.truck_number,
        ll.name as loading_location, ll.id as loading_location_id,
        ul.name as unloading_location, ul.id as unloading_location_id
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
        AND a.assigned_date = CURRENT_DATE
      ORDER BY a.created_at DESC
    `);

    console.log(`📊 Found ${assignmentsResult.rows.length} assignments for today:`);
    assignmentsResult.rows.forEach((assignment, index) => {
      console.log(`  ${index + 1}. Assignment ${assignment.assignment_code}:`);
      console.log(`     Status: ${assignment.status}`);
      console.log(`     Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
      console.log(`     Created: ${assignment.created_at}`);
    });

    if (assignmentsResult.rows.length === 0) {
      console.log('❌ No assignments found for DT-100 today');
      console.log('💡 Creating a test assignment...');
      
      try {
        // Create test assignment: Point A → Point B
        await createTestAssignment(truck.id);
        return testRouteDeviationScenario(); // Retry
      } catch (error) {
        if (error.code === '23505') {
          console.log('⚠️  Assignment already exists, continuing with test...');
          return testRouteDeviationScenario(); // Retry
        }
        throw error;
      }
    }

    const assignment = assignmentsResult.rows[0];

    // Step 3: Check Point C location
    console.log('\n📋 Step 3: Checking Point C location...');
    const locationResult = await query(`
      SELECT id, location_code, name, type, is_active
      FROM locations 
      WHERE location_code = 'LOC-003' OR name ILIKE '%Point C%'
    `);

    if (locationResult.rows.length === 0) {
      console.log('❌ Point C (LOC-003) not found');
      console.log('💡 Creating Point C location...');
      await createTestLocation();
      return testRouteDeviationScenario(); // Retry
    }

    const pointC = locationResult.rows[0];
    console.log('✅ Point C found:', {
      id: pointC.id,
      code: pointC.location_code,
      name: pointC.name,
      type: pointC.type,
      active: pointC.is_active
    });

    // Step 4: Check existing trips
    console.log('\n📋 Step 4: Checking existing trips...');
    const tripsResult = await query(`
      SELECT 
        tl.id, tl.trip_number, tl.status, tl.is_exception,
        tl.created_at, tl.loading_start_time,
        a.assignment_code
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1 
        AND DATE(tl.created_at) = CURRENT_DATE
      ORDER BY tl.created_at DESC
    `, [truck.id]);

    console.log(`📊 Found ${tripsResult.rows.length} trips for today:`);
    tripsResult.rows.forEach((trip, index) => {
      console.log(`  ${index + 1}. Trip ${trip.trip_number}:`);
      console.log(`     Status: ${trip.status}`);
      console.log(`     Exception: ${trip.is_exception}`);
      console.log(`     Created: ${trip.created_at}`);
    });

    // Step 5: Simulate the route deviation scenario
    console.log('\n🎯 Step 5: Simulating route deviation scenario...');
    console.log(`Expected: DT-100 should load at ${assignment.loading_location}`);
    console.log(`Actual: DT-100 scans at ${pointC.name}`);
    console.log(`This should create a route deviation exception.`);

    // Test the logic that should detect this deviation
    const shouldDetectDeviation = assignment.loading_location_id !== pointC.id;
    console.log(`\n🔍 Deviation Detection: ${shouldDetectDeviation ? '✅ YES' : '❌ NO'}`);

    if (shouldDetectDeviation) {
      console.log('🎉 Route deviation should be detected and handled properly!');
      console.log('\n📝 Expected System Response:');
      console.log('  1. Detect route deviation');
      console.log('  2. Create exception record with status "pending"');
      console.log('  3. Return message: "Route deviation detected! Approval required"');
      console.log('  4. Send notification to administrators');
      console.log('  5. Wait for admin approval/rejection');
    }

    // Step 6: Check for any existing exceptions
    console.log('\n📋 Step 6: Checking existing exceptions...');
    const exceptionsResult = await query(`
      SELECT 
        ap.id, ap.exception_type, ap.status, ap.description,
        ap.created_at, ap.reviewed_at,
        tl.trip_number
      FROM approvals ap
      JOIN trip_logs tl ON ap.trip_log_id = tl.id
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
        AND DATE(ap.created_at) = CURRENT_DATE
      ORDER BY ap.created_at DESC
    `, [truck.id]);

    console.log(`📊 Found ${exceptionsResult.rows.length} exceptions for today:`);
    exceptionsResult.rows.forEach((exception, index) => {
      console.log(`  ${index + 1}. Exception ${exception.id}:`);
      console.log(`     Type: ${exception.exception_type}`);
      console.log(`     Status: ${exception.status}`);
      console.log(`     Description: ${exception.description}`);
      console.log(`     Created: ${exception.created_at}`);
    });

    console.log('\n✅ Route deviation test scenario analysis complete!');
    console.log('\n🔧 The enhanced scanner logic should now:');
    console.log('  ✅ Detect that DT-100 has an assignment for today');
    console.log('  ✅ Recognize Point C scan as a route deviation');
    console.log('  ✅ Create exception instead of throwing "No assignment" error');
    console.log('  ✅ Provide structured logging for debugging');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function createTestAssignment(truckId) {
  console.log('📝 Creating test assignment: Point A → Point B');
  
  // Get Point A and Point B
  const locationsResult = await query(`
    SELECT id, location_code, name 
    FROM locations 
    WHERE location_code IN ('LOC-001', 'LOC-002')
    ORDER BY location_code
  `);

  if (locationsResult.rows.length < 2) {
    console.log('❌ Point A or Point B not found');
    return;
  }

  const pointA = locationsResult.rows[0];
  const pointB = locationsResult.rows[1];

  await query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id,
      loading_location_id, unloading_location_id,
      assigned_date, status, priority,
      expected_loads_per_day, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
  `, [
    `TEST-${Date.now()}`,
    truckId,
    1, // Default driver
    pointA.id,
    pointB.id,
    new Date().toISOString().split('T')[0],
    'assigned',
    'normal',
    1,
    new Date(),
    new Date()
  ]);

  console.log(`✅ Created assignment: ${pointA.name} → ${pointB.name}`);
}

async function createTestLocation() {
  console.log('📝 Creating Point C location');
  
  await query(`
    INSERT INTO locations (
      location_code, name, type, coordinates,
      qr_code_data, is_active, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
  `, [
    'LOC-003',
    'Point C',
    'loading',
    '14.5995,120.9842',
    JSON.stringify({ type: 'location', id: 'LOC-003', name: 'Point C' }),
    true,
    new Date(),
    new Date()
  ]);

  console.log('✅ Created Point C location');
}

// Run the test
if (require.main === module) {
  testRouteDeviationScenario()
    .then(() => {
      console.log('\n🎯 Test completed. You can now test the scanner with:');
      console.log('  1. Scan Point C location QR code');
      console.log('  2. Scan DT-100 truck QR code');
      console.log('  3. System should detect route deviation and create exception');
      process.exit(0);
    })
    .catch(error => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testRouteDeviationScenario };