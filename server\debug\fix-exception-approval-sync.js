/**
 * Fix Exception Approval Workflow Synchronization Issues
 * 
 * This script fixes:
 * 1. Undefined route deviation message issue
 * 2. Assignment status not synchronized after exception approval
 * 3. Trip status inconsistency
 */

const { getClient } = require('../config/database');

async function fixExceptionApprovalSync() {
  console.log('🔧 Fixing Exception Approval Workflow Synchronization Issues...\n');
  
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // Fix 1: Update assignment status for approved exceptions
    console.log('1️⃣ Fixing assignment status synchronization...');
    await fixAssignmentStatusSync(client);
    
    // Fix 2: Test route deviation message generation
    console.log('\n2️⃣ Testing route deviation message generation...');
    await testRouteDeviationMessage(client);
    
    // Fix 3: Verify the fixes work
    console.log('\n3️⃣ Verifying fixes...');
    await verifyFixes(client);
    
    await client.query('COMMIT');
    
    console.log('\n🎉 Exception approval workflow synchronization fixes completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Fix failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  } finally {
    client.release();
  }
}

async function fixAssignmentStatusSync(client) {
  // Find assignments that should be 'assigned' but are still 'pending_approval'
  const syncIssuesResult = await client.query(`
    SELECT DISTINCT
        a.id as assignment_id,
        a.assignment_code,
        a.status as current_status,
        COUNT(ap.id) as approved_exceptions
    FROM assignments a
    JOIN trip_logs tl ON a.id = tl.assignment_id
    JOIN approvals ap ON tl.id = ap.trip_log_id
    WHERE a.status = 'pending_approval'
        AND ap.status = 'approved'
    GROUP BY a.id, a.assignment_code, a.status
    HAVING COUNT(ap.id) > 0
  `);
  
  if (syncIssuesResult.rows.length === 0) {
    console.log('   ✅ No assignment status synchronization issues found');
    return;
  }
  
  console.log(`   🔍 Found ${syncIssuesResult.rows.length} assignment(s) with synchronization issues:`);
  
  for (const issue of syncIssuesResult.rows) {
    console.log(`   📋 Assignment ${issue.assignment_code} (ID: ${issue.assignment_id})`);
    console.log(`      Current Status: ${issue.current_status}`);
    console.log(`      Approved Exceptions: ${issue.approved_exceptions}`);
    
    // Update the assignment status to 'assigned'
    const updateResult = await client.query(`
      UPDATE assignments 
      SET status = 'assigned',
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [issue.assignment_id]);
    
    if (updateResult.rows.length > 0) {
      console.log(`      ✅ Updated status to 'assigned'`);
    } else {
      console.log(`      ❌ Failed to update status`);
    }
  }
}

async function testRouteDeviationMessage(client) {
  // Test the route deviation message generation with actual data
  const testAssignmentResult = await client.query(`
    SELECT 
        a.id,
        a.assignment_code,
        a.loading_location_id,
        a.unloading_location_id,
        dt.truck_number,
        ll.name as loading_location_name,
        ul.name as unloading_location_name
    FROM assignments a
    JOIN dump_trucks dt ON a.truck_id = dt.id
    LEFT JOIN locations ll ON a.loading_location_id = ll.id
    LEFT JOIN locations ul ON a.unloading_location_id = ul.id
    WHERE dt.truck_number = 'DT-100'
        AND a.status IN ('assigned', 'pending_approval')
    ORDER BY a.created_at DESC
    LIMIT 1
  `);
  
  if (testAssignmentResult.rows.length === 0) {
    console.log('   ❌ No test assignment found for DT-100');
    return;
  }
  
  const assignment = testAssignmentResult.rows[0];
  console.log(`   📋 Testing with assignment: ${assignment.assignment_code}`);
  console.log(`   🚛 Truck: ${assignment.truck_number}`);
  console.log(`   📍 Loading Location: ${assignment.loading_location_name || 'UNDEFINED - THIS IS THE ISSUE!'}`);
  console.log(`   📍 Unloading Location: ${assignment.unloading_location_name || 'UNDEFINED'}`);
  
  // Check if location names are properly retrieved
  if (!assignment.loading_location_name) {
    console.log('   🚨 ISSUE FOUND: Loading location name is NULL/undefined');
    
    // Check if the location exists
    const locationCheck = await client.query(`
      SELECT id, name, location_code 
      FROM locations 
      WHERE id = $1
    `, [assignment.loading_location_id]);
    
    if (locationCheck.rows.length > 0) {
      const location = locationCheck.rows[0];
      console.log(`   🔍 Location exists: ID=${location.id}, Name="${location.name}", Code="${location.location_code}"`);
      console.log('   🔧 The issue is in the JOIN query - location name should be retrieved properly');
    } else {
      console.log(`   🚨 CRITICAL: Location ID ${assignment.loading_location_id} does not exist in locations table!`);
    }
  } else {
    console.log('   ✅ Location names are properly retrieved');
  }
  
  // Test route deviation message generation
  const actualLocationResult = await client.query(`
    SELECT id, name, location_code 
    FROM locations 
    WHERE id != $1 
    AND status = 'active'
    LIMIT 1
  `, [assignment.loading_location_id]);
  
  if (actualLocationResult.rows.length > 0) {
    const actualLocation = actualLocationResult.rows[0];
    const expectedLocationName = assignment.loading_location_name || 'UNDEFINED';
    
    console.log(`   📝 Route deviation message test:`);
    console.log(`      Expected: "Truck ${assignment.truck_number} loading at ${actualLocation.name} instead of assigned ${expectedLocationName}"`);
    
    if (expectedLocationName === 'UNDEFINED') {
      console.log('   🚨 This would result in the "undefined" message issue!');
    } else {
      console.log('   ✅ Route deviation message would be properly formatted');
    }
  }
}

async function verifyFixes(client) {
  // Verify that all assignments with approved exceptions are now 'assigned'
  const verificationResult = await client.query(`
    SELECT 
        a.id,
        a.assignment_code,
        a.status,
        COUNT(ap.id) as approved_exceptions
    FROM assignments a
    JOIN trip_logs tl ON a.id = tl.assignment_id
    JOIN approvals ap ON tl.id = ap.trip_log_id
    WHERE ap.status = 'approved'
    GROUP BY a.id, a.assignment_code, a.status
    ORDER BY a.created_at DESC
  `);
  
  console.log('   📊 Assignment status verification:');
  
  let issuesFound = 0;
  verificationResult.rows.forEach((row, index) => {
    const statusIcon = row.status === 'assigned' ? '✅' : '⚠️';
    console.log(`   ${index + 1}. ${row.assignment_code}: ${row.status} ${statusIcon} (${row.approved_exceptions} approved exceptions)`);
    
    if (row.status === 'pending_approval') {
      issuesFound++;
    }
  });
  
  if (issuesFound === 0) {
    console.log('   ✅ All assignments with approved exceptions have correct status');
  } else {
    console.log(`   ⚠️ ${issuesFound} assignment(s) still have synchronization issues`);
  }
  
  // Check for any remaining "undefined" messages in approvals
  const undefinedMessagesResult = await client.query(`
    SELECT id, exception_description
    FROM approvals
    WHERE exception_description LIKE '%undefined%'
    ORDER BY created_at DESC
    LIMIT 5
  `);
  
  if (undefinedMessagesResult.rows.length > 0) {
    console.log(`   ⚠️ Found ${undefinedMessagesResult.rows.length} approval(s) with "undefined" in description:`);
    undefinedMessagesResult.rows.forEach((row, index) => {
      console.log(`   ${index + 1}. Approval ${row.id}: "${row.exception_description}"`);
    });
  } else {
    console.log('   ✅ No "undefined" messages found in recent approvals');
  }
}

// Run the fix
if (require.main === module) {
  fixExceptionApprovalSync()
    .then(() => {
      console.log('\n🎯 Exception approval workflow synchronization is now fixed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Fix failed:', error.message);
      process.exit(1);
    });
}

module.exports = { fixExceptionApprovalSync };
