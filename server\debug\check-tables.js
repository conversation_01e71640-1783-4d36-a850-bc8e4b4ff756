const { query } = require('./config/database');

async function checkTables() {
  try {
    console.log('Checking database tables...');
    
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    const result = await query(tablesQuery);
    console.log('\n=== EXISTING TABLES ===');
    result.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.table_name}`);
    });
    
    // Check specific tables needed by the query
    const requiredTables = ['approvals', 'trip_logs', 'assignments', 'dump_trucks', 'drivers', 'locations', 'users'];
    
    console.log('\n=== TABLE STATUS ===');
    for (const tableName of requiredTables) {
      const existsQuery = `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = $1
        )
      `;
      const existsResult = await query(existsQuery, [tableName]);
      const exists = existsResult.rows[0].exists;
      console.log(`${exists ? '✅' : '❌'} ${tableName}`);
      
      if (exists && (tableName === 'drivers' || tableName === 'locations' || tableName === 'assignments')) {
        // Check columns for problematic tables
        const columnsQuery = `
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_name = $1
          ORDER BY ordinal_position
        `;
        const columnsResult = await query(columnsQuery, [tableName]);
        console.log(`   Columns: ${columnsResult.rows.map(r => r.column_name).join(', ')}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking tables:', error.message);
  }
  
  process.exit(0);
}

checkTables();
