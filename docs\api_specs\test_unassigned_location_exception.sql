-- Working example with sample values
SELECT a.*
FROM assignments a
JOIN dump_trucks dt ON a.truck_id = dt.id
WHERE dt.truck_number = 'DT-100'
  AND a.loading_location_id = 9 -- example loading location ID
  AND a.unloading_location_id = 2 -- example unloading location ID
  AND a.status IN ('assigned', 'in_progress')
LIMIT 1;

-- Parameterized template (for documentation/prepared statements)
/*
SELECT a.*
FROM assignments a
JOIN dump_trucks dt ON a.truck_id = dt.id
WHERE dt.truck_number = 'TRUCK_NUMBER_VALUE'
  AND a.loading_location_id = $1  -- loading_location_id parameter
  AND a.unloading_location_id = $2  -- unloading_location_id parameter
  AND a.status IN ('assigned', 'in_progress')
LIMIT 1;
*/
