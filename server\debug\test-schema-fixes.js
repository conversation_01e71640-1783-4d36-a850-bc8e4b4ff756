/**
 * Test Schema Fixes for Database Issues
 * 
 * This script tests the fixes for:
 * 1. Missing 'is_active' column error in assignments.js
 * 2. Truck trip summary duplicate data issue
 */

const { getClient } = require('../config/database');

async function testSchemaFixes() {
  console.log('🔧 Testing Database Schema Fixes...\n');
  
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // Test 1: Verify locations table schema
    console.log('📋 Test 1: Verify locations table schema');
    await testLocationsSchema(client);
    console.log('✅ Locations schema test passed\n');
    
    // Test 2: Test assignment creation with location validation
    console.log('🏗️  Test 2: Test assignment creation with location validation');
    await testAssignmentCreation(client);
    console.log('✅ Assignment creation test passed\n');
    
    // Test 3: Test truck trip summary for duplicates
    console.log('📊 Test 3: Test truck trip summary for duplicates');
    await testTripSummaryDuplicates(client);
    console.log('✅ Trip summary duplicate test passed\n');
    
    await client.query('ROLLBACK'); // Clean up test data
    
    console.log('🎉 All schema fix tests passed successfully!');
    console.log('\n📊 Summary:');
    console.log('  ✅ Locations table uses correct "status" column');
    console.log('  ✅ Assignment creation works with location validation');
    console.log('  ✅ Trip summary queries no longer create duplicates');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  } finally {
    client.release();
  }
}

async function testLocationsSchema(client) {
  // Check that locations table has 'status' column, not 'is_active'
  const schemaResult = await client.query(`
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns 
    WHERE table_name = 'locations' 
    AND table_schema = 'public'
    ORDER BY ordinal_position
  `);
  
  const columns = schemaResult.rows.map(row => row.column_name);
  
  if (!columns.includes('status')) {
    throw new Error('Locations table missing "status" column');
  }
  
  if (columns.includes('is_active')) {
    console.log('  ⚠️  Warning: locations table still has "is_active" column (should be migrated to "status")');
  }
  
  console.log('  ✅ Locations table has correct "status" column');
  console.log(`  📋 Columns found: ${columns.join(', ')}`);
}

async function testAssignmentCreation(client) {
  // Create test data
  const testData = await setupTestData(client);
  
  // Test location validation query (this is what was failing before)
  const locationValidationQuery = `
    SELECT id FROM locations 
    WHERE id = $1 AND status = $2
  `;
  
  // Test loading location validation
  const loadingResult = await client.query(locationValidationQuery, [testData.locationA.id, 'active']);
  if (loadingResult.rows.length === 0) {
    throw new Error('Loading location validation failed');
  }
  
  // Test unloading location validation
  const unloadingResult = await client.query(locationValidationQuery, [testData.locationB.id, 'active']);
  if (unloadingResult.rows.length === 0) {
    throw new Error('Unloading location validation failed');
  }
  
  console.log('  ✅ Location validation queries work correctly');
  console.log(`  📍 Loading location validated: ${testData.locationA.name}`);
  console.log(`  📍 Unloading location validated: ${testData.locationB.name}`);
}

async function testTripSummaryDuplicates(client) {
  const testData = await setupTestData(client);
  
  // Create multiple assignments and trips for the same truck
  const assignment1 = await createTestAssignment(
    client, 
    testData.truck.id, 
    testData.driver.id, 
    testData.locationA.id, 
    testData.locationB.id,
    { assigned_date: '2025-06-25' }
  );
  
  const assignment2 = await createTestAssignment(
    client, 
    testData.truck.id, 
    testData.driver.id, 
    testData.locationA.id, 
    testData.locationB.id,
    { assigned_date: '2025-06-26', status: 'completed' } // Different date, completed status
  );
  
  // Create trips for both assignments
  await createTestTrip(client, assignment1.id, 'trip_completed');
  await createTestTrip(client, assignment1.id, 'trip_completed');
  await createTestTrip(client, assignment2.id, 'trip_completed');
  
  // Test the fixed trip summary query (should not create duplicates)
  const summaryQuery = `
    SELECT
      dt.truck_number,
      dt.model,
      dt.license_plate,
      COALESCE(ll.name, 'Unknown') AS loading_location,
      COALESCE(ul.name, 'Unknown') AS unloading_location,
      COUNT(tl.id) AS total_trips,
      COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) AS completed_trips
    FROM
      dump_trucks dt
    JOIN
      assignments a ON dt.id = a.truck_id
    JOIN
      trip_logs tl ON a.id = tl.assignment_id
    LEFT JOIN
      locations ll ON COALESCE(tl.actual_loading_location_id, a.loading_location_id) = ll.id
    LEFT JOIN
      locations ul ON COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) = ul.id
    WHERE
      dt.truck_number = $1
      AND tl.status = 'trip_completed'
    GROUP BY
      dt.truck_number,
      dt.model,
      dt.license_plate,
      ll.name,
      ul.name
    ORDER BY
      dt.truck_number
  `;
  
  const summaryResult = await client.query(summaryQuery, [testData.truck.truck_number]);
  
  // Should have only 1 row for the truck-location combination, not multiple rows per date
  if (summaryResult.rows.length !== 1) {
    throw new Error(`Expected 1 summary row, got ${summaryResult.rows.length}. Duplicate issue not fixed.`);
  }
  
  const summary = summaryResult.rows[0];
  if (parseInt(summary.total_trips) !== 3) {
    throw new Error(`Expected 3 total trips, got ${summary.total_trips}`);
  }
  
  console.log('  ✅ Trip summary query returns single row per truck-location combination');
  console.log(`  📊 Truck: ${summary.truck_number}, Total trips: ${summary.total_trips}`);
  console.log(`  📍 Location: ${summary.loading_location} → ${summary.unloading_location}`);
}

// Helper functions
async function setupTestData(client) {
  // Create test truck with unique identifier (keep under 20 chars)
  const shortId = Math.random().toString(36).substr(2, 8);
  const uniqueId = `DT-${shortId}`;
  const qrCodeData = { type: 'truck', id: uniqueId, timestamp: new Date().toISOString() };
  const truckResult = await client.query(`
    INSERT INTO dump_trucks (truck_number, license_plate, make, model, status, qr_code_data)
    VALUES ($1, $2, 'Test Make', 'Test Model', 'active', $3)
    RETURNING *
  `, [uniqueId, `LP-${shortId}`, qrCodeData]);
  
  // Create test driver with unique identifier
  const driverShortId = Math.random().toString(36).substr(2, 8);
  const driverUniqueId = `EMP-${driverShortId}`;
  const driverResult = await client.query(`
    INSERT INTO drivers (employee_id, full_name, license_number, license_expiry, hire_date, status)
    VALUES ($1, 'Schema Test Driver', $2, '2026-12-31', '2024-01-01', 'active')
    RETURNING *
  `, [driverUniqueId, `LIC-${driverShortId}`]);
  
  // Create test locations with unique identifiers
  const locationShortId = Math.random().toString(36).substr(2, 8);
  const locationA = await createTestLocation(client, `SA-${locationShortId}`, 'Schema Test Point A', 'loading');
  const locationB = await createTestLocation(client, `SB-${locationShortId}`, 'Schema Test Point B', 'unloading');
  
  return {
    truck: truckResult.rows[0],
    driver: driverResult.rows[0],
    locationA,
    locationB
  };
}

async function createTestLocation(client, code, name, type) {
  const qrCodeData = {
    type: 'location',
    id: code,
    name: name,
    location_type: type,
    timestamp: new Date().toISOString()
  };
  
  const result = await client.query(`
    INSERT INTO locations (location_code, name, type, status, coordinates, qr_code_data)
    VALUES ($1, $2, $3, 'active', '0.0,0.0', $4)
    RETURNING *
  `, [code, name, type, qrCodeData]);
  return result.rows[0];
}

async function createTestAssignment(client, truckId, driverId, loadingLocationId, unloadingLocationId, options = {}) {
  const assignmentCode = `SCHEMA-TEST-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
  const result = await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
      assigned_date, status, priority, expected_loads_per_day
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING *
  `, [
    assignmentCode,
    truckId,
    driverId,
    loadingLocationId,
    unloadingLocationId,
    options.assigned_date || new Date().toISOString().split('T')[0],
    options.status || 'assigned',
    options.priority || 'normal',
    options.expected_loads_per_day || 1
  ]);
  return result.rows[0];
}

async function createTestTrip(client, assignmentId, status) {
  const result = await client.query(`
    INSERT INTO trip_logs (assignment_id, trip_number, status, loading_start_time)
    VALUES ($1, (SELECT COALESCE(MAX(trip_number), 0) + 1 FROM trip_logs WHERE assignment_id = $1), $2, CURRENT_TIMESTAMP)
    RETURNING *
  `, [assignmentId, status]);
  return result.rows[0];
}

// Run the test
if (require.main === module) {
  testSchemaFixes()
    .then(() => {
      console.log('\n🎯 Database schema fixes are working correctly!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Schema fix test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testSchemaFixes };
