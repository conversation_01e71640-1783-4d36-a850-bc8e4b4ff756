const { getClient } = require('../config/database');

/**
 * Test script to verify that route deviations at trip completion are properly detected
 * Simulates the exact scenario: DT-100 completes unloading at Point B, then goes to Point C
 * This should trigger a route deviation exception and create a new assignment
 */

async function testRouteDeviationAtCompletion() {
  console.log('🧪 Testing Route Deviation at Trip Completion');
  console.log('='.repeat(60));
  
  let client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    console.log('\n1️⃣ SETUP: Getting test data...');
    
    // Get test truck DT-100
    const truckResult = await client.query(
      'SELECT id, truck_number FROM dump_trucks WHERE truck_number = $1',
      ['DT-100']
    );
    
    if (truckResult.rows.length === 0) {
      console.log('❌ DT-100 not found. Please ensure DT-100 exists in the database.');
      return;
    }
    
    const truck = truckResult.rows[0];
    console.log(`✅ Found truck: ${truck.truck_number} (ID: ${truck.id})`);
    
    // Get test locations
    const locationsResult = await client.query(`
      SELECT id, name, location_code, type 
      FROM locations 
      WHERE name IN ('Point A - Main Loading Site', 'Point B - Primary Dump Site', 'Point C - Secondary Dump Site')
      ORDER BY name
    `);
    
    if (locationsResult.rows.length < 3) {
      console.log('❌ Missing required locations. Need Point A, Point B, and Point C.');
      return;
    }
    
    const pointA = locationsResult.rows.find(l => l.name.includes('Point A'));
    const pointB = locationsResult.rows.find(l => l.name.includes('Point B'));
    const pointC = locationsResult.rows.find(l => l.name.includes('Point C')) || 
                  locationsResult.rows.find(l => l.name.includes('Secondary'));
    
    console.log(`✅ Point A: ${pointA.name} (ID: ${pointA.id})`);
    console.log(`✅ Point B: ${pointB.name} (ID: ${pointB.id})`);
    console.log(`✅ Point C: ${pointC.name} (ID: ${pointC.id})`);
    
    // Get test driver
    const driverResult = await client.query(
      'SELECT id, full_name FROM drivers WHERE status = $1 LIMIT 1',
      ['active']
    );
    
    if (driverResult.rows.length === 0) {
      console.log('❌ No active drivers found.');
      return;
    }
    
    const driver = driverResult.rows[0];
    console.log(`✅ Driver: ${driver.full_name} (ID: ${driver.id})`);
      console.log('\n2️⃣ GETTING OR CREATING TEST ASSIGNMENT: Point A → Point B...');
    
    // First, try to find existing assignment
    let assignmentResult = await client.query(`
      SELECT id, assignment_code, status
      FROM assignments 
      WHERE truck_id = $1 
        AND loading_location_id = $2 
        AND unloading_location_id = $3 
        AND assigned_date = CURRENT_DATE
      LIMIT 1
    `, [truck.id, pointA.id, pointB.id]);
    
    let assignment;
    if (assignmentResult.rows.length > 0) {
      assignment = assignmentResult.rows[0];
      console.log(`✅ Found existing assignment: ${assignment.assignment_code} (ID: ${assignment.id})`);
      
      // Update status to in_progress if needed
      if (assignment.status !== 'in_progress') {
        await client.query(`
          UPDATE assignments SET status = 'in_progress', updated_at = CURRENT_TIMESTAMP 
          WHERE id = $1
        `, [assignment.id]);
        console.log(`   Updated assignment status to 'in_progress'`);
      }
    } else {
      // Create a unique assignment for this test using tomorrow's date to avoid conflicts
      const assignmentCode = `TEST-DEVIATION-${Date.now()}`;
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      assignmentResult = await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, 
          loading_location_id, unloading_location_id,
          assigned_date, status, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, 'in_progress', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id, assignment_code
      `, [
        assignmentCode,
        truck.id,
        driver.id,
        pointA.id,
        pointB.id,
        tomorrow.toISOString().split('T')[0]
      ]);
      
      assignment = assignmentResult.rows[0];
      console.log(`✅ Created new assignment: ${assignment.assignment_code} (ID: ${assignment.id})`);
    }
    
    console.log(`   Route: ${pointA.name} → ${pointB.name} → ${pointA.name}`);
      console.log('\n3️⃣ CREATING TRIP IN "UNLOADING_END" STATE...');
    
    // Get the next available trip number for this assignment
    const tripNumberResult = await client.query(`
      SELECT COALESCE(MAX(trip_number), 0) + 1 as next_trip_number
      FROM trip_logs 
      WHERE assignment_id = $1
    `, [assignment.id]);
    
    const nextTripNumber = tripNumberResult.rows[0].next_trip_number;
    
    // Create a trip that has completed unloading and is ready for completion
    const tripResult = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status,
        loading_start_time, loading_end_time,
        unloading_start_time, unloading_end_time,
        actual_loading_location_id, is_exception,
        created_at, updated_at
      ) VALUES ($1, $2, 'unloading_end', $3, $4, $5, $6, $7, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING id
    `, [
      assignment.id,
      nextTripNumber,
      new Date(Date.now() - 120 * 60 * 1000), // 2 hours ago
      new Date(Date.now() - 100 * 60 * 1000), // 1h 40m ago
      new Date(Date.now() - 80 * 60 * 1000),  // 1h 20m ago  
      new Date(Date.now() - 10 * 60 * 1000),  // 10 minutes ago
      pointA.id
    ]);
    
    const trip = tripResult.rows[0];
    console.log(`✅ Created trip #${nextTripNumber} in 'unloading_end' state (ID: ${trip.id})`);
    console.log('   Status: Unloading completed at Point B, ready for completion scan');
    
    console.log('\n4️⃣ TESTING COMPLETION DEVIATION SCENARIO...');
    console.log(`   📍 Current Status: Trip completed unloading at Point B`);
    console.log(`   📍 Expected: Truck should return to Point A (assigned loading location)`);
    console.log(`   📍 Actual Scenario: Truck goes to Point C instead`);
    console.log(`   📍 What Should Happen: Route deviation exception triggered`);
    
    console.log('\n   🎯 Expected System Behavior:');
    console.log(`      1. Detect that Point C ≠ Point A (assigned loading location)`);
    console.log(`      2. Trigger route deviation exception`);
    console.log(`      3. Create pending approval`);
    console.log(`      4. Create new assignment: Point C → Point B → Point C`);
    console.log(`      5. Wait for admin approval before proceeding`);
    
    console.log('\n5️⃣ VERIFICATION OF CURRENT STATE...');
    
    // Check current trip status
    const tripCheckResult = await client.query(
      'SELECT status, is_exception FROM trip_logs WHERE id = $1',
      [trip.id]
    );
    console.log(`   Current trip status: ${tripCheckResult.rows[0].status}`);
    console.log(`   Is exception: ${tripCheckResult.rows[0].is_exception}`);
    
    // Check for any pending approvals
    const approvalsResult = await client.query(`
      SELECT COUNT(*) as pending_count 
      FROM approvals 
      WHERE trip_log_id = $1 AND status = 'pending'
    `, [trip.id]);
    console.log(`   Pending approvals: ${approvalsResult.rows[0].pending_count}`);
    
    // Check existing assignments for this truck
    const assignmentsResult = await client.query(`
      SELECT COUNT(*) as assignment_count 
      FROM assignments 
      WHERE truck_id = $1 AND assigned_date = CURRENT_DATE
    `, [truck.id]);
    console.log(`   Total assignments for DT-100 today: ${assignmentsResult.rows[0].assignment_count}`);
    
    console.log('\n✅ Test scenario setup completed!');
    console.log('\n💡 ISSUE IDENTIFIED:');
    console.log('   Based on your description, when DT-100 scans at Point C after unloading,');
    console.log('   the system incorrectly accepts it without triggering an exception.');
    console.log('   This suggests the scanner logic in handleUnloadingEnd() needs to be fixed');
    console.log('   to properly validate the completion location matches the assigned loading location.');
    
    console.log('\n🔧 RECOMMENDED FIX:');
    console.log('   Update handleUnloadingEnd() in scanner.js to:');
    console.log('   1. Check if scanned location matches assigned loading location');
    console.log('   2. If not, trigger createRouteDeviationForExistingAssignment()');
    console.log('   3. Create new assignment with revised flow: Point C → Point B → Point C');
    console.log('   4. Require admin approval before proceeding');
    
    console.log('\n📝 TO TEST THE FIX:');
    console.log('   1. Implement the scanner.js fix');
    console.log('   2. Use QR scanner to scan Point C location');
    console.log('   3. Then scan DT-100 truck QR code');
    console.log('   4. Verify exception is created and approval is required');
    console.log('   5. Approve exception and verify new assignment is created');
    
    await client.query('ROLLBACK');
    console.log('\n🧹 Test data rolled back');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    client.release();
  }
}

// Run the test
if (require.main === module) {
  testRouteDeviationAtCompletion()
    .then(() => {
      console.log('\n🎯 Route Deviation at Completion Test completed.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testRouteDeviationAtCompletion };
