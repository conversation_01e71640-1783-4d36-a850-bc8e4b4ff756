/* Tailwind CSS Directives - These are processed during build time */
@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Base layer styles - foundational styles */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f8fafc;
  }
  
  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-secondary-200 text-secondary-800 hover:bg-secondary-300 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md border border-secondary-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-secondary-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .table {
    @apply w-full text-left border-collapse;
  }
  
  .table th {
    @apply px-4 py-3 bg-secondary-50 border-b border-secondary-200 font-semibold text-secondary-700;
  }
  
  .table td {
    @apply px-4 py-3 border-b border-secondary-100;
  }
  
  .sidebar-item {
    @apply flex items-center px-4 py-3 text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200 cursor-pointer;
  }
  
  .sidebar-item.active {
    @apply bg-primary-100 text-primary-700 border-r-2 border-primary-600;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }
  
  .badge-info {
    @apply bg-primary-100 text-primary-800;
  }
  
  .scanner-overlay {
    @apply absolute inset-0 border-2 border-primary-500 rounded-lg;
  }
  
  .scanner-corner {
    @apply absolute w-6 h-6 border-primary-600;
  }
  
  .scanner-corner.top-left {
    @apply top-2 left-2 border-t-2 border-l-2;
  }
  
  .scanner-corner.top-right {
    @apply top-2 right-2 border-t-2 border-r-2;
  }
  
  .scanner-corner.bottom-left {
    @apply bottom-2 left-2 border-b-2 border-l-2;
  }
  
  .scanner-corner.bottom-right {
    @apply bottom-2 right-2 border-b-2 border-r-2;
  }
}

@layer utilities {
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .bg-gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }
  
  .bg-gradient-success {
    background: linear-gradient(135deg, #22c55e 0%, #15803d 100%);
  }
  
  .bg-gradient-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #b45309 100%);
  }
    .bg-gradient-danger {
    background: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%);
  }
  
  /* Responsive Table Utilities */
  .table-responsive {
    @apply w-full overflow-hidden bg-white shadow-lg sm:rounded-lg border border-secondary-200;
  }
  
  .table-header-responsive {
    @apply bg-gradient-to-r from-secondary-50 to-secondary-100;
  }
  
  .table-cell-responsive {
    @apply px-4 sm:px-6 py-4 text-sm;
  }
  
  .table-row-hover {
    @apply hover:bg-secondary-50 transition-colors duration-150;
  }
  
  .mobile-stack {
    @apply md:hidden mt-2 space-y-1 text-xs text-secondary-500;
  }
  
  .desktop-only {
    @apply hidden md:table-cell;
  }
  
  .large-desktop-only {
    @apply hidden lg:table-cell;
  }
  
  .mobile-inline {
    @apply md:hidden inline;
  }
  
  .desktop-inline {
    @apply hidden md:inline;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Print styles for QR codes */
@media print {
  body * {
    visibility: hidden;
  }
  
  .print-area, .print-area * {
    visibility: visible;
  }
  
  .print-area {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  
  @page {
    margin: 1in;
  }
}