const { query, getClient } = require('../config/database');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function testDuplicateAssignmentHandling() {
  console.log('🧪 Testing duplicate assignment handling with revised flow logic...');
  
  let client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // 1. Create a test scenario with potential duplicate assignment
    console.log('Step 1: Setting up test scenario...');
    
    // Get a test truck
    const trucksResult = await client.query('SELECT id, truck_number FROM dump_trucks LIMIT 1');
    if (trucksResult.rows.length === 0) {
      console.log('❌ No trucks available for testing');
      await client.query('ROLLBACK');
      client.release();
      return;
    }
    const truck = trucksResult.rows[0];
    
    // Get loading and unloading locations
    const locationsResult = await client.query(`
      SELECT id, name, type FROM locations 
      WHERE type IN ('loading', 'unloading') 
      LIMIT 3
    `);
    
    if (locationsResult.rows.length < 3) {
      console.log('❌ Not enough locations for testing');
      await client.query('ROLLBACK');
      client.release();
      return;
    }
    
    const loadingLocation = locationsResult.rows.find(l => l.type === 'loading') || locationsResult.rows[0];
    const unloadingLocation = locationsResult.rows.find(l => l.type === 'unloading') || locationsResult.rows[1];
    const alternateLocation = locationsResult.rows[2];
    
    // Get a driver
    const driversResult = await client.query('SELECT id, full_name FROM drivers LIMIT 1');
    if (driversResult.rows.length === 0) {
      console.log('❌ No drivers available for testing');
      await client.query('ROLLBACK');
      client.release();
      return;
    }
    const driver = driversResult.rows[0];
    
    // Create first assignment (A → B → A flow)
    console.log('\nCreating original assignment with A → B → A flow');
    const assignment1Result = await client.query(`
      INSERT INTO assignments (
        truck_id, driver_id, loading_location_id, unloading_location_id,
        status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING id
    `, [truck.id, driver.id, loadingLocation.id, unloadingLocation.id]);
    
    const assignment1Id = assignment1Result.rows[0].id;
    
    console.log(`✅ Created first assignment: ${assignment1Id}`);
    console.log(`   Truck: ${truck.truck_number}, Driver: ${driver.full_name}`);
    console.log(`   Original flow: ${loadingLocation.name} → ${unloadingLocation.name} → ${loadingLocation.name}`);
    
    // Create a trip log for this assignment
    const tripResult =    await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, created_at, updated_at, notes
      ) VALUES ($1, $2, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '{"test_notes":"This is a test trip"}'::jsonb)
      RETURNING id
    `, [assignment1Id, Math.floor(Date.now() / 1000)]);
    
    const tripId = tripResult.rows[0].id;
    console.log(`✅ Created trip log: ${tripId}`);
    
    // Create a pending approval for the trip
    const approvalResult = await client.query(`
      INSERT INTO approvals (
        trip_log_id, exception_type, exception_description, 
        severity, status, created_at, updated_at
      ) VALUES (
        $1, 'route_deviation', 'Loading at different location for test', 
        'medium', 'pending', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      )
      RETURNING id
    `, [tripId]);
    
    const approvalId = approvalResult.rows[0].id;
    console.log(`✅ Created approval: ${approvalId}`);
      // Create second assignment with revised flow (C → B → C)
    console.log('\nStep 2: Creating revised flow assignment (C → B → C)...');
    
    // This represents a route deviation where the truck is at location C but should be at A
    const assignment2Result = await client.query(`
      INSERT INTO assignments (
        truck_id, driver_id, loading_location_id, unloading_location_id,
        status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING id
    `, [truck.id, driver.id, alternateLocation.id, unloadingLocation.id]);
    
    const assignment2Id = assignment2Result.rows[0].id;
    console.log(`✅ Created second assignment: ${assignment2Id} (revised flow)`);
    console.log(`   Revised flow: ${alternateLocation.name} → ${unloadingLocation.name} → ${alternateLocation.name}`);
      // Update trip notes to include the pending assignment with revised flow information
    const initialNotesObj = { 
        pending_assignment_id: assignment2Id,
        original_assignment_id: assignment1Id,
        deviation_type: 'location_flow',
        from_location: loadingLocation.name,
        to_location: alternateLocation.name,
        revised_flow_pattern: `${alternateLocation.name} → ${unloadingLocation.name} → ${alternateLocation.name}`
    };
    
    await client.query(`
      UPDATE trip_logs 
      SET notes = $1
      WHERE id = $2
    `, [JSON.stringify(initialNotesObj), tripId]);
    
    console.log('✅ Updated trip notes with revised flow information');
    
    // Step 3: Test the handleApprovedException logic
    console.log('\nStep 3: Testing approval with revised flow logic...');
    
    // Get approval details first
    const approvalDetailsResult = await client.query(`
      SELECT a.id, a.status as approval_status, a.exception_description,
             a.trip_log_id, tl.status as trip_status, tl.notes as trip_notes
      FROM approvals a
      JOIN trip_logs tl ON a.trip_log_id = tl.id
      WHERE a.id = $1
    `, [approvalId]);
    
    const approval = approvalDetailsResult.rows[0];
    console.log('✅ Found approval details');
    
    // Parse trip notes
    let tripNotes;
    try {
      tripNotes = approval.trip_notes ? JSON.parse(approval.trip_notes) : {};
      console.log('✅ Trip notes parsed:');
      console.log(`   Original assignment: ${tripNotes.original_assignment_id}`);
      console.log(`   Pending assignment: ${tripNotes.pending_assignment_id}`);
      console.log(`   Revised flow: ${tripNotes.revised_flow_pattern}`);
    } catch (e) {
      console.log('❌ Failed to parse trip notes:', e.message);
      tripNotes = {};
    }
    
    // Try to create a third assignment with same truck and locations as assignment2
    // This would be a true duplicate that should be prevented
    console.log('\nCreating potential duplicate assignment (should be detected)...');
    
    // Try to create a duplicate, this should cause an error but not abort our overall test
    let duplicateError = null;
    try {
      await client.query(`
        INSERT INTO assignments (
          truck_id, driver_id, loading_location_id, unloading_location_id,
          status, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [truck.id, driver.id, alternateLocation.id, unloadingLocation.id]);
      
      console.log('🚨 Duplicate assignment created - this should be prevented during approval process');
    } catch(error) {
      duplicateError = error;
      console.log('✅ Expected error creating duplicate:', error.message.substring(0, 100));
    }
    
    console.log('\n✅ Test completed successfully - committing transaction');
    await client.query('COMMIT');
    client.release();
    
    // Create a new transaction for the idempotency test
    client = await getClient();
    
    try {
      await client.query('BEGIN');
      console.log('\nTesting idempotency protection (double approval prevention)...');      // Set up a mock "already approved" state for the trip
      await client.query(`
        UPDATE trip_logs
        SET exception_approved_by = 1, 
            exception_approved_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [tripId]);
      
      console.log('✅ Trip marked as already approved');
      
      // Run a check that would happen at the beginning of handleApprovedException
      const alreadyProcessedCheck = await client.query(`
        SELECT status, exception_approved_at FROM trip_logs WHERE id = $1
      `, [tripId]);
      
      // This is how the check should be performed in the real code
      if (alreadyProcessedCheck.rows.length > 0 && 
          alreadyProcessedCheck.rows[0].exception_approved_at !== null) {
        console.log('✅ Idempotency check correctly detected already processed exception');
        console.log('   System would skip processing to avoid duplicate assignments');
      } else {
        console.log('❌ Idempotency check failed - could allow duplicate processing');
      }      // Before rolling back, update the trip notes to include references in a way that persists
      console.log('\nVerifying assignment references are maintained in trip notes:');
      
      // Update trip_logs to use assignment2 and add references that would be created during approval
      // Use JSON string instead of jsonb_build_object to avoid data type issues
      const notesObj = {
        original_assignment_id: assignment1Id,
        pending_assignment_id: assignment2Id,
        revised_flow_pattern: `${alternateLocation.name} → ${unloadingLocation.name} → ${alternateLocation.name}`,
        deviation_type: 'location_flow',
        approval_timestamp: new Date().toISOString()
      };
      
      await client.query(`
        UPDATE trip_logs 
        SET assignment_id = $1,
            notes = $2
        WHERE id = $3
      `, [
        assignment2Id,
        JSON.stringify(notesObj),
        tripId
      ]);
      
      // Now check if the trip notes are properly maintained
      const tripNotesCheck = await client.query(`
        SELECT notes, assignment_id
        FROM trip_logs
        WHERE id = $1
      `, [tripId]);
      
      if (tripNotesCheck.rows.length > 0 && tripNotesCheck.rows[0].notes) {
        try {
          // Handle both jsonb and text format depending on how notes are stored
          const noteData = typeof tripNotesCheck.rows[0].notes === 'string' 
            ? JSON.parse(tripNotesCheck.rows[0].notes)
            : tripNotesCheck.rows[0].notes;
          
          console.log('✅ Trip notes found and parsed successfully');
          console.log(`   Original assignment: ${noteData.original_assignment_id}`);
          console.log(`   Current assignment: ${tripNotesCheck.rows[0].assignment_id}`);
          console.log(`   Pending assignment: ${noteData.pending_assignment_id}`);
          console.log(`   Revised flow: ${noteData.revised_flow_pattern || noteData.revised_flow}`);
          
          // Now get the assignment location details
          const assignmentRefCheck = await client.query(`
            SELECT a.id, 
                   l1.name as loading_location, 
                   l2.name as unloading_location
            FROM assignments a
            JOIN locations l1 ON a.loading_location_id = l1.id
            JOIN locations l2 ON a.unloading_location_id = l2.id
            WHERE a.id = $1
          `, [assignment2Id]);
          
          if (assignmentRefCheck.rows.length > 0) {
            const assignmentRef = assignmentRefCheck.rows[0];
            console.log(`✅ Assignment ${assignmentRef.id} locations:`);
            console.log(`   Revised flow: ${assignmentRef.loading_location} → ${assignmentRef.unloading_location} → ${assignmentRef.loading_location}`);
          }
        } catch (parseError) {
          console.log('❌ Failed to parse trip notes:', parseError.message);
        }
      } else {
        console.log('❌ Failed to find trip notes with assignment references');
      }
      
      // ROLLBACK since this was just a test
      await client.query('ROLLBACK');
      console.log('\n✅ Test completed and rolled back successfully');
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Idempotency test error:', error);
    } finally {
      client.release();
    }
    
  } catch (error) {
    if (client) {
      await client.query('ROLLBACK');
      client.release();
    }
    console.error('❌ Main test error:', error);
  }
}

testDuplicateAssignmentHandling();
