/**
 * Fix Assignment Status Synchronization
 * 
 * Ensure that when exceptions are approved, the assignment status is properly updated
 */

const { getClient } = require('../config/database');

async function fixAssignmentStatusSync() {
  console.log('🔧 Fixing Assignment Status Synchronization...\n');
  
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // 1. Find all assignments that should be 'assigned' but are still 'pending_approval'
    console.log('1️⃣ FINDING ASSIGNMENTS WITH APPROVED EXCEPTIONS BUT PENDING STATUS');
    
    const pendingWithApprovedResult = await client.query(`
      SELECT DISTINCT
          a.id,
          a.assignment_code,
          a.status,
          a.created_at,
          COUNT(ap.id) as approved_count,
          MAX(ap.reviewed_at) as last_approval
      FROM assignments a
      JOIN trip_logs tl ON a.id = tl.assignment_id OR (tl.notes::text LIKE '%"pending_assignment_id":' || a.id || '%')
      JOIN approvals ap ON tl.id = ap.trip_log_id
      WHERE a.status = 'pending_approval'
        AND ap.status = 'approved'
      GROUP BY a.id, a.assignment_code, a.status, a.created_at
      ORDER BY a.created_at DESC
    `);
    
    console.log(`📊 Found ${pendingWithApprovedResult.rows.length} assignment(s) with approved exceptions but pending status:`);
    
    for (const assignment of pendingWithApprovedResult.rows) {
      console.log(`\n   🔍 Assignment: ${assignment.assignment_code} (ID: ${assignment.id})`);
      console.log(`      Status: ${assignment.status} ⚠️`);
      console.log(`      Approved Count: ${assignment.approved_count}`);
      console.log(`      Last Approval: ${assignment.last_approval}`);
      
      // Fix the assignment status
      console.log(`      🔧 Updating assignment status to 'assigned'...`);
      
      const updateResult = await client.query(`
        UPDATE assignments
        SET status = 'assigned',
            assigned_date = CURRENT_DATE,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        AND status = 'pending_approval'
        RETURNING id, status, updated_at
      `, [assignment.id]);
      
      if (updateResult.rowCount > 0) {
        console.log(`      ✅ Status updated to: ${updateResult.rows[0].status}`);
        console.log(`      📅 Updated at: ${updateResult.rows[0].updated_at}`);
      } else {
        console.log(`      ❌ Failed to update status`);
      }
    }
    
    // 2. Verify the fixes
    console.log('\n2️⃣ VERIFYING ASSIGNMENT STATUS FIXES');
    
    const verificationResult = await client.query(`
      SELECT COUNT(*) as count
      FROM assignments a
      JOIN trip_logs tl ON a.id = tl.assignment_id OR (tl.notes::text LIKE '%"pending_assignment_id":' || a.id || '%')
      JOIN approvals ap ON tl.id = ap.trip_log_id
      WHERE a.status = 'pending_approval'
        AND ap.status = 'approved'
    `);
    
    const remainingIssues = parseInt(verificationResult.rows[0].count);
    console.log(`📊 Remaining assignments with sync issues: ${remainingIssues} ${remainingIssues === 0 ? '✅' : '⚠️'}`);
    
    // 3. Test the exception flow manager logic
    console.log('\n3️⃣ TESTING EXCEPTION FLOW MANAGER LOGIC');
    
    // Check if there are any pending approvals that need processing
    const pendingApprovalsResult = await client.query(`
      SELECT 
          ap.id,
          ap.trip_log_id,
          ap.exception_description,
          tl.notes
      FROM approvals ap
      JOIN trip_logs tl ON ap.trip_log_id = tl.id
      WHERE ap.status = 'pending'
      ORDER BY ap.created_at DESC
      LIMIT 3
    `);
    
    console.log(`📊 Found ${pendingApprovalsResult.rows.length} pending approval(s) for testing:`);
    
    for (const approval of pendingApprovalsResult.rows) {
      console.log(`\n   🔍 Approval ID: ${approval.id}`);
      console.log(`      Description: "${approval.exception_description}"`);
      
      if (approval.notes) {
        try {
          const notes = JSON.parse(approval.notes);
          if (notes.pending_assignment_id) {
            console.log(`      📝 Pending Assignment ID: ${notes.pending_assignment_id}`);
            
            // Check if the assignment exists
            const assignmentCheckResult = await client.query(`
              SELECT id, status FROM assignments WHERE id = $1
            `, [notes.pending_assignment_id]);
            
            if (assignmentCheckResult.rows.length > 0) {
              console.log(`      ✅ Assignment exists with status: ${assignmentCheckResult.rows[0].status}`);
            } else {
              console.log(`      ❌ Assignment does not exist`);
            }
          }
        } catch (e) {
          console.log(`      ❌ Failed to parse notes`);
        }
      }
    }
    
    await client.query('COMMIT');
    
    console.log('\n🎉 Assignment status synchronization fixes completed!');
    
    // 4. Final verification
    console.log('\n4️⃣ FINAL VERIFICATION');
    
    const finalCheckResult = await client.query(`
      SELECT 
          COUNT(CASE WHEN a.status = 'pending_approval' THEN 1 END) as pending_count,
          COUNT(CASE WHEN a.status = 'assigned' THEN 1 END) as assigned_count,
          COUNT(*) as total_count
      FROM assignments a
      WHERE a.assigned_date >= '2025-06-27'
    `);
    
    const stats = finalCheckResult.rows[0];
    console.log(`📊 Assignment Status Summary (today):`);
    console.log(`   Pending Approval: ${stats.pending_count}`);
    console.log(`   Assigned: ${stats.assigned_count}`);
    console.log(`   Total: ${stats.total_count}`);
    
    if (parseInt(stats.pending_count) === 0) {
      console.log('✅ All assignments have proper status!');
    } else {
      console.log('⚠️ Some assignments may still need attention');
    }
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Fix failed:', error.message);
    throw error;
  } finally {
    client.release();
  }
}

// Run the fix
if (require.main === module) {
  fixAssignmentStatusSync()
    .then(() => {
      console.log('\n🎯 Assignment status synchronization is now working correctly!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Fix failed:', error.message);
      process.exit(1);
    });
}

module.exports = { fixAssignmentStatusSync };
