const { query, getClient } = require('../config/database');

async function fixDataIssues() {
  console.log('🔧 Fixing data consistency issues...');

  const client = await getClient();
  
  try {
    await client.query('BEGIN');

    // 1. Fix exception_type format to match validation
    console.log('🔧 Normalizing exception_type values...');
    const updateExceptionTypes = await client.query(`
      UPDATE approvals 
      SET exception_type = CASE 
        WHEN exception_type = 'Route Deviation' THEN 'route_deviation'
        WHEN exception_type = 'Time Violation' THEN 'time_violation'
        WHEN exception_type = 'Equipment Issue' THEN 'equipment_issue'
        WHEN exception_type = 'Weather Delay' THEN 'weather_delay'
        WHEN exception_type = 'Manual Override' THEN 'manual_override'
        WHEN exception_type = 'Other' THEN 'other'
        ELSE exception_type
      END,
      updated_at = CURRENT_TIMESTAMP
      WHERE exception_type IN ('Route Deviation', 'Time Violation', 'Equipment Issue', 'Weather Delay', 'Manual Override', 'Other')
      RETURNING id, exception_type
    `);

    console.log(`✅ Updated ${updateExceptionTypes.rows.length} exception types:`);
    updateExceptionTypes.rows.forEach(row => {
      console.log(`  ID: ${row.id} -> ${row.exception_type}`);
    });

    // 2. Check and fix the pending_assignment_id issue
    console.log('🔍 Checking trip_logs with pending_assignment_id...');
    const tripLogsWithPendingAssignment = await client.query(`
      SELECT id, notes, assignment_id
      FROM trip_logs 
      WHERE notes::text LIKE '%pending_assignment_id%'
    `);

    for (const trip of tripLogsWithPendingAssignment.rows) {
      const notes = JSON.parse(trip.notes || '{}');
      if (notes.pending_assignment_id) {
        console.log(`Trip ${trip.id} has pending_assignment_id: ${notes.pending_assignment_id}`);
        
        // Check if this assignment exists
        const assignmentExists = await client.query(
          'SELECT id FROM assignments WHERE id = $1',
          [notes.pending_assignment_id]
        );

        if (assignmentExists.rows.length === 0) {
          console.log(`❌ Assignment ${notes.pending_assignment_id} doesn't exist. Creating or finding replacement...`);
          
          // Find an existing assignment for the same truck
          const existingAssignment = await client.query(`
            SELECT id FROM assignments 
            WHERE truck_id = (
              SELECT truck_id FROM assignments WHERE id = $1
            )
            AND status = 'assigned'
            LIMIT 1
          `, [trip.assignment_id]);

          if (existingAssignment.rows.length > 0) {
            // Update the notes to reference the existing assignment
            notes.pending_assignment_id = existingAssignment.rows[0].id;
            await client.query(`
              UPDATE trip_logs 
              SET notes = $1,
                  updated_at = CURRENT_TIMESTAMP
              WHERE id = $2
            `, [JSON.stringify(notes), trip.id]);
            
            console.log(`✅ Updated trip ${trip.id} to use assignment ${existingAssignment.rows[0].id}`);
          } else {
            // Remove the pending_assignment_id if no valid assignment exists
            delete notes.pending_assignment_id;
            await client.query(`
              UPDATE trip_logs 
              SET notes = $1,
                  updated_at = CURRENT_TIMESTAMP
              WHERE id = $2
            `, [JSON.stringify(notes), trip.id]);
            
            console.log(`✅ Removed invalid pending_assignment_id from trip ${trip.id}`);
          }
        } else {
          console.log(`✅ Assignment ${notes.pending_assignment_id} exists`);
        }
      }
    }

    await client.query('COMMIT');
    console.log('✅ All data issues fixed successfully!');

    // Verify the fixes
    console.log('\n🔍 Verifying fixes...');
    const verifyApproval = await query(`
      SELECT id, status, exception_type, exception_description
      FROM approvals 
      WHERE id = 3
    `);
    
    if (verifyApproval.rows.length > 0) {
      console.log('✅ Approval 3 after fix:', verifyApproval.rows[0]);
    }

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Error during fix:', error);
    throw error;
  } finally {
    client.release();
  }
}

fixDataIssues().then(() => {
  console.log('\n✅ Data fix completed successfully');
  process.exit(0);
}).catch(error => {
  console.error('\n❌ Data fix failed:', error.message);
  process.exit(1);
});
