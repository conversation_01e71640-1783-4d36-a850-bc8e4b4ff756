# Troubleshooting Guide: Backend/Frontend Errors

## Issues Identified and Fixed

### 1. 404 Errors for Users and Stats Endpoints
**Root Cause**: Missing proxy configuration for development server
**Status**: ✅ FIXED

**Changes Made**:
- Updated `client/src/setupProxy.js` to proxy `/api` requests to `http://localhost:5000`

### 2. 500 Errors for Assignments Endpoints  
**Root Cause**: Database schema mismatch - missing columns
**Status**: ✅ FIXED (migration needed)

**Issues Found**:
- Backend expects `assignment_code` column but it doesn't exist in database
- Backend expects `priority` column but it doesn't exist in database

**Changes Made**:
- Updated `database/init.sql` to include missing columns
- Created migration script to add columns to existing databases
- Created troubleshooting scripts

## How to Apply the Fixes

### Step 1: Apply Database Migration
Choose one option based on your operating system:

**Windows (PowerShell/Command Prompt):**
```cmd
cd database
migrate.bat
```

**Linux/Mac (Bash):**
```bash
cd database
chmod +x migrate.sh
./migrate.sh
```

**Manual SQL (if above scripts fail):**
```sql
-- Connect to your database and run:
ALTER TABLE assignments ADD COLUMN assignment_code VARCHAR(50) UNIQUE;
ALTER TABLE assignments ADD COLUMN priority VARCHAR(20) DEFAULT 'normal' 
    CHECK (priority IN ('low', 'normal', 'high', 'urgent'));

-- Generate codes for existing records
UPDATE assignments SET assignment_code = 'ASG-' || id || '-' || EXTRACT(EPOCH FROM created_at)::bigint
WHERE assignment_code IS NULL;

-- Set default priority
UPDATE assignments SET priority = 'normal' WHERE priority IS NULL;
```

### Step 2: Test Database Schema
```bash
cd database
node test-db.js
```

### Step 3: Restart Both Servers
```bash
# Terminal 1 - Backend
cd server
npm start

# Terminal 2 - Frontend  
cd client
npm start
```

## Verification Steps

1. **Check Database Structure**:
   ```bash
   node database/test-db.js
   ```

2. **Test Backend Health**:
   Visit: `http://localhost:5000/health`
   Should return: `{"status":"OK","message":"Hauling QR Trip Management System API is running",...}`

3. **Test Frontend Proxy**:
   Open browser dev tools and check Network tab when accessing users or assignments pages

4. **Check for Errors**:
   - Backend: Look for SQL errors in server console
   - Frontend: Check browser console for API errors

## Common Issues and Solutions

### Database Connection Issues
- Ensure PostgreSQL is running
- Check DB credentials in `.env` file
- Verify database name exists

### Port Conflicts
- Backend default: port 5000
- Frontend default: port 3000
- Check if ports are available: `netstat -an | findstr :5000`

### Environment Variables
Create `.env` file in server directory:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=your_password
PORT=5000
FRONTEND_URL=http://localhost:3000
```

## Expected Behavior After Fixes

✅ **Users Management Page**: Should load without 404 errors
✅ **User Statistics**: Should display user counts and metrics  
✅ **Assignments Management**: Should load without 500 errors
✅ **Create Assignment**: Should work with assignment code generation
✅ **API Proxy**: Frontend requests should properly route to backend

## Files Modified

1. `client/src/setupProxy.js` - Added API proxy configuration
2. `database/init.sql` - Added missing columns to assignments table
3. `database/migrations/001_add_assignment_code_priority.sql` - Migration script
4. `database/migrate.bat` / `database/migrate.sh` - Migration runners
5. `database/test-db.js` - Database testing utility

## Next Steps

1. Run the migration script
2. Test the application
3. If issues persist, check server logs for detailed error messages
4. Verify all required services (PostgreSQL, Node.js) are running

For additional help, check the server console output and browser developer tools for specific error messages.
