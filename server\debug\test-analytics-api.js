const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testAnalyticsEndpoints() {
  console.log('🧪 Testing Analytics API Endpoints...\n');
  const endpoints = [
    '/analytics/dashboard'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`📡 Testing: ${endpoint}`);
      const response = await axios.get(`${API_BASE}${endpoint}`, {
        timeout: 5000,
        headers: {
          'Authorization': 'Bearer test-token', // You might need a real token
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`✅ Status: ${response.status}`);
      console.log(`📊 Data:`, JSON.stringify(response.data, null, 2));
      console.log('---\n');
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Data:`, error.response.data);
      }
      console.log('---\n');
    }
  }
}

// Test without authentication first
async function testWithoutAuth() {
  console.log('🔓 Testing without authentication...\n');
  
  try {
    const response = await axios.get(`${API_BASE}/analytics/dashboard`, {
      timeout: 5000
    });
    console.log('✅ No auth needed - Status:', response.status);
    console.log('📊 Data:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ Auth required - Error:', error.response?.status, error.response?.data);
    
    // If 401, we need authentication
    if (error.response?.status === 401) {
      console.log('🔐 Authentication required. Testing with login...');
      await testWithLogin();
    }
  }
}

async function testWithLogin() {
  try {
    // First login to get a token
    console.log('🔑 Attempting login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    console.log('✅ Login successful');
    const token = loginResponse.data.token;
    
    // Now test analytics with token
    console.log('📡 Testing analytics with token...');
    const analyticsResponse = await axios.get(`${API_BASE}/analytics/dashboard`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Analytics Status:', analyticsResponse.status);
    console.log('📊 Analytics Data:', JSON.stringify(analyticsResponse.data, null, 2));
    
  } catch (error) {
    console.log('❌ Login/Analytics failed:', error.message);
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    }
  }
}

testWithoutAuth();
