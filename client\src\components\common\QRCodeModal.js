import React from 'react';

const QRCodeModal = ({ qrData, onClose }) => {
  if (!qrData) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Determine the type of QR data (truck or location)
  const isLocationQR = qrData.location_code || qrData.qr_data?.type === 'location';
  const isTruckQR = qrData.truck_number || qrData.qr_data?.type === 'truck';

  // Get display information based on type
  const getDisplayInfo = () => {
    if (isLocationQR) {
      return {
        title: qrData.name || qrData.location_code || 'Location',
        identifier: qrData.location_code || qrData.qr_data?.id || 'Unknown',
        icon: '📍',
        type: 'Location',
        subtitle: 'Scan this QR code to identify location'
      };
    } else if (isTruckQR) {
      return {
        title: qrData.truck_number || qrData.qr_data?.id || 'Truck',
        identifier: qrData.truck_number || qrData.qr_data?.id || 'Unknown',
        icon: '🚛',
        type: 'Truck',
        subtitle: 'Scan this QR code to track truck operations'
      };
    } else {
      return {
        title: 'QR Code',
        identifier: 'Unknown',
        icon: '📱',
        type: 'Unknown',
        subtitle: 'QR Code information'
      };
    }
  };

  const displayInfo = getDisplayInfo();

  const handleDownload = () => {
    // Create a download link for the QR code
    const link = document.createElement('a');
    link.href = qrData.qr_code;
    link.download = `${displayInfo.identifier}_QR_Code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    
    // Get additional data based on type
    let additionalInfo = '';
    if (isLocationQR) {
      additionalInfo = `
        Type: ${qrData.type || 'Location'}<br>
        Address: ${qrData.address || 'N/A'}<br>
        Coordinates: ${qrData.coordinates || 'N/A'}<br>
      `;
    } else if (isTruckQR) {
      additionalInfo = `
        License Plate: ${qrData.license_plate || 'N/A'}<br>
        Route: ${qrData.qr_data?.assigned_route || 'N/A'}<br>
        Capacity: ${qrData.capacity_tons || 'N/A'} tons<br>
      `;
    }

    printWindow.document.write(`
      <html>
        <head>
          <title>QR Code - ${displayInfo.title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 20px;
            }
            .qr-container {
              max-width: 400px;
              margin: 0 auto;
              border: 2px solid #000;
              padding: 20px;
              border-radius: 10px;
            }
            .info {
              margin-bottom: 20px;
              font-size: 18px;
              font-weight: bold;
            }
            .qr-code {
              margin: 20px 0;
            }
            .qr-data {
              margin-top: 20px;
              font-size: 12px;
              color: #666;
              text-align: left;
            }
            @media print {
              body { margin: 0; }
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <div class="info">
              ${displayInfo.icon} ${displayInfo.type}: ${displayInfo.title}
            </div>
            <div class="qr-code">
              <img src="${qrData.qr_code}" alt="QR Code" style="max-width: 200px; height: auto;" />
            </div>
            <div class="qr-data">
              <strong>Information:</strong><br>
              ${additionalInfo}
              <strong>QR Data:</strong><br>
              Type: ${qrData.qr_data?.type || 'Unknown'}<br>
              ID: ${qrData.qr_data?.id || displayInfo.identifier}<br>
              Generated: ${qrData.qr_data?.timestamp ? new Date(qrData.qr_data.timestamp).toLocaleString() : new Date().toLocaleString()}
            </div>
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      {/* Backdrop */}
      <div 
        className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
        onClick={handleBackdropClick}
      >
        <div className="fixed inset-0 bg-secondary-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        {/* Modal */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full sm:p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-secondary-900" id="modal-title">
              QR Code - {displayInfo.title}
            </h3>
            <button
              onClick={onClose}
              className="text-secondary-400 hover:text-secondary-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* QR Code Display */}
          <div className="text-center">
            {/* Item Info */}
            <div className="mb-6">
              <div className="flex items-center justify-center mb-2">
                <span className="text-3xl mr-2">{displayInfo.icon}</span>
                <span className="text-xl font-semibold text-secondary-900">
                  {displayInfo.title}
                </span>
              </div>
              <p className="text-sm text-secondary-600">
                {displayInfo.subtitle}
              </p>
            </div>

            {/* QR Code Image */}
            <div className="bg-white p-4 rounded-lg border-2 border-secondary-200 inline-block mb-6">
              <img 
                src={qrData.qr_code} 
                alt={`QR Code for ${displayInfo.title}`}
                className="w-48 h-48 mx-auto"
              />
            </div>

            {/* QR Data Info */}
            <div className="bg-secondary-50 rounded-lg p-4 mb-6 text-left">
              <h4 className="font-medium text-secondary-900 mb-3">QR Code Data:</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-secondary-600">Type:</span>
                  <span className="font-medium text-secondary-900">{qrData.qr_data?.type || displayInfo.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-600">ID:</span>
                  <span className="font-medium text-secondary-900">{qrData.qr_data?.id || displayInfo.identifier}</span>
                </div>
                {/* Truck-specific data */}
                {isTruckQR && (
                  <>
                    {qrData.license_plate && (
                      <div className="flex justify-between">
                        <span className="text-secondary-600">License Plate:</span>
                        <span className="font-medium text-secondary-900">{qrData.license_plate}</span>
                      </div>
                    )}
                    {qrData.qr_data?.assigned_route && (
                      <div className="flex justify-between">
                        <span className="text-secondary-600">Route:</span>
                        <span className="font-medium text-secondary-900">{qrData.qr_data.assigned_route}</span>
                      </div>
                    )}
                  </>
                )}
                {/* Location-specific data */}
                {isLocationQR && (
                  <>
                    {qrData.type && (
                      <div className="flex justify-between">
                        <span className="text-secondary-600">Location Type:</span>
                        <span className="font-medium text-secondary-900">{qrData.type}</span>
                      </div>
                    )}
                    {qrData.address && (
                      <div className="flex justify-between">
                        <span className="text-secondary-600">Address:</span>
                        <span className="font-medium text-secondary-900 text-right">{qrData.address}</span>
                      </div>
                    )}
                  </>
                )}
                <div className="flex justify-between">
                  <span className="text-secondary-600">Generated:</span>
                  <span className="font-medium text-secondary-900">
                    {qrData.qr_data?.timestamp ? 
                      new Date(qrData.qr_data.timestamp).toLocaleDateString() : 
                      new Date().toLocaleDateString()
                    }
                  </span>
                </div>
              </div>
            </div>
            {/* Action Buttons */}
            <div className="flex items-center justify-center space-x-3">
              <button
                onClick={handleDownload}
                className="btn btn-secondary flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Download
              </button>
              <button
                onClick={handlePrint}
                className="btn btn-primary flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                </svg>
                Print
              </button>
            </div>
            {/* Usage Instructions */}
            <div className="mt-6 p-4 bg-info-50 rounded-lg">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-info-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="text-left">
                  <h5 className="text-sm font-medium text-info-900 mb-1">Usage Instructions:</h5>
                  <ul className="text-xs text-info-800 space-y-1">
                    {isLocationQR ? (
                      <>
                        <li>• Scan first to identify location</li>
                        <li>• Use in two-step scanning workflow</li>
                        <li>• Print and post at location entrance</li>
                        <li>• Tracks trip events automatically</li>
                      </>
                    ) : (
                      <>
                        <li>• Scan at loading/unloading locations</li>
                        <li>• Use mobile app or QR scanner</li>
                        <li>• Tracks trip progress automatically</li>
                        <li>• Print and attach to truck dashboard</li>
                      </>
                    )}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRCodeModal;
