#!/bin/bash

# Hauling QR Trip System - Production Deployment Script
# This script helps deploy the application to production

set -e  # Exit on any error

echo "🚛 Hauling QR Trip System - Production Deployment"
echo "=================================================="

# Check if Docker and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env.production exists
if [ ! -f .env.production ]; then
    echo "⚠️  .env.production file not found!"
    echo "📝 Copying .env.production.example to .env.production"
    cp .env.production.example .env.production
    echo "⚠️  Please edit .env.production with your actual values before proceeding."
    echo "📖 Key variables to update:"
    echo "   - DB_PASSWORD"
    echo "   - JWT_SECRET"
    echo "   - SSL certificate paths (if using HTTPS)"
    read -p "Press Enter after updating .env.production to continue..."
fi

# Load environment variables
source .env.production

echo "🔧 Building Docker images..."
docker-compose build --no-cache

echo "🗄️  Setting up database..."
# Check if PostgreSQL container is running and initialize if needed
if ! docker-compose ps postgres | grep -q "Up"; then
    echo "Starting PostgreSQL container..."
    docker-compose up -d postgres
    echo "Waiting for PostgreSQL to be ready..."
    sleep 10
fi

# Run database migrations/setup
echo "🔄 Running database initialization..."
docker-compose exec -T postgres psql -U postgres -d hauling_qr_system -f /docker-entrypoint-initdb.d/init.sql || echo "Database already initialized"

echo "🚀 Starting all services..."
docker-compose up -d

echo "⏳ Waiting for services to be ready..."
sleep 30

# Health check
echo "🔍 Performing health checks..."
if curl -f http://localhost:5000/api/health &> /dev/null; then
    echo "✅ Backend health check passed"
else
    echo "❌ Backend health check failed"
    echo "📋 Checking logs:"
    docker-compose logs app
    exit 1
fi

echo "📊 Deployment Summary:"
echo "====================="
echo "✅ Application: http://localhost:5000"
echo "✅ Database: PostgreSQL on port 5432"
echo "✅ Redis: Running on port 6379"

if [ "$NODE_ENV" = "production" ]; then
    echo "🔒 Production mode enabled"
    echo "📋 Next steps:"
    echo "   1. Configure your domain in nginx.conf"
    echo "   2. Set up SSL certificates"
    echo "   3. Configure monitoring (optional)"
    echo "   4. Set up backup procedures"
fi

echo ""
echo "🎉 Deployment completed successfully!"
echo "📖 Run 'docker-compose logs -f' to view real-time logs"
echo "🛑 Run 'docker-compose down' to stop all services"
