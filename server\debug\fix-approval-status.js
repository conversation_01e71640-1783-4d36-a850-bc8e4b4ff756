const { query, getClient } = require('../config/database');

async function fixApprovalStatus() {
  console.log('🔧 Fixing approval status issues...');

  const client = await getClient();
  
  try {
    await client.query('BEGIN');

    // Check for approvals with invalid status - we need to be careful about the enum constraint
    console.log('🔍 Checking approval ID 3 specifically...');
    
    // First, let's see what the actual data looks like by bypassing enum validation
    const directQuery = await client.query(`
      SELECT id, trip_log_id, exception_type, created_at, updated_at
      FROM approvals 
      WHERE id = 3
    `);

    if (directQuery.rows.length > 0) {
      console.log('Found approval 3:', directQuery.rows[0]);
      
      // Now try to read the status as text to see what it actually contains
      try {
        const statusQuery = await client.query(`
          SELECT id, status::text as status_text 
          FROM approvals 
          WHERE id = 3
        `);
        console.log('Status as text:', statusQuery.rows[0]);
      } catch (statusError) {
        console.log('❌ Error reading status - this confirms enum violation:', statusError.message);
        
        // The status column contains invalid data, we need to fix it
        console.log('🔧 Attempting to fix invalid status...');
        
        // Update the status to 'pending' which is valid
        const fixResult = await client.query(`
          UPDATE approvals 
          SET status = 'pending'::approval_status,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = 3
          RETURNING id, status
        `);
        
        console.log('✅ Fixed approval status:', fixResult.rows[0]);
      }
    } else {
      console.log('❌ Approval ID 3 not found');
    }

    // Check if there are any other approvals with similar issues
    console.log('\n🔍 Checking for other approvals with potential status issues...');
    try {
      const allApprovals = await client.query(`
        SELECT id, status, exception_type, created_at
        FROM approvals 
        ORDER BY id
      `);
      console.log(`Found ${allApprovals.rows.length} total approvals:`);
      allApprovals.rows.forEach(approval => {
        console.log(`  ID: ${approval.id}, Status: ${approval.status}, Type: ${approval.exception_type}`);
      });
    } catch (error) {
      console.log('❌ Error reading all approvals:', error.message);
      
      // If we can't read all approvals, there might be more invalid data
      console.log('🔧 Attempting to fix all invalid approval statuses...');
      
      // This is a risky operation, but we'll try to update any invalid statuses
      try {
        const massFixResult = await client.query(`
          UPDATE approvals 
          SET status = 'pending'::approval_status,
              updated_at = CURRENT_TIMESTAMP
          WHERE status::text NOT IN ('pending', 'approved', 'rejected')
          RETURNING id, status
        `);
        console.log(`✅ Mass fixed ${massFixResult.rows.length} approvals`);
      } catch (massFixError) {
        console.log('❌ Mass fix failed:', massFixError.message);
      }
    }

    await client.query('COMMIT');
    console.log('✅ Transaction committed successfully');

    // Verify the fix
    console.log('\n🔍 Verifying fix for approval ID 3...');
    const verification = await query(`
      SELECT id, status, exception_type 
      FROM approvals 
      WHERE id = 3
    `);
    
    if (verification.rows.length > 0) {
      console.log('✅ Approval 3 after fix:', verification.rows[0]);
    } else {
      console.log('❌ Approval 3 not found after fix');
    }

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Error during fix:', error);
    throw error;
  } finally {
    client.release();
  }
}

fixApprovalStatus().then(() => {
  console.log('\n✅ Fix completed successfully');
  process.exit(0);
}).catch(error => {
  console.error('\n❌ Fix failed:', error.message);
  process.exit(1);
});
