const { query } = require('../config/database');

async function checkApproval3() {
  try {
    console.log('🔍 Checking approval ID 3...');
    
    // Get the specific approval that's failing
    const approvalResult = await query(`
      SELECT a.*, tl.status as trip_status, tl.trip_number, tl.notes as trip_notes
      FROM approvals a
      JOIN trip_logs tl ON a.trip_log_id = tl.id
      WHERE a.id = 3
    `);

    if (approvalResult.rows.length === 0) {
      console.log('❌ Approval ID 3 not found');
      return;
    }

    const approval = approvalResult.rows[0];
    console.log('📋 Approval ID 3 details:');
    console.log('  Status:', approval.status);
    console.log('  Exception Type:', approval.exception_type);
    console.log('  Exception Description:', approval.exception_description);
    console.log('  Trip Log ID:', approval.trip_log_id);
    console.log('  Trip Status:', approval.trip_status);
    console.log('  Trip Number:', approval.trip_number);
    console.log('  Reviewed By:', approval.reviewed_by);
    console.log('  Created At:', approval.created_at);
    console.log('  Updated At:', approval.updated_at);
    
    if (approval.trip_notes) {
      console.log('  Trip Notes:', approval.trip_notes);
    }

    // Check if there are any validation issues
    console.log('\n🔍 Validation checks:');
    console.log('  Status is valid enum:', ['pending', 'approved', 'rejected'].includes(approval.status));
    console.log('  Exception type is valid:', ['route_deviation', 'time_violation', 'equipment_issue', 'weather_delay', 'manual_override', 'other'].includes(approval.exception_type));
    console.log('  Has trip log:', !!approval.trip_log_id);
    console.log('  Trip status is valid:', ['assigned', 'cancelled', 'exception_pending', 'loading_end', 'loading_start', 'trip_completed', 'unloading_end', 'unloading_start'].includes(approval.trip_status));

    // Simulate the PUT request validation
    console.log('\n🔍 Simulating PUT request with sample payload...');
    const samplePayload = {
      decision: 'approved',
      notes: 'Test approval'
    };
    
    console.log('Sample payload:', samplePayload);
    console.log('Decision is valid:', ['approved', 'rejected'].includes(samplePayload.decision));
    console.log('Notes length is valid:', !samplePayload.notes || samplePayload.notes.length <= 1000);

  } catch (error) {
    console.error('❌ Error:', error);
  }
  process.exit(0);
}

checkApproval3();
