import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import LoadingSpinner from './common/LoadingSpinner';

// Pages - will be created in subsequent steps
const LoginPage = React.lazy(() => import('../pages/auth/LoginPage'));
const DashboardLayout = React.lazy(() => import('./layout/DashboardLayout'));

function AppRoutes() {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <React.Suspense fallback={<LoadingSpinner />}>
      <Routes>
        {!isAuthenticated ? (
          // Public routes
          <>
            <Route path="/login" element={<LoginPage />} />
            <Route path="*" element={<LoginPage />} />
          </>
        ) : (
          // Protected routes
          <>
            <Route path="/*" element={<DashboardLayout />} />
          </>
        )}
      </Routes>
    </React.Suspense>
  );
}

export default AppRoutes;