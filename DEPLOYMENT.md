# Production Deployment Guide

This guide covers deploying the Hauling QR Trip System to production using Docker.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- PostgreSQL database (can be containerized)
- SSL certificates (for HTTPS)
- Domain name (optional)

## Quick Start

1. **<PERSON>lone and prepare the environment:**
```bash
git clone <repository-url>
cd Hauling-QR-Trip-System
cp .env.production.example .env.production
```

2. **Configure environment variables:**
Edit `.env.production` with your production values:
```bash
DB_PASSWORD=your_secure_password
JWT_SECRET=your_super_secret_jwt_key_minimum_32_chars
```

3. **Deploy using the script:**
```bash
# Linux/Mac
chmod +x deploy.sh
./deploy.sh

# Windows
deploy.bat
```

## Manual Deployment

### 1. Environment Setup

Create `.env.production` file with your configuration:

```env
# Database
DB_HOST=postgres
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=your_secure_password

# Application
NODE_ENV=production
PORT=5000
JWT_SECRET=your_super_secret_jwt_key

# Optional: Redis for session management
REDIS_URL=redis://redis:6379
```

### 2. Build and Start Services

```bash
# Build the application
docker-compose build

# Start all services
docker-compose up -d

# Check status
docker-compose ps
```

### 3. Initialize Database

The database will be automatically initialized with the schema from `database/init.sql`.

### 4. Verify Deployment

- Application: http://localhost:5000
- Health check: http://localhost:5000/api/health
- Database: PostgreSQL on port 5432

## SSL/HTTPS Setup

### 1. Obtain SSL Certificate

Option A: Let's Encrypt (recommended)
```bash
# Install certbot
sudo apt-get install certbot

# Obtain certificate
sudo certbot certonly --standalone -d your-domain.com
```

Option B: Self-signed (development only)
```bash
mkdir ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/private.key -out ssl/certificate.crt
```

### 2. Configure Nginx

Update `nginx.conf`:
- Replace `your-domain.com` with your actual domain
- Update SSL certificate paths

### 3. Enable HTTPS

```bash
# Restart nginx service
docker-compose restart nginx
```

## Monitoring and Maintenance

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f app
docker-compose logs -f postgres
```

### Database Backup
```bash
# Create backup
docker-compose exec postgres pg_dump -U postgres hauling_qr_system > backup_$(date +%Y%m%d).sql

# Restore backup
docker-compose exec -T postgres psql -U postgres hauling_qr_system < backup_file.sql
```

### Update Application
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose build app
docker-compose up -d app
```

## Security Considerations

### 1. Environment Variables
- Use strong passwords (minimum 16 characters)
- Generate secure JWT secrets (minimum 32 characters)
- Never commit `.env.production` to version control

### 2. Network Security
- Use firewall to restrict access to necessary ports only
- Enable fail2ban for SSH protection
- Regular security updates

### 3. Database Security
- Use strong database passwords
- Limit database access to application containers only
- Regular database backups

### 4. Application Security
- Keep dependencies updated
- Monitor for security vulnerabilities
- Use HTTPS in production
- Implement rate limiting (configured in nginx.conf)

## Performance Optimization

### 1. Database
- Configure PostgreSQL for production workload
- Set up connection pooling
- Regular VACUUM and ANALYZE operations

### 2. Application
- Monitor memory usage
- Configure appropriate container limits
- Use Redis for session management

### 3. Web Server
- Enable gzip compression (configured in nginx.conf)
- Configure caching headers
- Use CDN for static assets (if needed)

## Troubleshooting

### Common Issues

**Service won't start:**
```bash
# Check logs
docker-compose logs <service-name>

# Check container status
docker-compose ps
```

**Database connection errors:**
```bash
# Verify database is running
docker-compose exec postgres psql -U postgres -l

# Test connection
docker-compose exec app node -e "console.log('DB test')"
```

**Permission issues:**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x deploy.sh
```

### Health Checks

The application includes built-in health checks:
- Application: `GET /api/health`
- Database: Automatic Docker health checks
- Redis: Automatic Docker health checks

## Scaling

For high-traffic deployments:

1. **Load Balancing:** Add multiple app containers
2. **Database:** Consider PostgreSQL clustering
3. **Caching:** Implement Redis caching for frequent queries
4. **CDN:** Use CDN for static assets

## Support

For deployment issues:
1. Check the logs: `docker-compose logs -f`
2. Verify environment configuration
3. Ensure all prerequisites are met
4. Check network connectivity and firewall settings
