const { query } = require('../config/database');

async function checkEnums() {
  try {
    console.log('🔍 Checking valid trip_status enum values...');
    const result = await query(`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'trip_status'
      )
      ORDER BY enumlabel
    `);
    console.log('Valid trip_status values:');
    result.rows.forEach(row => console.log('  -', row.enumlabel));
    
    console.log('\n🔍 Checking valid approval_status enum values...');
    const approvalResult = await query(`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'approval_status'
      )
      ORDER BY enumlabel
    `);
    console.log('Valid approval_status values:');
    approvalResult.rows.forEach(row => console.log('  -', row.enumlabel));
    
  } catch (error) {
    console.error('Error:', error.message);
  }
  process.exit(0);
}

checkEnums();
