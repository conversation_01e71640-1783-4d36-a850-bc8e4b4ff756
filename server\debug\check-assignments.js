const { query } = require('./config/database');

(async () => {
  try {
    console.log('🔍 Checking for existing assignments...');
    
    const result = await query(`
      SELECT 
        a.id, a.assignment_code, a.status, a.assigned_date,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id  
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.assigned_date = CURRENT_DATE
      AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 10
    `);
    
    console.log(`Found ${result.rows.length} assignments for today:`);
    result.rows.forEach((assignment, index) => {
      console.log(`${index + 1}. ${assignment.assignment_code} - ${assignment.truck_number}: ${assignment.loading_location} → ${assignment.unloading_location} [${assignment.status}]`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
})();
