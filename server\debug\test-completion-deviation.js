const { getClient } = require('../config/database');

/**
 * Test script to verify that route deviations at trip completion are properly detected
 * Simulates the exact scenario: DT-100 completes unloading at Point B, then goes to Point C
 */

async function testCompletionRouteDeviation() {
  console.log('🧪 Testing Route Deviation at Trip Completion');
  console.log('='.repeat(60));
  
  let client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    console.log('\n1️⃣ SETUP: Creating test scenario...');
    
    // Get or create test truck
    let truckResult = await client.query(
      'SELECT id, truck_number FROM dump_trucks WHERE truck_number = $1',
      ['DT-100']
    );
    
    if (truckResult.rows.length === 0) {
      console.log('❌ DT-100 not found. Please ensure DT-100 exists in the database.');
      return;
    }
    
    const truck = truckResult.rows[0];
    console.log(`✅ Found truck: ${truck.truck_number} (ID: ${truck.id})`);
    
    // Get test locations
    const locationsResult = await client.query(`
      SELECT id, name, location_code, type 
      FROM locations 
      WHERE name IN ('Point A - Main Loading Site', 'Point B - Primary Dump Site', 'Point C - Secondary Dump Site')
      ORDER BY name
    `);
    
    if (locationsResult.rows.length < 3) {
      console.log('❌ Missing required locations. Need Point A, Point B, and Point C.');
      return;
    }
    
    const pointA = locationsResult.rows.find(l => l.name.includes('Point A'));
    const pointB = locationsResult.rows.find(l => l.name.includes('Point B'));
    const pointC = locationsResult.rows.find(l => l.name.includes('Point C')) || 
                  locationsResult.rows.find(l => l.name.includes('Secondary'));
    
    console.log(`✅ Point A: ${pointA.name} (ID: ${pointA.id})`);
    console.log(`✅ Point B: ${pointB.name} (ID: ${pointB.id})`);
    console.log(`✅ Point C: ${pointC.name} (ID: ${pointC.id})`);
    
    // Get test driver
    const driverResult = await client.query(
      'SELECT id, full_name FROM drivers WHERE status = $1 LIMIT 1',
      ['active']
    );
    
    if (driverResult.rows.length === 0) {
      console.log('❌ No active drivers found.');
      return;
    }
    
    const driver = driverResult.rows[0];
    console.log(`✅ Driver: ${driver.full_name} (ID: ${driver.id})`);
      console.log('\n2️⃣ GETTING OR CREATING ASSIGNMENT: Point A → Point B...');
    
    // First check if assignment already exists for today
    let assignmentResult = await client.query(`
      SELECT id, assignment_code, status
      FROM assignments 
      WHERE truck_id = $1 
        AND loading_location_id = $2 
        AND unloading_location_id = $3
        AND assigned_date = CURRENT_DATE
      ORDER BY created_at DESC
      LIMIT 1
    `, [truck.id, pointA.id, pointB.id]);
    
    let assignment;
    if (assignmentResult.rows.length > 0) {
      assignment = assignmentResult.rows[0];
      console.log(`✅ Found existing assignment: ${assignment.assignment_code} (ID: ${assignment.id})`);
      
      // Update status to in_progress if needed
      if (assignment.status !== 'in_progress') {
        await client.query(`
          UPDATE assignments 
          SET status = 'in_progress', updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
        `, [assignment.id]);
        console.log(`   Updated assignment status to 'in_progress'`);
      }
    } else {
      // Create new assignment: A → B
      assignmentResult = await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, 
          loading_location_id, unloading_location_id,
          assigned_date, status, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'in_progress', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id, assignment_code
      `, [
        `TEST-COMPLETION-${Date.now()}`,
        truck.id,
        driver.id,
        pointA.id,
        pointB.id
      ]);
      
      assignment = assignmentResult.rows[0];
      console.log(`✅ Created new assignment: ${assignment.assignment_code} (ID: ${assignment.id})`);
    }
    console.log(`✅ Created assignment: ${assignment.assignment_code} (ID: ${assignment.id})`);
    console.log(`   Route: ${pointA.name} → ${pointB.name} → ${pointA.name}`);
      console.log('\n3️⃣ SIMULATING COMPLETE TRIP CYCLE...');
    
    // Check if there's already a trip in unloading_end state for this assignment
    let tripResult = await client.query(`
      SELECT id, status, trip_number
      FROM trip_logs 
      WHERE assignment_id = $1 
        AND status = 'unloading_end'
        AND DATE(created_at) = CURRENT_DATE
      ORDER BY created_at DESC
      LIMIT 1
    `, [assignment.id]);
    
    let trip;
    if (tripResult.rows.length > 0) {
      trip = tripResult.rows[0];
      console.log(`✅ Found existing trip in 'unloading_end' state (ID: ${trip.id})`);
    } else {
      // Create a trip that has completed unloading and is ready for completion
      tripResult = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status,
          loading_start_time, loading_end_time,
          unloading_start_time, unloading_end_time,
          actual_loading_location_id, is_exception,
          created_at, updated_at
        ) VALUES ($1, 1, 'unloading_end', $2, $3, $4, $5, $6, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `, [
        assignment.id,
        new Date(Date.now() - 120 * 60 * 1000), // 2 hours ago
        new Date(Date.now() - 100 * 60 * 1000), // 1h 40m ago
        new Date(Date.now() - 80 * 60 * 1000),  // 1h 20m ago  
        new Date(Date.now() - 10 * 60 * 1000),  // 10 minutes ago
        pointA.id
      ]);
      
      trip = tripResult.rows[0];
      console.log(`✅ Created trip in 'unloading_end' state (ID: ${trip.id})`);
    }    console.log('   Status: Unloading completed at Point B, ready for completion scan');
    
    console.log('\n4️⃣ TESTING COMPLETION DEVIATION...');
    console.log(`   Scenario: Truck should return to Point A but goes to Point C instead`);
    
    // Test the scanner logic for this scenario
    console.log(`\n   📍 Expected: Truck completes trip at Point A (assigned loading location)`);
    console.log(`   📍 Actual: Truck scans at Point C (wrong loading location)`);
    
    // This should trigger route deviation detection
    console.log(`\n   🎯 Expected Result:`);
    console.log(`      ✅ Route deviation detected`);
    console.log(`      ✅ Exception created with status 'pending'`);
    console.log(`      ✅ New assignment created: Point C → Point B → Point C`);
    console.log(`      ✅ Original trip remains in 'unloading_end' status`);
    console.log(`      ✅ Admin approval required before proceeding`);
    
    console.log('\n5️⃣ VERIFICATION QUERIES...');
    
    // Check current trip status
    const tripCheckResult = await client.query(
      'SELECT status, is_exception FROM trip_logs WHERE id = $1',
      [trip.id]
    );
    console.log(`   Current trip status: ${tripCheckResult.rows[0].status}`);
    console.log(`   Is exception: ${tripCheckResult.rows[0].is_exception}`);
    
    // Check for any pending approvals
    const approvalsResult = await client.query(`
      SELECT COUNT(*) as pending_count 
      FROM approvals 
      WHERE trip_log_id = $1 AND status = 'pending'
    `, [trip.id]);
    console.log(`   Pending approvals: ${approvalsResult.rows[0].pending_count}`);
    
    console.log('\n✅ Test scenario setup completed!');
    console.log('\n💡 To test the actual scanner logic:');
    console.log('   1. Use the QR scanner to scan Point C location');
    console.log('   2. Then scan DT-100 truck QR code');
    console.log('   3. System should detect route deviation and create exception');
    console.log('   4. Check approvals dashboard for pending exception');
    
    await client.query('ROLLBACK');
    console.log('\n🧹 Test data rolled back');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    client.release();
  }
}

// Run the test
if (require.main === module) {
  testCompletionRouteDeviation()
    .then(() => {
      console.log('\n🎯 Completion Route Deviation Test completed.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testCompletionRouteDeviation };
