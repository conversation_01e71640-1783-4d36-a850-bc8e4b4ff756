/**
 * Run the migration to make assigned_date optional
 */

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');
const config = require('../server/config/database');

async function runMigration() {
  const client = new Client({
    host: config.host,
    port: config.port,
    user: config.user,
    password: config.password,
    database: config.database
  });

  try {
    console.log('Connecting to database...');
    await client.connect();

    const migrationFile = path.join(__dirname, 'migrations', '013_make_assigned_date_optional.sql');
    const sql = fs.readFileSync(migrationFile, 'utf8');

    console.log('Running migration: 013_make_assigned_date_optional.sql');
    console.log('This will make the assigned_date column optional in the assignments table');

    await client.query(sql);

    console.log('✓ Migration completed successfully!');
    console.log('The assigned_date column is now optional in the assignments table');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await client.end();
  }
}

runMigration().catch(console.error);
