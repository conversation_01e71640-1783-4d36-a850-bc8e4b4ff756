/**
 * Check Trip Notes for Assignment Update Issue
 */

const { getClient } = require('../config/database');

async function checkTripNotes() {
  console.log('🔍 Checking Trip Notes...\n');
  
  const client = await getClient();
  
  try {
    // Get trip log 19 details
    const tripResult = await client.query(`
      SELECT 
          tl.id,
          tl.assignment_id,
          tl.notes,
          ap.id as approval_id,
          ap.status as approval_status
      FROM trip_logs tl
      LEFT JOIN approvals ap ON tl.id = ap.trip_log_id
      WHERE tl.id = 19
    `);
    
    if (tripResult.rows.length === 0) {
      console.log('❌ Trip log 19 not found');
      return;
    }
    
    const trip = tripResult.rows[0];
    console.log('📋 Trip Log 19 Details:');
    console.log(`   ID: ${trip.id}`);
    console.log(`   Assignment ID: ${trip.assignment_id}`);
    console.log(`   Approval ID: ${trip.approval_id}`);
    console.log(`   Approval Status: ${trip.approval_status}`);
    
    // Parse trip notes properly
    if (trip.notes) {
      console.log('\n📝 Trip Notes:');
      
      let notes;
      try {
        // Handle different note formats
        if (typeof trip.notes === 'string') {
          notes = JSON.parse(trip.notes);
        } else if (typeof trip.notes === 'object') {
          notes = trip.notes;
        } else {
          console.log(`   Raw notes: ${trip.notes}`);
          return;
        }
        
        console.log(JSON.stringify(notes, null, 2));
        
        // Check for pending_assignment_id
        if (notes.pending_assignment_id) {
          console.log(`\n🎯 Found pending_assignment_id: ${notes.pending_assignment_id}`);
          
          // Check if this matches the pending assignment
          if (notes.pending_assignment_id == 40) {
            console.log('✅ This matches the pending assignment ID 40');
            console.log('🔍 The assignment update logic should have been executed');
            
            // Test the update manually
            console.log('\n🧪 Testing manual assignment update:');
            
            const updateResult = await client.query(`
              UPDATE assignments
              SET status = 'assigned',
                  assigned_date = CURRENT_DATE,
                  updated_at = CURRENT_TIMESTAMP
              WHERE id = $1
              AND status = 'pending_approval'
              RETURNING id, status, updated_at
            `, [notes.pending_assignment_id]);
            
            console.log(`   Rows updated: ${updateResult.rowCount}`);
            if (updateResult.rowCount > 0) {
              console.log(`   New status: ${updateResult.rows[0].status}`);
              console.log(`   Updated at: ${updateResult.rows[0].updated_at}`);
              console.log('   ✅ ASSIGNMENT STATUS FIXED!');
            } else {
              console.log('   ❌ No rows updated - assignment may not be in pending_approval status');
            }
            
          } else {
            console.log(`❌ This does NOT match the pending assignment ID 40`);
            console.log(`   Expected: 40`);
            console.log(`   Found: ${notes.pending_assignment_id}`);
          }
        } else {
          console.log('❌ No pending_assignment_id found in trip notes');
        }
        
        // Check for new_assignment_id
        if (notes.new_assignment_id) {
          console.log(`\n🔗 Found new_assignment_id: ${notes.new_assignment_id}`);
          
          if (notes.new_assignment_id == 40) {
            console.log('✅ This matches the pending assignment ID 40');
          } else {
            console.log(`❌ This does NOT match the pending assignment ID 40`);
          }
        }
        
      } catch (e) {
        console.log(`   ❌ Failed to parse notes: ${e.message}`);
        console.log(`   Raw notes: ${trip.notes}`);
      }
    } else {
      console.log('❌ No trip notes found');
    }
    
  } catch (error) {
    console.error('Check failed:', error.message);
  } finally {
    client.release();
  }
}

checkTripNotes();
