-- Normalize exception types to use the standard format

-- Convert 'Route Deviation' to 'route_deviation'
UPDATE approvals
SET exception_type = 'route_deviation',
    updated_at = CURRENT_TIMESTAMP
WHERE exception_type = 'Route Deviation';

-- Convert 'Time Violation' to 'time_violation'
UPDATE approvals
SET exception_type = 'time_violation',
    updated_at = CURRENT_TIMESTAMP
WHERE exception_type = 'Time Violation';

-- Convert 'Equipment Issue' to 'equipment_issue'
UPDATE approvals
SET exception_type = 'equipment_issue',
    updated_at = CURRENT_TIMESTAMP
WHERE exception_type = 'Equipment Issue';

-- Convert 'Weather Delay' to 'weather_delay'  
UPDATE approvals
SET exception_type = 'weather_delay',
    updated_at = CURRENT_TIMESTAMP
WHERE exception_type = 'Weather Delay';

-- Convert 'Manual Override' to 'manual_override'
UPDATE approvals
SET exception_type = 'manual_override',
    updated_at = CURRENT_TIMESTAMP
WHERE exception_type = 'Manual Override';

-- Convert 'Other' to 'other'
UPDATE approvals
SET exception_type = 'other',
    updated_at = CURRENT_TIMESTAMP
WHERE exception_type = 'Other';
