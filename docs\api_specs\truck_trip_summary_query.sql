SELECT
 dt.truck_number,
 dt.model,
 dt.license_plate,
 dt.capacity_tons,
 ll.name AS loading_location,
 ul.name AS unloading_location,
 DATE(tl.created_at) AS trip_date,
 COUNT(tl.id) AS total_trips,
 MIN(tl.created_at) AS first_trip_time,
 MAX(tl.created_at) AS last_trip_time
FROM
 dump_trucks dt
JOIN
 assignments a ON dt.id = a.truck_id
JOIN
 locations ll ON a.loading_location_id = ll.id
JOIN
 locations ul ON a.unloading_location_id = ul.id
JOIN
 trip_logs tl ON a.id = tl.assignment_id
WHERE
 tl.status = 'trip_completed'
GROUP BY
 dt.truck_number,
 dt.model,
 dt.license_plate,
 dt.capacity_tons,
 ll.name,
 ul.name,
 DATE(tl.created_at)
ORDER BY
 dt.truck_number,
 trip_date DESC;