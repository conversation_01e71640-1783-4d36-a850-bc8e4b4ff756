# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=PostgreSQLPassword

# JWT Configuration
JWT_SECRET=hauling_qr_jwt_secret_2025_secure_key_for_development
JWT_EXPIRY=24h

# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# QR Code Configuration
QR_CODE_SIZE=200
QR_CODE_QUALITY=H

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100