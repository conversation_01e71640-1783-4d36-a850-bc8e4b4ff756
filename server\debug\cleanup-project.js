// File cleanup script for maintaining organized project structure
// Removes temporary test files and moves debug files to appropriate locations

const fs = require('fs').promises;
const path = require('path');

async function cleanupProject() {
  console.log('🧹 Starting project cleanup...');
  
  const filesToCheck = [
    'server/scanner-optimized.js',
    'server/routes/scanner-optimized.js', 
    'server/routes/approvals-optimized.js',
    'server/test-analytics-api.js',
    'server/check-analytics-data.js',
    'server/check-schema.js',
    'server/check-tables.js'
  ];
  
  let cleanedFiles = 0;
  
  for (const file of filesToCheck) {
    const fullPath = path.join(process.cwd(), file);
    
    try {
      await fs.access(fullPath);
      console.log(`🗑️  Found file: ${file}`);
      
      // Check if it's a temporary/test file
      if (file.includes('test-') || file.includes('check-') || file.includes('-optimized')) {
        // Move to debug folder or delete if not needed
        const debugPath = path.join(path.dirname(fullPath), 'debug', path.basename(file));
        
        try {
          await fs.mkdir(path.dirname(debugPath), { recursive: true });
          await fs.rename(fullPath, debugPath);
          console.log(`📁 Moved ${file} to debug folder`);
          cleanedFiles++;
        } catch (moveError) {
          console.log(`⚠️  Could not move ${file}: ${moveError.message}`);
        }
      }
    } catch (error) {
      // File doesn't exist, skip
    }
  }
  
  // Check for any other temporary files
  const tempPatterns = [
    '*.tmp',
    '*.temp', 
    '*-backup.*',
    '*-test.*'
  ];
  
  console.log(`\n✅ Cleanup completed! Processed ${cleanedFiles} files.`);
  console.log('📂 Project structure is now organized:');
  console.log('   - Production files in main directories');
  console.log('   - Debug/test files in debug folders');
  console.log('   - Migration files in database/migrations');
  console.log('   - Test files in server/tests');
}

async function validateProjectStructure() {
  console.log('\n🔍 Validating project structure...');
  
  const requiredDirectories = [
    'server/routes',
    'server/debug', 
    'server/tests',
    'database/migrations',
    'client/src/components'
  ];
  
  for (const dir of requiredDirectories) {
    try {
      await fs.access(dir);
      console.log(`✅ ${dir} exists`);
    } catch (error) {
      console.log(`❌ ${dir} missing`);
    }
  }
  
  console.log('\n🎯 Key implementation files:');
  const keyFiles = [
    'server/routes/scanner.js - Enhanced with unassigned trip handling',
    'server/routes/assignments.js - Updated with duplicate prevention & rate calculation',
    'database/migrations/009_add_driver_rate_column.sql - Database schema update',
    'server/tests/enhanced-scanner.test.js - Test suite for new features'
  ];
  
  keyFiles.forEach(file => console.log(`   📄 ${file}`));
}

if (require.main === module) {
  cleanupProject()
    .then(() => validateProjectStructure())
    .then(() => {
      console.log('\n🚀 Project is ready for testing!');
      console.log('\nNext steps:');
      console.log('1. Run database migrations');
      console.log('2. Restart the server');
      console.log('3. Test the enhanced scanner functionality');
      console.log('4. Verify assignment creation with rate calculation');
    })
    .catch(error => {
      console.error('Cleanup failed:', error);
      process.exit(1);
    });
}

module.exports = { cleanupProject, validateProjectStructure };
