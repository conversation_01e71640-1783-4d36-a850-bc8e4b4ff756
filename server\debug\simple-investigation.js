/**
 * Simple Investigation of Real Issues
 */

const { getClient } = require('../config/database');

async function simpleInvestigation() {
  console.log('🔍 Simple Investigation of Real Issues...\n');
  
  const client = await getClient();
  
  try {
    // 1. Check pending assignments with approved exceptions
    console.log('1️⃣ CHECKING PENDING ASSIGNMENTS WITH APPROVED EXCEPTIONS');
    const pendingResult = await client.query(`
      SELECT DISTINCT
          a.id,
          a.assignment_code,
          a.status,
          COUNT(ap.id) as approved_count
      FROM assignments a
      JOIN trip_logs tl ON a.id = tl.assignment_id
      JOIN approvals ap ON tl.id = ap.trip_log_id
      WHERE a.status = 'pending_approval'
        AND ap.status = 'approved'
      GROUP BY a.id, a.assignment_code, a.status
    `);
    
    console.log(`Found ${pendingResult.rows.length} assignments still pending despite approved exceptions:`);
    pendingResult.rows.forEach((row, i) => {
      console.log(`${i+1}. ${row.assignment_code}: ${row.status} (${row.approved_count} approved)`);
    });
    
    // 2. Check location name retrieval
    console.log('\n2️⃣ CHECKING LOCATION NAME RETRIEVAL');
    const locationResult = await client.query(`
      SELECT 
          a.assignment_code,
          a.loading_location_id,
          ll.name as loading_location_name,
          a.unloading_location_id,
          ul.name as unloading_location_name
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.assigned_date >= '2025-06-27'
      ORDER BY a.created_at DESC
      LIMIT 3
    `);
    
    console.log('Recent assignment location data:');
    locationResult.rows.forEach((row, i) => {
      console.log(`${i+1}. ${row.assignment_code}:`);
      console.log(`   Loading: ID ${row.loading_location_id} → "${row.loading_location_name || 'NULL'}"`);
      console.log(`   Unloading: ID ${row.unloading_location_id} → "${row.unloading_location_name || 'NULL'}"`);
    });
    
    // 3. Check undefined messages
    console.log('\n3️⃣ CHECKING UNDEFINED MESSAGES');
    const undefinedResult = await client.query(`
      SELECT id, exception_description, created_at
      FROM approvals
      WHERE exception_description LIKE '%undefined%'
      ORDER BY created_at DESC
      LIMIT 3
    `);
    
    console.log(`Found ${undefinedResult.rows.length} approvals with undefined messages:`);
    undefinedResult.rows.forEach((row, i) => {
      console.log(`${i+1}. ID ${row.id}: "${row.exception_description}"`);
    });
    
  } catch (error) {
    console.error('Investigation failed:', error.message);
  } finally {
    client.release();
  }
}

simpleInvestigation();
