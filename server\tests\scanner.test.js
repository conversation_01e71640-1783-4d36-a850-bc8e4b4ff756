const request = require('supertest');
const { query, getClient } = require('../config/database');
const app = require('../server');
const jwt = require('jsonwebtoken');

// Mock user for authentication
const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  role: 'operator'
};

const authToken = jwt.sign(mockUser, process.env.JWT_SECRET || 'test-secret');

// Test data
const testLocationQR = {
  type: 'location',
  id: 'LOC-001',
  name: 'Point A',
  coordinates: '40.7128,-74.0060'
};

const testTruckQR = {
  type: 'truck',
  id: 'DT-100',
  assigned_route: 'A-B',
  driver_id: 'DR-001'
};

describe('Scanner Route Tests', () => {
  let testTripId;
  let testAssignmentId;

  beforeAll(async () => {
    // Clean up test data
    await query('DELETE FROM scan_logs WHERE scanner_user_id = $1', [mockUser.id]);
    await query('DELETE FROM approvals WHERE reported_by = $1', [mockUser.id]);
    await query('DELETE FROM trip_logs WHERE trip_number >= 9999');
  });

  afterAll(async () => {
    // Clean up test data
    if (testTripId) {
      await query('DELETE FROM scan_logs WHERE trip_log_id = $1', [testTripId]);
      await query('DELETE FROM approvals WHERE trip_log_id = $1', [testTripId]);
      await query('DELETE FROM trip_logs WHERE id = $1', [testTripId]);
    }
  });

  describe('POST /api/scanner/scan - Location Scan', () => {
    test('should successfully scan a valid location', async () => {
      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'location',
          scanned_data: JSON.stringify(testLocationQR),
          ip_address: '127.0.0.1',
          user_agent: 'Test Agent'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('scanned successfully');
      expect(response.body.data.location).toBeDefined();
      expect(response.body.data.location.code).toBe('LOC-001');
      expect(response.body.next_step).toBe('scan_truck');
    });

    test('should fail with invalid location QR data', async () => {
      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'location',
          scanned_data: 'invalid-json',
          ip_address: '127.0.0.1'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid QR Code');
    });

    test('should fail with mismatched QR type', async () => {
      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'location',
          scanned_data: JSON.stringify(testTruckQR),
          ip_address: '127.0.0.1'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('QR Type Mismatch');
    });

    test('should fail with inactive location', async () => {
      const inactiveLocationQR = { ...testLocationQR, id: 'LOC-999' };
      
      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'location',
          scanned_data: JSON.stringify(inactiveLocationQR),
          ip_address: '127.0.0.1'
        });

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('not found or inactive');
    });
  });

  describe('POST /api/scanner/scan - Truck Scan', () => {
    test('should start new trip at assigned loading location', async () => {
      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'truck',
          scanned_data: JSON.stringify(testTruckQR),
          location_scan_data: testLocationQR,
          ip_address: '127.0.0.1',
          user_agent: 'Test Agent'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Loading started');
      expect(response.body.data.trip).toBeDefined();
      expect(response.body.data.trip.status).toBe('loading_start');
      expect(response.body.next_step).toBe('scan_loading_end');
      
      testTripId = response.body.data.trip.id;
    });

    test('should create exception for route deviation', async () => {
      // Create a test assignment for truck DT-101
      const assignmentResult = await query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, 
          loading_location_id, unloading_location_id, 
          assigned_date, status
        )
        VALUES ('TEST-ASG-001', 2, 2, 1, 2, CURRENT_DATE, 'assigned')
        RETURNING id
      `);
      testAssignmentId = assignmentResult.rows[0].id;

      // Scan at wrong location (Point C instead of Point A)
      const wrongLocationQR = {
        type: 'location',
        id: 'LOC-003',
        name: 'Point C'
      };

      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'truck',
          scanned_data: JSON.stringify({ ...testTruckQR, id: 'DT-101' }),
          location_scan_data: wrongLocationQR,
          ip_address: '127.0.0.1',
          user_agent: 'Test Agent'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Route deviation detected');
      expect(response.body.message).toContain('requires approval');
      expect(response.body.data.trip.status).toBe('exception_pending');
      expect(response.body.next_step).toBe('await_approval');

      // Verify exception was created
      const exceptionResult = await query(
        'SELECT * FROM approvals WHERE trip_log_id = $1',
        [response.body.data.trip.id]
      );
      expect(exceptionResult.rows.length).toBe(1);
      expect(exceptionResult.rows[0].exception_type).toBe('Route Deviation');
      expect(exceptionResult.rows[0].status).toBe('pending');

      // Clean up
      await query('DELETE FROM approvals WHERE trip_log_id = $1', [response.body.data.trip.id]);
      await query('DELETE FROM trip_logs WHERE id = $1', [response.body.data.trip.id]);
      await query('DELETE FROM assignments WHERE id = $1', [testAssignmentId]);
    });

    test('should fail with no active assignment', async () => {
      const unassignedTruckQR = { ...testTruckQR, id: 'DT-999' };
      
      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'truck',
          scanned_data: JSON.stringify(unassignedTruckQR),
          location_scan_data: testLocationQR,
          ip_address: '127.0.0.1'
        });

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('not found or inactive');
    });
  });

  describe('Trip State Transitions', () => {
    let tripId;
    let assignmentId;

    beforeAll(async () => {
      // Create test assignment
      const assignmentResult = await query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, 
          loading_location_id, unloading_location_id, 
          assigned_date, status
        )
        VALUES ('TEST-ASG-002', 1, 1, 1, 2, CURRENT_DATE, 'assigned')
        RETURNING id
      `);
      assignmentId = assignmentResult.rows[0].id;

      // Create test trip
      const tripResult = await query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status, 
          loading_start_time, actual_loading_location_id
        )
        VALUES ($1, 9999, 'loading_start', CURRENT_TIMESTAMP, 1)
        RETURNING id
      `, [assignmentId]);
      tripId = tripResult.rows[0].id;
    });

    afterAll(async () => {
      await query('DELETE FROM trip_logs WHERE id = $1', [tripId]);
      await query('DELETE FROM assignments WHERE id = $1', [assignmentId]);
    });

    test('should complete loading at same location', async () => {
      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'truck',
          scanned_data: JSON.stringify(testTruckQR),
          location_scan_data: testLocationQR,
          ip_address: '127.0.0.1'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Loading completed');
      expect(response.body.data.trip.status).toBe('loading_end');
      expect(response.body.next_step).toBe('travel_to_unloading');
    });

    test('should start unloading at assigned location', async () => {
      // Update trip status to loading_end
      await query(
        'UPDATE trip_logs SET status = $1, loading_end_time = CURRENT_TIMESTAMP WHERE id = $2',
        ['loading_end', tripId]
      );

      const unloadingLocationQR = {
        type: 'location',
        id: 'LOC-002',
        name: 'Point B'
      };

      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'truck',
          scanned_data: JSON.stringify(testTruckQR),
          location_scan_data: unloadingLocationQR,
          ip_address: '127.0.0.1'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Arrived at unloading location');
      expect(response.body.data.trip.status).toBe('unloading_start');
      expect(response.body.next_step).toBe('scan_unloading_end');
    });

    test('should complete unloading', async () => {
      // Update trip status to unloading_start
      await query(
        'UPDATE trip_logs SET status = $1, unloading_start_time = CURRENT_TIMESTAMP WHERE id = $2',
        ['unloading_start', tripId]
      );

      const unloadingLocationQR = {
        type: 'location',
        id: 'LOC-002',
        name: 'Point B'
      };

      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'truck',
          scanned_data: JSON.stringify(testTruckQR),
          location_scan_data: unloadingLocationQR,
          ip_address: '127.0.0.1'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Unloading completed');
      expect(response.body.data.trip.status).toBe('unloading_end');
      expect(response.body.next_step).toBe('return_to_loading_or_new_trip');
    });

    test('should complete trip at loading location', async () => {
      // Update trip status to unloading_end
      await query(
        'UPDATE trip_logs SET status = $1, unloading_end_time = CURRENT_TIMESTAMP WHERE id = $2',
        ['unloading_end', tripId]
      );

      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'truck',
          scanned_data: JSON.stringify(testTruckQR),
          location_scan_data: testLocationQR,
          ip_address: '127.0.0.1'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Trip completed successfully');
      expect(response.body.data.trip.status).toBe('trip_completed');
      expect(response.body.next_step).toBe('trip_complete');
    });
  });

  describe('GET /api/scanner/status/:tripId', () => {
    test('should get trip status successfully', async () => {
      const response = await request(app)
        .get(`/api/scanner/status/${testTripId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(testTripId);
    });

    test('should return 404 for non-existent trip', async () => {
      const response = await request(app)
        .get('/api/scanner/status/99999')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Trip not found');
    });
  });

  describe('Transaction Management', () => {
    test('should rollback on error during scan', async () => {
      // Force an error by using invalid truck data
      const invalidTruckQR = {
        type: 'truck',
        id: null // This will cause an error
      };

      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'truck',
          scanned_data: JSON.stringify(invalidTruckQR),
          location_scan_data: testLocationQR,
          ip_address: '127.0.0.1'
        });

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);

      // Verify failed scan was logged
      const scanLogResult = await query(
        'SELECT * FROM scan_logs WHERE scanner_user_id = $1 AND is_valid = false ORDER BY created_at DESC LIMIT 1',
        [mockUser.id]
      );
      expect(scanLogResult.rows.length).toBeGreaterThan(0);
      expect(scanLogResult.rows[0].validation_error).toBeDefined();
    });
  });

  describe('Performance Tests', () => {
    test('should process scan within acceptable time', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/scanner/scan')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          scan_type: 'location',
          scanned_data: JSON.stringify(testLocationQR),
          ip_address: '127.0.0.1'
        });

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(response.status).toBe(200);
      expect(processingTime).toBeLessThan(1000); // Should process within 1 second
      expect(response.body.processing_time_ms).toBeDefined();
      expect(response.body.processing_time_ms).toBeLessThan(1000);
    });
  });
});

describe('Exception Handling Tests', () => {
  let tripId;
  let approvalId;
  let assignmentId;

  beforeAll(async () => {
    // Create test assignment
    const assignmentResult = await query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id, 
        loading_location_id, unloading_location_id, 
        assigned_date, status
      )
      VALUES ('TEST-EXC-001', 1, 1, 1, 2, CURRENT_DATE, 'assigned')
      RETURNING id
    `);
    assignmentId = assignmentResult.rows[0].id;
  });

  afterAll(async () => {
    if (approvalId) {
      await query('DELETE FROM approvals WHERE id = $1', [approvalId]);
    }
    if (tripId) {
      await query('DELETE FROM trip_logs WHERE id = $1', [tripId]);
    }
    await query('DELETE FROM assignments WHERE id = $1', [assignmentId]);
  });

  test('should create and handle route deviation exception', async () => {
    // Step 1: Create exception by scanning at wrong location
    const wrongLocationQR = {
      type: 'location',
      id: 'LOC-003',
      name: 'Point C'
    };

    const scanResponse = await request(app)
      .post('/api/scanner/scan')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        scan_type: 'truck',
        scanned_data: JSON.stringify(testTruckQR),
        location_scan_data: wrongLocationQR,
        ip_address: '127.0.0.1'
      });

    expect(scanResponse.status).toBe(200);
    expect(scanResponse.body.message).toContain('Route deviation detected');
    
    tripId = scanResponse.body.data.trip.id;

    // Step 2: Verify exception was created
    const approvalResult = await query(
      'SELECT * FROM approvals WHERE trip_log_id = $1',
      [tripId]
    );
    expect(approvalResult.rows.length).toBe(1);
    approvalId = approvalResult.rows[0].id;

    // Step 3: Approve the exception (simulating admin action)
    const adminToken = jwt.sign({ ...mockUser, role: 'admin' }, process.env.JWT_SECRET || 'test-secret');
    
    const approveResponse = await request(app)
      .put(`/api/approvals/${approvalId}`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        decision: 'approved',
        notes: 'Approved for testing'
      });

    expect(approveResponse.status).toBe(200);
    expect(approveResponse.body.success).toBe(true);

    // Step 4: Verify trip can continue after approval
    const continueResponse = await request(app)
      .post('/api/scanner/scan')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        scan_type: 'truck',
        scanned_data: JSON.stringify(testTruckQR),
        location_scan_data: wrongLocationQR,
        ip_address: '127.0.0.1'
      });

    expect(continueResponse.status).toBe(200);
    expect(continueResponse.body.data.trip.status).toBe('loading_end');
  });
});