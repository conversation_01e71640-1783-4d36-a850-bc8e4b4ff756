/**
 * Purpose: Check and verify the flow logic constraints in the QR Trip System
 * Validates that:
 * 1. Assignments must be created by admins, not by the scanner
 * 2. Scanner only uses existing assignments and doesn't auto-create them
 * 3. Duplicate assignments are prevented
 */
const { Pool } = require('pg');
const config = require('../config/database');
const pool = new Pool(config);

async function checkFlowLogicConstraints() {
  const client = await pool.connect();
  console.log('📊 FLOW LOGIC CONSTRAINTS CHECK');
  console.log('===============================');

  try {
    // 1. Check recent scanner actions - were any assignments auto-created?
    console.log('\n1️⃣ CHECKING AUTO-CREATED ASSIGNMENTS');
    const autoAssignmentResult = await client.query(`
      SELECT 
        a.id, a.assignment_code, a.status, a.notes,
        dt.truck_number,
        ll.name as loading_location, ul.name as unloading_location,
        a.created_at
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.notes LIKE '%auto%' OR a.notes LIKE '%Auto%'
      ORDER BY a.created_at DESC
      LIMIT 10
    `);

    if (autoAssignmentResult.rows.length > 0) {
      console.log(`⚠️ Found ${autoAssignmentResult.rows.length} potentially auto-created assignments:`);
      autoAssignmentResult.rows.forEach(row => {
        console.log(`   - Assignment ${row.assignment_code} (${row.loading_location} → ${row.unloading_location})`);
        console.log(`     Truck: ${row.truck_number}, Status: ${row.status}, Created: ${row.created_at}`);
        console.log(`     Notes: ${row.notes}`);
        console.log();
      });
    } else {
      console.log('✅ No auto-created assignments found');
    }

    // 2. Check scan logs that encountered the "no assignment found" error
    console.log('\n2️⃣ CHECKING SCAN LOGS WITH ASSIGNMENT ERRORS');
    const noAssignmentScans = await client.query(`
      SELECT 
        sl.id, sl.scan_type, sl.scan_data, sl.error_message, sl.created_at,
        CASE
          WHEN sl.truck_id IS NOT NULL THEN dt.truck_number
          ELSE 'N/A'
        END as truck_number,
        CASE
          WHEN sl.location_id IS NOT NULL THEN l.name
          ELSE 'N/A'
        END as location_name
      FROM scan_logs sl
      LEFT JOIN dump_trucks dt ON sl.truck_id = dt.id
      LEFT JOIN locations l ON sl.location_id = l.id
      WHERE sl.error_message LIKE '%No assignment found%'
      ORDER BY sl.created_at DESC
      LIMIT 10
    `);

    if (noAssignmentScans.rows.length > 0) {
      console.log(`ℹ️ Found ${noAssignmentScans.rows.length} scans with "No assignment found" error:`);
      noAssignmentScans.rows.forEach(row => {
        console.log(`   - Scan ID ${row.id} at ${row.created_at}`);
        console.log(`     Truck: ${row.truck_number}, Location: ${row.location_name}`);
        console.log(`     Error: ${row.error_message}`);
        console.log();
      });
    } else {
      console.log('ℹ️ No "No assignment found" errors in recent scan logs');
    }

    // 3. Check recent trips with exceptions
    console.log('\n3️⃣ CHECKING RECENT EXCEPTION TRIPS');
    const exceptionTrips = await client.query(`
      SELECT 
        t.id, t.status, t.is_exception, t.exception_reason,
        a.assignment_code, 
        dt.truck_number,
        l.name as location_name,
        t.created_at
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations l ON t.actual_loading_location_id = l.id
      WHERE t.is_exception = true
      ORDER BY t.created_at DESC
      LIMIT 10
    `);

    if (exceptionTrips.rows.length > 0) {
      console.log(`ℹ️ Found ${exceptionTrips.rows.length} recent exception trips:`);
      exceptionTrips.rows.forEach(row => {
        console.log(`   - Trip ID ${row.id} at ${row.created_at}`);
        console.log(`     Truck: ${row.truck_number}, Location: ${row.location_name}`);
        console.log(`     Status: ${row.status}, Reason: ${row.exception_reason}`);
        console.log();
      });
    } else {
      console.log('ℹ️ No recent exception trips found');
    }

    // 4. Check for assignment duplicates (which should be prevented)
    console.log('\n4️⃣ CHECKING FOR DUPLICATE ASSIGNMENTS');
    const duplicateAssignments = await client.query(`
      SELECT
        truck_id, loading_location_id, unloading_location_id, assigned_date, 
        COUNT(*) as count,
        STRING_AGG(id::text, ', ') as assignment_ids,
        STRING_AGG(status, ', ') as statuses
      FROM assignments
      WHERE status IN ('assigned', 'in_progress')
        AND assigned_date >= CURRENT_DATE - INTERVAL '7 days'
      GROUP BY truck_id, loading_location_id, unloading_location_id, assigned_date
      HAVING COUNT(*) > 1
      ORDER BY assigned_date DESC
    `);

    if (duplicateAssignments.rows.length > 0) {
      console.log(`❌ Found ${duplicateAssignments.rows.length} duplicate assignment combinations:`);
      for (const dup of duplicateAssignments.rows) {
        // Get more details about these duplicates
        const detailsResult = await client.query(`
          SELECT 
            a.id, a.assignment_code, a.status, a.created_at,
            dt.truck_number,
            ll.name as loading_location, ul.name as unloading_location
          FROM assignments a
          JOIN dump_trucks dt ON a.truck_id = dt.id
          JOIN locations ll ON a.loading_location_id = ll.id
          JOIN locations ul ON a.unloading_location_id = ul.id
          WHERE a.id IN (${dup.assignment_ids})
          ORDER BY a.created_at
        `);
        
        console.log(`   • Duplicate set for truck ${detailsResult.rows[0].truck_number} on ${dup.assigned_date}:`);
        console.log(`     Route: ${detailsResult.rows[0].loading_location} → ${detailsResult.rows[0].unloading_location}`);
        console.log(`     Count: ${dup.count} assignments with statuses: ${dup.statuses}`);
        console.log('     Assignments:');
        detailsResult.rows.forEach(row => {
          console.log(`       - ID ${row.id} (${row.assignment_code}): Status ${row.status}, Created ${row.created_at}`);
        });
        console.log();
      }
    } else {
      console.log('✅ No duplicate assignments found - constraint is working properly');
    }

    // 5. Check if there are route deviations that successfully created new assignments
    console.log('\n5️⃣ CHECKING APPROVED ROUTE DEVIATIONS');
    const approvedDeviations = await client.query(`
      SELECT 
        a.id as approval_id, a.status as approval_status, a.exception_type,
        t.id as trip_id, t.status as trip_status, t.exception_reason,
        dt.truck_number, l.name as location_name,
        a.created_at as requested_at, a.reviewed_at
      FROM approvals a
      JOIN trip_logs t ON a.trip_log_id = t.id
      JOIN assignments asg ON t.assignment_id = asg.id
      JOIN dump_trucks dt ON asg.truck_id = dt.id
      LEFT JOIN locations l ON t.actual_loading_location_id = l.id
      WHERE a.exception_type = 'Route Deviation'
        AND a.status = 'approved'
      ORDER BY a.created_at DESC
      LIMIT 5
    `);

    if (approvedDeviations.rows.length > 0) {
      console.log(`ℹ️ Found ${approvedDeviations.rows.length} approved route deviations:`);
      approvedDeviations.rows.forEach(row => {
        console.log(`   - Approval ID ${row.approval_id} (${row.exception_type})`);
        console.log(`     Truck: ${row.truck_number}, Location: ${row.location_name}`);
        console.log(`     Trip Status: ${row.trip_status}, Reason: ${row.exception_reason}`);
        console.log(`     Requested: ${row.requested_at}, Approved: ${row.reviewed_at}`);
        console.log();
      });
    } else {
      console.log('ℹ️ No approved route deviations found');
    }

    // 6. Verify the current flow logic implementation in scanner.js
    console.log('\n6️⃣ VERIFYING FLOW LOGIC IMPLEMENTATION');
    
    // Count total assignments in the system
    const totalAssignments = await client.query(`
      SELECT COUNT(*) FROM assignments WHERE assigned_date >= CURRENT_DATE - INTERVAL '30 days'
    `);
    
    // Count admin-created assignments (ones without auto-creation notes)
    const adminAssignments = await client.query(`
      SELECT COUNT(*) FROM assignments 
      WHERE (notes IS NULL OR (notes NOT LIKE '%auto%' AND notes NOT LIKE '%Auto%'))
      AND assigned_date >= CURRENT_DATE - INTERVAL '30 days'
    `);
    
    // Calculate percentages
    const total = parseInt(totalAssignments.rows[0].count);
    const adminCreated = parseInt(adminAssignments.rows[0].count);
    const autoCreated = total - adminCreated;
    const autoPercent = total > 0 ? (autoCreated / total * 100).toFixed(1) : 0;
    
    console.log(`📊 Assignment Creation Stats (last 30 days):`);
    console.log(`   - Total Assignments: ${total}`);
    console.log(`   - Admin Created: ${adminCreated} (${(100 - autoPercent).toFixed(1)}%)`);
    console.log(`   - Auto Created: ${autoCreated} (${autoPercent}%)`);
    
    if (autoCreated > 0 && autoPercent > 10) {
      console.log('\n⚠️ WARNING: Auto-created assignments exceed 10% of total assignments.');
      console.log('   This suggests the scanner may be creating assignments when it should not be.');
    } else if (autoCreated > 0) {
      console.log('\nℹ️ Auto-created assignments are within acceptable range for exceptions.');
    } else {
      console.log('\n✅ All assignments are admin-created - correct flow is being followed.');
    }

    // Overall assessment
    console.log('\n🔍 FLOW LOGIC ASSESSMENT');
    console.log('=========================');
    
    if (duplicateAssignments.rows.length > 0) {
      console.log('❌ FAIL: Duplicate assignments detected - constraint is not working properly');
    } else {
      console.log('✅ PASS: Duplicate prevention is working properly');
    }
    
    if (autoCreated > 0 && autoPercent > 10) {
      console.log('❌ FAIL: Too many auto-created assignments - scanner should not create assignments');
    } else {
      console.log('✅ PASS: Assignment creation flow appears correct');
    }
    
    console.log('\nRecommendations:');
    if (duplicateAssignments.rows.length > 0 || (autoCreated > 0 && autoPercent > 10)) {
      console.log('1. Review scanner.js to ensure it never creates assignments during normal operation');
      console.log('2. Confirm all assignment creation is done through admin interfaces');
      console.log('3. Verify duplicate prevention constraints are properly implemented');
      console.log('4. Check that route deviation/exception handling is explicitly approved by admins');
    } else {
      console.log('✅ Flow logic constraints are properly implemented and working correctly');
    }

  } catch (error) {
    console.error('Error during flow logic check:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the checks
checkFlowLogicConstraints().catch(err => {
  console.error('Error running flow logic check:', err);
  process.exit(1);
});
