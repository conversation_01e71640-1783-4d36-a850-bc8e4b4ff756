import React from 'react';

const ExceptionsReport = ({ data, loading, dateRange }) => {
  if (loading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-secondary-200 h-32 rounded-lg"></div>
          ))}
        </div>
        <div className="bg-secondary-200 h-64 rounded-lg"></div>
      </div>
    );
  }

  // Enhanced error handling - Use REAL data from API with proper fallbacks
  const exceptionsData = data || {
    summary: {
      total: 0,
      pending: 0,
      resolved: 0,
      rate: '0.0'
    },
    recent: [],
    trends: []
  };

  const formatDateRange = () => {
    try {
      const start = new Date(dateRange.start).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const end = new Date(dateRange.end).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      return `${start} - ${end}`;
    } catch (error) {
      console.warn('Date formatting error:', error);
      return 'Invalid date range';
    }
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Unknown';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.warn('DateTime formatting error:', error);
      return 'Invalid date';
    }
  };

  const getStatusBadge = (status) => {
    const styles = {
      pending: 'bg-yellow-100 text-yellow-800',
      resolved: 'bg-green-100 text-green-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status] || 'bg-secondary-100 text-secondary-800'}`}>
        {status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown'}
      </span>
    );
  };

  const getSeverityIcon = (reason) => {
    if (!reason) return '🔵';
    const reasonLower = reason.toLowerCase();
    if (reasonLower.includes('delay') || reasonLower.includes('weather')) return '🟡';
    if (reasonLower.includes('equipment') || reasonLower.includes('malfunction')) return '🔴';
    if (reasonLower.includes('route') || reasonLower.includes('deviation')) return '🟠';
    return '🔵';
  };

  // Calculate exception types from real data with error handling
  const getExceptionTypes = () => {
    if (!exceptionsData.recent || !Array.isArray(exceptionsData.recent) || exceptionsData.recent.length === 0) {
      return [];
    }

    try {
      const typeCounts = {};
      exceptionsData.recent.forEach(exception => {
        const reason = exception.reason || 'Other';
        const type = reason.includes('delay') ? 'Time Delay' :
                     reason.includes('route') ? 'Route Deviation' :
                     reason.includes('equipment') ? 'Equipment Issue' :
                     reason.includes('weather') ? 'Weather Delay' : 'Other';
        
        typeCounts[type] = (typeCounts[type] || 0) + 1;
      });

      const total = exceptionsData.recent.length;
      return Object.entries(typeCounts).map(([type, count]) => ({
        type,
        count,
        percentage: total > 0 ? ((count / total) * 100).toFixed(1) : '0.0'
      })).sort((a, b) => b.count - a.count);
    } catch (error) {
      console.warn('Error calculating exception types:', error);
      return [];
    }
  };

  const exceptionTypes = getExceptionTypes();

  // Calculate daily trends from real data with error handling
  const getDailyTrends = () => {
    if (!exceptionsData.trends || !Array.isArray(exceptionsData.trends) || exceptionsData.trends.length === 0) {
      return [0, 0, 0, 0, 0, 0, 0]; // Default 7 days
    }

    try {
      // Group by day and count
      const dailyCounts = new Array(7).fill(0);
      const today = new Date();
      
      exceptionsData.trends.forEach(trend => {
        if (trend.date) {
          const trendDate = new Date(trend.date);
          const daysDiff = Math.floor((today - trendDate) / (1000 * 60 * 60 * 24));
          if (daysDiff >= 0 && daysDiff < 7) {
            dailyCounts[6 - daysDiff] = trend.count || 0;
          }
        }
      });

      return dailyCounts;
    } catch (error) {
      console.warn('Error calculating daily trends:', error);
      return [0, 0, 0, 0, 0, 0, 0];
    }
  };

  const dailyTrends = getDailyTrends();

  // Show different messages based on data availability
  if (!data) {
    return (
      <div className="text-center py-12">
        <svg className="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.382 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 className="text-lg font-medium text-secondary-900 mb-2">No Exception Data Available</h3>
        <p className="text-secondary-500">Exception reports will appear here once there are trip exceptions.</p>
        <p className="text-secondary-400 text-sm mt-2">Check your network connection or try refreshing the page.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-secondary-900">Exceptions Report</h3>
        <span className="text-sm text-secondary-500">Period: {formatDateRange()}</span>
      </div>

      {/* Summary Cards - REAL DATA with error handling */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <span className="text-red-600 font-medium text-sm">{exceptionsData.summary?.total || 0}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Total Exceptions</p>
              <p className="text-xs text-secondary-500">{exceptionsData.summary?.rate || '0.0'}% of trips</p>
            </div>
          </div>
        </div>

        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 font-medium text-sm">{exceptionsData.summary?.pending || 0}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Pending Review</p>
              <p className="text-xs text-secondary-500">Awaiting approval</p>
            </div>
          </div>
        </div>

        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-medium text-sm">{exceptionsData.summary?.resolved || 0}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Resolved</p>
              <p className="text-xs text-secondary-500">Successfully handled</p>
            </div>
          </div>
        </div>

        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-secondary-100 rounded-full flex items-center justify-center">
                <span className="text-secondary-600 font-medium text-sm">
                  {(exceptionsData.summary?.total || 0) > 0 ? 
                    Math.round(((exceptionsData.summary?.resolved || 0) / exceptionsData.summary.total) * 100) : 0}%
                </span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Resolution Rate</p>
              <p className="text-xs text-secondary-500">Percentage resolved</p>
            </div>
          </div>
        </div>
      </div>

      {/* Exception Types and Trends - REAL DATA with error handling */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Exception Types */}
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-secondary-900 mb-4">Exception Types</h4>
          {exceptionTypes.length > 0 ? (
            <div className="space-y-4">
              {exceptionTypes.map((type, index) => (
                <div key={type.type} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      index === 0 ? 'bg-red-500' :
                      index === 1 ? 'bg-orange-500' :
                      index === 2 ? 'bg-yellow-500' :
                      index === 3 ? 'bg-blue-500' : 'bg-purple-500'
                    }`}></div>
                    <span className="text-sm font-medium text-secondary-900">{type.type}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-24 bg-secondary-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full" 
                        style={{ width: `${Math.min(parseFloat(type.percentage), 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-secondary-600 w-8 text-right">{type.count}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-secondary-500">
              <span className="text-4xl block mb-2">📊</span>
              No exception types data available
            </div>
          )}
        </div>

        {/* Daily Trend - REAL DATA with error handling */}
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-secondary-900 mb-4">Daily Exception Trend</h4>
          {dailyTrends.some(count => count > 0) ? (
            <div className="h-32 flex items-end space-x-3 pb-4">
              {dailyTrends.map((count, index) => {
                const maxCount = Math.max(...dailyTrends);
                const height = maxCount > 0 ? (count / maxCount) * 80 : 0;
                return (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div 
                      className="w-full bg-red-500 rounded-t" 
                      style={{ height: `${height}px` }}
                      title={`${count} exceptions`}
                    ></div>
                    <span className="text-xs text-secondary-600 mt-2">
                      {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][index]}
                    </span>
                    <span className="text-xs font-medium text-secondary-900">{count}</span>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-secondary-500">
              <span className="text-4xl block mb-2">📈</span>
              No trend data available
            </div>
          )}
          <div className="mt-4 text-center">
            <p className="text-sm text-secondary-600">
              Average: {dailyTrends.length > 0 ? (dailyTrends.reduce((a, b) => a + b, 0) / 7).toFixed(1) : 0} exceptions/day
            </p>
          </div>
        </div>
      </div>

      {/* Recent Exceptions - REAL DATA with enhanced error handling */}
      <div className="bg-white border border-secondary-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-secondary-900 mb-4">Recent Exceptions</h4>
        {exceptionsData.recent && Array.isArray(exceptionsData.recent) && exceptionsData.recent.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-secondary-200">
              <thead className="bg-secondary-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Trip
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Reason
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Reported
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-secondary-200">
                {exceptionsData.recent.slice(0, 10).map((exception) => (
                  <tr key={exception.id} className="hover:bg-secondary-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">⚠️</span>
                        <div>
                          <div className="text-sm font-medium text-secondary-900">
                            Trip #{exception.tripNumber || 'N/A'}
                          </div>
                          <div className="text-sm text-secondary-500">
                            {exception.truckNumber || 'N/A'} • {exception.driverName || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">{getSeverityIcon(exception.reason)}</span>
                        <div className="text-sm text-secondary-900 max-w-xs">
                          {exception.reason || 'No reason provided'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(exception.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-secondary-900">
                        {formatDateTime(exception.createdAt)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-secondary-500">
            <span className="text-4xl block mb-2">✅</span>
            No recent exceptions found
            <p className="text-sm mt-2">All trips are running smoothly!</p>
          </div>
        )}
      </div>

      {/* Resolution Metrics - REAL DATA with error handling */}
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-orange-900 mb-4">Exception Management Performance</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-900">
              {(exceptionsData.summary?.total || 0) > 0 ? 
                Math.round(((exceptionsData.summary?.resolved || 0) / exceptionsData.summary.total) * 100) : 0}%
            </div>
            <div className="text-sm text-orange-700">Resolution Rate</div>
            <div className="text-xs text-orange-600 mt-1">Target: &gt;90%</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-900">{exceptionsData.summary?.pending || 0}</div>
            <div className="text-sm text-orange-700">Pending Cases</div>
            <div className="text-xs text-orange-600 mt-1">Require attention</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-900">{exceptionsData.summary?.rate || '0.0'}%</div>
            <div className="text-sm text-orange-700">Exception Rate</div>
            <div className="text-xs text-orange-600 mt-1">Of total trips</div>
          </div>
        </div>
        <p className="text-sm text-orange-700 mt-4 text-center">
          ✅ All exception data is fetched live from the database • Updated: {new Date().toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
};

export default ExceptionsReport;