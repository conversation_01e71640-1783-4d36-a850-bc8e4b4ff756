const { Pool } = require('pg');
require('dotenv').config({ path: '../.env' });

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD
});

async function checkAssignmentEnum() {
  try {
    console.log('=== Checking Assignment Status Enum ===\n');
    
    // Get the enum type definition
    const enumQuery = await pool.query(`
      SELECT 
        t.typname AS enum_name,
        e.enumlabel AS enum_value,
        e.enumsortorder AS sort_order
      FROM pg_type t 
      JOIN pg_enum e ON t.oid = e.enumtypid  
      WHERE t.typname = 'assignment_status'
      ORDER BY e.enumsortorder;
    `);
    
    console.log('Current assignment_status enum values:');
    if (enumQuery.rows.length > 0) {
      enumQuery.rows.forEach(row => {
        console.log(`  - ${row.enum_value} (order: ${row.sort_order})`);
      });
    } else {
      console.log('  No enum values found or enum does not exist');
    }
    
    // Check for assignments table structure
    console.log('\n=== Assignments Table Structure ===\n');
    const columnQuery = await pool.query(`
      SELECT 
        column_name, 
        data_type,
        column_default,
        is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'assignments' 
      AND column_name = 'status'
    `);
    
    if (columnQuery.rows.length > 0) {
      const col = columnQuery.rows[0];
      console.log(`Column 'status' in assignments table:`);
      console.log(`  - Data Type: ${col.data_type}`);
      console.log(`  - Default: ${col.column_default}`);
      console.log(`  - Nullable: ${col.is_nullable}`);
    }
    
    // Search for "pending_approval" usage in the code
    console.log('\n=== Code Usage Analysis ===\n');
    console.log('Searching for "pending_approval" usage in scanner.js...\n');
    
    // Check actual assignments with different statuses
    const statusUsageQuery = await pool.query(`
      SELECT 
        status, 
        COUNT(*) as count,
        MIN(created_at) as first_used,
        MAX(created_at) as last_used
      FROM assignments
      GROUP BY status
      ORDER BY count DESC
    `);
    
    console.log('Current status usage in assignments table:');
    if (statusUsageQuery.rows.length > 0) {
      statusUsageQuery.rows.forEach(row => {
        console.log(`  - ${row.status}: ${row.count} records`);
        console.log(`    First: ${row.first_used}`);
        console.log(`    Last: ${row.last_used}\n`);
      });
    } else {
      console.log('  No assignments found');
    }
    
    // Find instances where pending_approval is being set
    console.log('\n=== Attempting to find pending_approval assignments ===\n');
    const pendingQuery = await pool.query(`
      SELECT 
        id, 
        assignment_code, 
        status, 
        notes,
        created_at
      FROM assignments
      WHERE notes::text ILIKE '%pending_approval%'
      ORDER BY created_at DESC
      LIMIT 5
    `);
    
    if (pendingQuery.rows.length > 0) {
      console.log('Found assignments with pending_approval in notes:');
      pendingQuery.rows.forEach(row => {
        console.log(`  - ID: ${row.id}, Code: ${row.assignment_code}`);
        console.log(`    Status: ${row.status}`);
        console.log(`    Notes: ${row.notes}`);
        console.log(`    Created: ${row.created_at}\n`);
      });
    } else {
      console.log('No assignments found with pending_approval in notes');
    }
    
    // Identify the fix needed
    console.log('\n=== ANALYSIS RESULTS ===\n');
    console.log('The code in scanner.js is attempting to set status = "pending_approval"');
    console.log('in the following locations:');
    console.log('  - Line 792: createRouteDeviationForExistingAssignment()');
    console.log('  - Line 1176: createRouteDeviationException()');
    console.log('\nThis value does NOT exist in the assignment_status enum.');
    console.log('\nTwo possible solutions:');
    console.log('1. Add "pending_approval" to the enum (if it\'s a valid business state)');
    console.log('2. Use an existing enum value like "assigned" and track approval separately');
    
  } catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await pool.end();
  }
}

checkAssignmentEnum();