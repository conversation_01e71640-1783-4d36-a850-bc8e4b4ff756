const { Pool } = require('pg');
require('dotenv').config({ path: '../.env' });

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD
});

async function checkScanLogsStructure() {
  try {
    console.log('=== Scan Logs Table Structure ===\n');
    
    // Check scan_logs table structure
    const scanLogsColumns = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'scan_logs' 
      ORDER BY ordinal_position
    `);
    
    console.log('scan_logs columns:');
    scanLogsColumns.rows.forEach(col => {
      console.log(`  - ${col.column_name} (${col.data_type})`);
    });
    
    // Check trip_logs table structure too
    console.log('\n=== Trip Logs Table Structure ===\n');
    
    const tripLogsColumns = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'trip_logs' 
      ORDER BY ordinal_position
    `);
    
    console.log('trip_logs columns:');
    tripLogsColumns.rows.forEach(col => {
      console.log(`  - ${col.column_name} (${col.data_type})`);
    });
    
    // Get a sample of recent scan logs to see the data
    console.log('\n=== Recent Scan Logs Sample ===\n');
    const recentScans = await pool.query(`
      SELECT * FROM scan_logs 
      ORDER BY created_at DESC 
      LIMIT 3
    `);
    
    if (recentScans.rows.length > 0) {
      console.log('Sample scan log data:');
      recentScans.rows.forEach((scan, index) => {
        console.log(`\nScan ${index + 1}:`);
        Object.keys(scan).forEach(key => {
          const value = scan[key];
          if (value !== null) {
            console.log(`  ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`);
          }
        });
      });
    } else {
      console.log('No scan logs found in the database.');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await pool.end();
  }
}

checkScanLogsStructure();