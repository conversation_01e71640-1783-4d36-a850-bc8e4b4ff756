/**
 * Investigate Real Issues
 * 
 * 1. Why assignment status stays 'pending_approval' after admin approval
 * 2. Why location names show as undefined instead of actual names
 */

const { getClient } = require('../config/database');

async function investigateRealIssues() {
  console.log('🔍 Investigating Real Issues...\n');
  
  const client = await getClient();
  
  try {
    // 1. Check assignment status issue
    console.log('1️⃣ INVESTIGATING ASSIGNMENT STATUS ISSUE');
    await investigateAssignmentStatus(client);
    
    // 2. Check location name retrieval issue
    console.log('\n2️⃣ INVESTIGATING LOCATION NAME RETRIEVAL ISSUE');
    await investigateLocationNames(client);
    
    // 3. Check approval workflow execution
    console.log('\n3️⃣ INVESTIGATING APPROVAL WORKFLOW EXECUTION');
    await investigateApprovalWorkflow(client);
    
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
  } finally {
    client.release();
  }
}

async function investigateAssignmentStatus(client) {
  // Find assignments that should be 'assigned' but are still 'pending_approval'
  const pendingAssignmentsResult = await client.query(`
    SELECT 
        a.id,
        a.assignment_code,
        a.status,
        a.created_at,
        a.updated_at,
        COUNT(ap.id) as total_approvals,
        COUNT(CASE WHEN ap.status = 'approved' THEN 1 END) as approved_count,
        MAX(ap.reviewed_at) as last_approval_time,
        STRING_AGG(DISTINCT ap.reviewed_by, ', ') as approved_by
    FROM assignments a
    JOIN trip_logs tl ON a.id = tl.assignment_id
    JOIN approvals ap ON tl.id = ap.trip_log_id
    WHERE a.status = 'pending_approval'
    GROUP BY a.id, a.assignment_code, a.status, a.created_at, a.updated_at
    ORDER BY a.created_at DESC
  `);
  
  console.log(`   📋 Found ${pendingAssignmentsResult.rows.length} assignments still in 'pending_approval' status:`);
  
  for (const assignment of pendingAssignmentsResult.rows) {
    console.log(`\n   🔍 Assignment: ${assignment.assignment_code} (ID: ${assignment.id})`);
    console.log(`      Status: ${assignment.status}`);
    console.log(`      Created: ${assignment.created_at}`);
    console.log(`      Updated: ${assignment.updated_at}`);
    console.log(`      Approved Count: ${assignment.approved_count}`);
    console.log(`      Last Approval: ${assignment.last_approval_time}`);
    console.log(`      Approved By: ${assignment.approved_by}`);
    
    // Check if assignment was updated after approval
    if (assignment.last_approval_time && assignment.updated_at) {
      const approvalTime = new Date(assignment.last_approval_time);
      const updateTime = new Date(assignment.updated_at);
      const timeDiff = approvalTime - updateTime;
      
      if (timeDiff > 0) {
        console.log(`      🚨 ISSUE: Assignment NOT updated after approval (${Math.round(timeDiff / 1000)}s gap)`);
        console.log(`         Approval: ${approvalTime.toISOString()}`);
        console.log(`         Last Update: ${updateTime.toISOString()}`);
      } else {
        console.log(`      ✅ Assignment was updated after approval`);
      }
    }
    
    // Check trip notes for pending_assignment_id
    const tripNotesResult = await client.query(`
      SELECT tl.id, tl.notes
      FROM trip_logs tl
      WHERE tl.assignment_id = $1
      AND tl.notes IS NOT NULL
      ORDER BY tl.created_at DESC
      LIMIT 1
    `, [assignment.id]);
    
    if (tripNotesResult.rows.length > 0) {
      try {
        const notes = JSON.parse(tripNotesResult.rows[0].notes);
        console.log(`      📝 Trip Notes:`, JSON.stringify(notes, null, 8));
        
        if (notes.pending_assignment_id) {
          console.log(`      🔗 References pending assignment ID: ${notes.pending_assignment_id}`);
          
          if (notes.pending_assignment_id == assignment.id) {
            console.log(`      🎯 This IS the pending assignment that should be updated`);
          } else {
            console.log(`      ⚠️ This references a DIFFERENT assignment`);
          }
        }
      } catch (e) {
        console.log(`      📝 Trip Notes (raw): ${tripNotesResult.rows[0].notes}`);
      }
    }
  }
}

async function investigateLocationNames(client) {
  // Check how location names are retrieved in the scanner query
  console.log('   📋 Testing location name retrieval in scanner queries...');
  
  // Test the exact query used in scanner.js
  const scannerQueryResult = await client.query(`
    SELECT
      a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
      a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
      dt.truck_number, dt.status as truck_status,
      ll.name as loading_location, ul.name as unloading_location,
      d.full_name as driver_name
    FROM assignments a
    JOIN dump_trucks dt ON a.truck_id = dt.id
    LEFT JOIN locations ll ON a.loading_location_id = ll.id
    LEFT JOIN locations ul ON a.unloading_location_id = ul.id
    LEFT JOIN drivers d ON a.driver_id = d.id
    WHERE dt.truck_number = 'DT-100'
      AND a.status IN ('assigned', 'in_progress', 'pending_approval')
    ORDER BY a.created_at DESC
    LIMIT 3
  `);
  
  console.log(`   📊 Scanner query results for DT-100:`);
  
  for (const row of scannerQueryResult.rows) {
    console.log(`\n   📋 Assignment: ${row.assignment_code}`);
    console.log(`      Loading Location ID: ${row.loading_location_id}`);
    console.log(`      Loading Location Name: "${row.loading_location}" ${!row.loading_location ? '❌ NULL' : '✅'}`);
    console.log(`      Unloading Location ID: ${row.unloading_location_id}`);
    console.log(`      Unloading Location Name: "${row.unloading_location}" ${!row.unloading_location ? '❌ NULL' : '✅'}`);
    
    // Check if the location actually exists
    if (!row.loading_location && row.loading_location_id) {
      const locationCheckResult = await client.query(`
        SELECT id, name, status FROM locations WHERE id = $1
      `, [row.loading_location_id]);
      
      if (locationCheckResult.rows.length > 0) {
        const location = locationCheckResult.rows[0];
        console.log(`      🔍 Loading location exists: "${location.name}" (status: ${location.status})`);
        console.log(`      🚨 ISSUE: Location exists but LEFT JOIN returned NULL`);
      } else {
        console.log(`      🚨 CRITICAL: Loading location ID ${row.loading_location_id} does not exist!`);
      }
    }
    
    if (!row.unloading_location && row.unloading_location_id) {
      const locationCheckResult = await client.query(`
        SELECT id, name, status FROM locations WHERE id = $1
      `, [row.unloading_location_id]);
      
      if (locationCheckResult.rows.length > 0) {
        const location = locationCheckResult.rows[0];
        console.log(`      🔍 Unloading location exists: "${location.name}" (status: ${location.status})`);
        console.log(`      🚨 ISSUE: Location exists but LEFT JOIN returned NULL`);
      } else {
        console.log(`      🚨 CRITICAL: Unloading location ID ${row.unloading_location_id} does not exist!`);
      }
    }
  }
}

async function investigateApprovalWorkflow(client) {
  // Check which approval workflow is actually being executed
  console.log('   📋 Checking approval workflow execution...');
  
  // Check recent approvals and their processing
  const recentApprovalsResult = await client.query(`
    SELECT 
        ap.id,
        ap.trip_log_id,
        ap.status,
        ap.reviewed_by,
        ap.reviewed_at,
        ap.created_at,
        tl.assignment_id,
        tl.exception_approved_by,
        tl.exception_approved_at,
        a.status as assignment_status
    FROM approvals ap
    JOIN trip_logs tl ON ap.trip_log_id = tl.id
    JOIN assignments a ON tl.assignment_id = a.id
    WHERE ap.status = 'approved'
    ORDER BY ap.reviewed_at DESC
    LIMIT 3
  `);
  
  console.log(`   📊 Recent approved exceptions:`);
  
  for (const approval of recentApprovalsResult.rows) {
    console.log(`\n   🔍 Approval ID: ${approval.id}`);
    console.log(`      Trip Log ID: ${approval.trip_log_id}`);
    console.log(`      Assignment ID: ${approval.assignment_id}`);
    console.log(`      Assignment Status: ${approval.assignment_status}`);
    console.log(`      Reviewed By: ${approval.reviewed_by}`);
    console.log(`      Reviewed At: ${approval.reviewed_at}`);
    console.log(`      Exception Approved By: ${approval.exception_approved_by}`);
    console.log(`      Exception Approved At: ${approval.exception_approved_at}`);
    
    // Check if both approval methods were used
    if (approval.reviewed_by && approval.exception_approved_by) {
      console.log(`      🔍 Both approval methods used`);
    } else if (approval.reviewed_by && !approval.exception_approved_by) {
      console.log(`      🔍 Only standard approval used (reviewed_by)`);
    } else if (!approval.reviewed_by && approval.exception_approved_by) {
      console.log(`      🔍 Only exception approval used (exception_approved_by)`);
    }
    
    // Check if assignment status was updated after approval
    const timeDiff = approval.assignment_status === 'pending_approval' ? 'NOT UPDATED' : 'UPDATED';
    console.log(`      📊 Assignment Status Update: ${timeDiff}`);
  }
  
  // Check which approval endpoint is being used
  console.log('\n   📋 Checking active approval endpoint...');
  const fs = require('fs');
  const serverContent = fs.readFileSync('server/server.js', 'utf8');
  
  if (serverContent.includes('approvals-orig-backup.js')) {
    console.log('   📍 Active approval route: approvals-orig-backup.js');
    console.log('   🔗 This uses exception-flow-manager.js for processing');
  } else {
    console.log('   📍 Active approval route: approvals.js');
    console.log('   🔗 This uses its own handleApprovedException function');
  }
}

// Run the investigation
if (require.main === module) {
  investigateRealIssues()
    .then(() => {
      console.log('\n🎯 Real issues investigation completed.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Investigation failed:', error.message);
      process.exit(1);
    });
}

module.exports = { investigateRealIssues };
