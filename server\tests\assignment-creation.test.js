// Test script for assignment creation logic
// Tests the new flexible assignment system

const axios = require('axios');

// Mock assignment data for testing
const testAssignments = [
  {
    name: 'DT-100: Point A → Point B (should succeed)',
    data: {
      truck_id: 1,
      driver_id: 1, 
      loading_location_id: 1,
      unloading_location_id: 2,
      priority: 'normal',
      expected_loads_per_day: 1
    },
    shouldSucceed: true
  },
  {
    name: 'DT-100: Point C → Point B (should succeed - different loading)',
    data: {
      truck_id: 1,
      driver_id: 1,
      loading_location_id: 3,
      unloading_location_id: 2,
      priority: 'normal', 
      expected_loads_per_day: 1
    },
    shouldSucceed: true
  },
  {
    name: 'DT-100: Point A → Point D (should succeed - different unloading)',
    data: {
      truck_id: 1,
      driver_id: 1,
      loading_location_id: 1,
      unloading_location_id: 4,
      priority: 'normal',
      expected_loads_per_day: 1
    },
    shouldSucceed: true
  },
  {
    name: 'DT-100: Point A → Point B (should fail - exact duplicate)',
    data: {
      truck_id: 1,
      driver_id: 1,
      loading_location_id: 1,
      unloading_location_id: 2,
      priority: 'normal',
      expected_loads_per_day: 1
    },
    shouldSucceed: false
  }
];

async function testAssignmentCreation() {
  console.log('🧪 Testing Assignment Creation Logic...\n');
  
  const baseURL = 'http://localhost:5000/api';
  let passedTests = 0;
  let totalTests = testAssignments.length;
  
  for (let i = 0; i < testAssignments.length; i++) {
    const test = testAssignments[i];
    console.log(`Test ${i + 1}: ${test.name}`);
    
    try {
      const response = await axios.post(`${baseURL}/assignments`, test.data, {
        headers: {
          'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE', // Replace with actual token
          'Content-Type': 'application/json'
        }
      });
      
      if (test.shouldSucceed) {
        console.log(`✅ PASS - Assignment created successfully`);
        console.log(`   Assignment Code: ${response.data.data.assignment_code}`);
        if (response.data.calculated_rate) {
          console.log(`   Calculated Rate: $${response.data.calculated_rate}`);
        }
        passedTests++;
      } else {
        console.log(`❌ FAIL - Expected failure but assignment was created`);
      }
      
    } catch (error) {
      if (!test.shouldSucceed) {
        console.log(`✅ PASS - Assignment correctly rejected: ${error.response?.data?.message || error.message}`);
        passedTests++;
      } else {
        console.log(`❌ FAIL - Expected success but got error: ${error.response?.data?.message || error.message}`);
      }
    }
    
    console.log(''); // Empty line for readability
  }
  
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed\n`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Assignment logic is working correctly.');
    console.log('\n✅ Confirmed behaviors:');
    console.log('   - Multiple assignments allowed for same truck with different routes');
    console.log('   - Exact duplicates (same truck + same locations) are prevented');
    console.log('   - Distance-based rate calculation is working');
    console.log('   - Unique assignment codes are generated');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
}

// Manual test data for copy-paste into frontend
function getManualTestData() {
  return {
    test1: {
      truck_id: 1,
      driver_id: 1,
      loading_location_id: 1,
      unloading_location_id: 2,
      notes: 'Test 1: DT-100 Point A to Point B'
    },
    test2: {
      truck_id: 1,
      driver_id: 1,
      loading_location_id: 3,
      unloading_location_id: 2,
      notes: 'Test 2: DT-100 Point C to Point B (different loading)'
    },
    test3: {
      truck_id: 1,
      driver_id: 1,
      loading_location_id: 1,
      unloading_location_id: 4,
      notes: 'Test 3: DT-100 Point A to Point D (different unloading)'
    }
  };
}

if (require.main === module) {
  console.log('🚀 Assignment Creation Test Suite\n');
  console.log('⚠️  Note: This requires a running server and valid JWT token');
  console.log('📝 For manual testing, use the data below in your frontend:\n');
  console.log(JSON.stringify(getManualTestData(), null, 2));
  console.log('\n🔧 If you want to run automated tests, update the JWT token and run:');
  console.log('   testAssignmentCreation();');
}

module.exports = { testAssignmentCreation, getManualTestData };
