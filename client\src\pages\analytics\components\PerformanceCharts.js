import React from 'react';

const PerformanceCharts = ({ data, loading, dateRange, onExportData }) => {
  if (loading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-secondary-200 h-64 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <svg className="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
        <h3 className="text-lg font-medium text-secondary-900 mb-2">No Performance Data Available</h3>
        <p className="text-secondary-500">Performance charts will appear here once there are completed trips.</p>
      </div>
    );
  }

  const formatDateRange = () => {
    const start = new Date(dateRange.start).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    const end = new Date(dateRange.end).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    return `${start} - ${end}`;
  };

  // Simple chart component for trip volume
  const TripVolumeChart = ({ tripData }) => {
    if (!tripData || !tripData.labels || tripData.labels.length === 0) {
      return (
        <div className="text-center py-8 text-secondary-500">
          <span className="text-4xl block mb-2">📊</span>
          No trip volume data available
        </div>
      );
    }

    const maxValue = Math.max(...tripData.assigned, ...tripData.completed);

    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
            <span>Assigned Trips</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
            <span>Completed Trips</span>
          </div>
        </div>
        <div className="h-48 flex items-end justify-between space-x-2">
          {tripData.labels.map((label, index) => (
            <div key={index} className="flex-1 flex flex-col items-center space-y-1">
              <div className="w-full flex flex-col items-center space-y-1">
                <div 
                  className="w-4 bg-blue-500 rounded-t"
                  style={{ height: `${(tripData.assigned[index] / maxValue) * 140}px` }}
                  title={`${tripData.assigned[index]} assigned`}
                ></div>
                <div 
                  className="w-4 bg-green-500 rounded-t"
                  style={{ height: `${(tripData.completed[index] / maxValue) * 140}px` }}
                  title={`${tripData.completed[index]} completed`}
                ></div>
              </div>
              <span className="text-xs text-secondary-600 transform -rotate-45 origin-left">
                {label}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Simple chart for efficiency
  const EfficiencyChart = ({ efficiencyData }) => {
    if (!efficiencyData || !efficiencyData.labels || efficiencyData.labels.length === 0) {
      return (
        <div className="text-center py-8 text-secondary-500">
          <span className="text-4xl block mb-2">⏱️</span>
          No efficiency data available
        </div>
      );
    }

    const maxValue = Math.max(...efficiencyData.avgTripTime, efficiencyData.target);

    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-orange-500 rounded mr-2"></div>
            <span>Avg Trip Time</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded mr-2"></div>
            <span>Target ({efficiencyData.target}m)</span>
          </div>
        </div>
        <div className="h-48 flex items-end justify-between space-x-2">
          {efficiencyData.labels.map((label, index) => (
            <div key={index} className="flex-1 flex flex-col items-center">
              <div className="w-full flex justify-center items-end space-x-1">
                <div 
                  className="w-6 bg-orange-500 rounded-t"
                  style={{ height: `${(efficiencyData.avgTripTime[index] / maxValue) * 140}px` }}
                  title={`${efficiencyData.avgTripTime[index]} minutes`}
                ></div>
                <div 
                  className="w-1 bg-red-500"
                  style={{ height: `${(efficiencyData.target / maxValue) * 140}px` }}
                  title={`Target: ${efficiencyData.target} minutes`}
                ></div>
              </div>
              <span className="text-xs text-secondary-600 mt-2">{label}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Utilization bars
  const UtilizationBars = ({ utilizationData, title }) => {
    if (!utilizationData || utilizationData.length === 0) {
      return (
        <div className="text-center py-8 text-secondary-500">
          <span className="text-4xl block mb-2">📈</span>
          No {title.toLowerCase()} data available
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {utilizationData.slice(0, 8).map((item, index) => (
          <div key={index} className="space-y-1">
            <div className="flex justify-between items-center text-sm">
              <span className="font-medium text-secondary-900">{item.name}</span>
              <span className="text-secondary-600">{item.utilization}% ({item.trips} trips)</span>
            </div>
            <div className="w-full bg-secondary-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  item.utilization >= 80 ? 'bg-green-500' : 
                  item.utilization >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${Math.min(item.utilization, 100)}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Route performance table
  const RoutePerformanceTable = ({ routeData }) => {
    if (!routeData || !routeData.performance || routeData.performance.length === 0) {
      return (
        <div className="text-center py-8 text-secondary-500">
          <span className="text-4xl block mb-2">🛣️</span>
          No route performance data available
        </div>
      );
    }

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-secondary-200">
          <thead className="bg-secondary-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Route
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Avg Time
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Trips
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Efficiency
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-secondary-200">
            {routeData.performance.slice(0, 10).map((route, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-secondary-50'}>
                <td className="px-4 py-3 text-sm font-medium text-secondary-900">
                  {route.route}
                </td>
                <td className="px-4 py-3 text-sm text-secondary-600">
                  {route.avgTime}m
                </td>
                <td className="px-4 py-3 text-sm text-secondary-600">
                  {route.trips}
                </td>
                <td className="px-4 py-3 text-sm">
                  <div className="flex items-center">
                    <div className="w-16 bg-secondary-200 rounded-full h-2 mr-2">
                      <div 
                        className={`h-2 rounded-full ${
                          route.efficiency >= 90 ? 'bg-green-500' : 
                          route.efficiency >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${Math.min(route.efficiency, 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-secondary-600">{route.efficiency}%</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-secondary-900">Performance Analytics</h3>
        <div className="flex space-x-3">
          <span className="text-sm text-secondary-500">Period: {formatDateRange()}</span>
          <button
            onClick={() => onExportData && onExportData(data, 'csv')}
            className="text-sm text-primary-600 hover:text-primary-500 font-medium"
          >
            Export CSV
          </button>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trip Volume Chart */}
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-secondary-900 mb-4">Trip Volume Trends</h4>
          <TripVolumeChart tripData={data.tripVolume} />
        </div>

        {/* Efficiency Chart */}
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-secondary-900 mb-4">Trip Time Efficiency</h4>
          <EfficiencyChart efficiencyData={data.efficiency} />
        </div>

        {/* Truck Utilization */}
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-secondary-900 mb-4">Truck Utilization</h4>
          <UtilizationBars utilizationData={data.utilization?.trucks} title="Truck" />
        </div>

        {/* Driver Performance */}
        <div className="bg-white border border-secondary-200 rounded-lg p-6">
          <h4 className="text-lg font-medium text-secondary-900 mb-4">Driver Performance</h4>
          <UtilizationBars utilizationData={data.utilization?.drivers} title="Driver" />
        </div>
      </div>

      {/* Route Performance */}
      <div className="bg-white border border-secondary-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-secondary-900 mb-4">Route Performance</h4>
        <RoutePerformanceTable routeData={data.routes} />
      </div>

      {/* Performance Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-blue-900 mb-4">📊 Performance Summary</h4>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {data.tripVolume?.completed?.reduce((a, b) => a + b, 0) || 0}
            </div>
            <div className="text-sm text-blue-700">Total Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {data.efficiency?.avgTripTime ? 
                Math.round(data.efficiency.avgTripTime.reduce((a, b) => a + b, 0) / data.efficiency.avgTripTime.length) : 0
              }m
            </div>
            <div className="text-sm text-blue-700">Avg Trip Time</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {data.utilization?.trucks?.length || 0}
            </div>
            <div className="text-sm text-blue-700">Active Trucks</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {data.routes?.performance?.length || 0}
            </div>
            <div className="text-sm text-blue-700">Active Routes</div>
          </div>
        </div>
        <p className="text-sm text-blue-700 mt-4 text-center">
          ✅ All performance data is calculated from real database records • Updated: {new Date().toLocaleTimeString()}
        </p>
      </div>

      {/* Data Export Options */}
      <div className="bg-secondary-50 border border-secondary-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-secondary-900 mb-4">Export Options</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button 
            onClick={() => onExportData && onExportData(data, 'csv')}
            className="flex items-center justify-center p-3 border border-secondary-300 rounded-lg hover:bg-white transition-colors"
          >
            <span className="text-2xl mr-3">📄</span>
            <div className="text-left">
              <h5 className="font-medium text-secondary-900">Export as CSV</h5>
              <p className="text-sm text-secondary-500">Download raw data</p>
            </div>
          </button>
          <button 
            onClick={() => onExportData && onExportData(data, 'pdf')}
            className="flex items-center justify-center p-3 border border-secondary-300 rounded-lg hover:bg-white transition-colors"
          >
            <span className="text-2xl mr-3">📋</span>
            <div className="text-left">
              <h5 className="font-medium text-secondary-900">Export as PDF</h5>
              <p className="text-sm text-secondary-500">Printable report</p>
            </div>
          </button>
          <button className="flex items-center justify-center p-3 border border-secondary-300 rounded-lg hover:bg-white transition-colors">
            <span className="text-2xl mr-3">📧</span>
            <div className="text-left">
              <h5 className="font-medium text-secondary-900">Email Report</h5>
              <p className="text-sm text-secondary-500">Send to stakeholders</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PerformanceCharts;