-- Dump Trucks Table
CREATE TABLE dump_trucks (
    id SERIAL PRIMARY KEY,
    truck_number VARCHAR(20) UNIQUE NOT NULL,
    model VARCHAR(50),
    license_plate VARCHAR(20) UNIQUE NOT NULL,
    capacity_tons NUMERIC(5,2),
    status VARCHAR(20) DEFAULT 'active'
);

-- Drivers Table
CREATE TABLE drivers (
    id SERIAL PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(20) UNIQUE NOT NULL,
    contact_number VARCHAR(20)
);

-- Trips Table (supports multiple shifts, relational integrity, validation)
CREATE TABLE trips (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER NOT NULL REFERENCES dump_trucks(id) ON DELETE CASCADE,
    driver_id INTEGER NOT NULL REFERENCES drivers(id) ON DELETE CASCADE,
    trip_date DATE NOT NULL,
    shift VARCHAR(10) NOT NULL CHECK (shift IN ('day', 'night')),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    duration_minutes INTEGER GENERATED ALWAYS AS ((EXTRACT(EPOCH FROM (end_time - start_time))/60)::INTEGER) STORED,
    load_details VARCHAR(100),
    destination VARCHAR(100),
    CONSTRAINT unique_truck_shift UNIQUE (truck_id, trip_date, shift, start_time),
    CONSTRAINT no_overlap_shift CHECK (
        NOT EXISTS (
            SELECT 1 FROM trips t2
            WHERE t2.truck_id = truck_id
              AND t2.trip_date = trip_date
              AND t2.shift = shift
              AND (
                  (start_time, end_time) OVERLAPS (t2.start_time, t2.end_time)
              )
              AND t2.id <> id
        )
    )
);

-- Sample Data
INSERT INTO dump_trucks (truck_number, model, license_plate, capacity_tons) VALUES
('DT-001', 'CAT 770G', 'ABC-123', 35.0),
('DT-002', 'Volvo FMX', 'XYZ-789', 40.0);

INSERT INTO drivers (full_name, employee_id, contact_number) VALUES
('Juan Dela Cruz', 'EMP001', '09171234567'),
('Maria Santos', 'EMP002', '09179876543');

INSERT INTO trips (truck_id, driver_id, trip_date, shift, start_time, end_time, load_details, destination) VALUES
(1, 1, '2025-06-25', 'day', '2025-06-25 06:00', '2025-06-25 14:00', 'Sand, 20 tons', 'Site A'),
(1, 2, '2025-06-25', 'night', '2025-06-25 18:00', '2025-06-26 02:00', 'Gravel, 18 tons', 'Site B'),
(2, 2, '2025-06-25', 'day', '2025-06-25 07:00', '2025-06-25 15:00', 'Sand, 22 tons', 'Site C');