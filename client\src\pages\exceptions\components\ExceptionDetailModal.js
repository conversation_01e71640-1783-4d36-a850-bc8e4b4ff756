import React, { useState } from 'react';
import { format } from 'date-fns';

const ExceptionDetailModal = ({ isOpen, onClose, exception, onApprove, onReject }) => {
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  const getSeverityBadge = (severity) => {
    const severityClasses = {
      'low': 'bg-blue-100 text-blue-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'high': 'bg-orange-100 text-orange-800',
      'critical': 'bg-red-100 text-red-800'
    };

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${severityClasses[severity] || 'bg-secondary-100 text-secondary-800'}`}>
        {severity ? severity.charAt(0).toUpperCase() + severity.slice(1) : 'Unknown'}
      </span>
    );
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'approved': 'bg-green-100 text-green-800',
      'rejected': 'bg-red-100 text-red-800'
    };

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusClasses[status] || 'bg-secondary-100 text-secondary-800'}`}>
        {status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown'}
      </span>
    );
  };

  const formatExceptionType = (type) => {
    return type ? type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ') : 'Unknown';
  };

  const getTypeIcon = (type) => {
    const typeIcons = {
      'route_deviation': '🛣️',
      'time_violation': '⏰',
      'equipment_issue': '🔧',
      'weather_delay': '🌧️',
      'manual_override': '🔄',
      'other': '❓'
    };

    return typeIcons[type] || '❓';
  };

  const handleDecisionSubmit = async (decisionType) => {
    setLoading(true);
    try {
      if (decisionType === 'approved') {
        await onApprove(notes);
      } else {
        await onReject(notes);
      }
      onClose();
    } catch (error) {
      console.error('Error submitting decision:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && !loading) {
      onClose();
    }
  };

  if (!isOpen || !exception) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      {/* Backdrop */}
      <div 
        className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
        onClick={handleBackdropClick}
      >
        <div className="fixed inset-0 bg-secondary-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        {/* Modal */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-secondary-900" id="modal-title">
              Exception Details
            </h3>
            <button
              onClick={onClose}
              disabled={loading}
              className="text-secondary-400 hover:text-secondary-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            {/* Exception Information */}
            <div className="space-y-6">
              
              {/* Basic Info */}
              <div className="bg-secondary-50 rounded-lg p-4">
                <h4 className="text-md font-medium text-secondary-900 mb-3">Exception Information</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Type:</span>
                    <div className="flex items-center">
                      <span className="text-lg mr-2">{getTypeIcon(exception.exception_type)}</span>
                      <span className="text-sm font-medium">{formatExceptionType(exception.exception_type)}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Severity:</span>
                    {getSeverityBadge(exception.severity)}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Status:</span>
                    {getStatusBadge(exception.status)}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Reported:</span>
                    <span className="text-sm">
                      {exception.created_at ? format(new Date(exception.created_at), 'MMM d, yyyy h:mm a') : 'Unknown'}
                    </span>
                  </div>
                  {exception.reported_by_email && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-secondary-600">Reported by:</span>
                      <span className="text-sm">{exception.reported_by_email}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Trip Information */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="text-md font-medium text-secondary-900 mb-3">Trip Information</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Trip Number:</span>
                    <span className="text-sm font-medium">#{exception.trip_number}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Truck:</span>
                    <span className="text-sm">{exception.truck_number}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Driver:</span>
                    <span className="text-sm">{exception.driver_name || 'No Driver'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Route:</span>
                    <span className="text-sm">{exception.loading_location} → {exception.unloading_location}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">Trip Status:</span>
                    <span className="text-sm capitalize">{exception.trip_status?.replace('_', ' ')}</span>
                  </div>
                </div>
              </div>

              {/* Exception Description */}
              <div>
                <h4 className="text-md font-medium text-secondary-900 mb-2">Exception Description</h4>
                <div className="bg-white border border-secondary-200 rounded-lg p-4">
                  <p className="text-sm text-secondary-700 whitespace-pre-wrap">
                    {exception.exception_description}
                  </p>
                </div>
              </div>
            </div>

            {/* Decision Section */}
            <div className="space-y-6">
              
              {/* Previous Decision */}
              {exception.status !== 'pending' && (
                <div className="bg-secondary-50 rounded-lg p-4">
                  <h4 className="text-md font-medium text-secondary-900 mb-3">Decision Made</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-secondary-600">Decision:</span>
                      {getStatusBadge(exception.status)}
                    </div>
                    {exception.reviewed_at && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-secondary-600">Reviewed:</span>
                        <span className="text-sm">
                          {format(new Date(exception.reviewed_at), 'MMM d, yyyy h:mm a')}
                        </span>
                      </div>
                    )}
                    {exception.reviewed_by_email && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-secondary-600">Reviewed by:</span>
                        <span className="text-sm">{exception.reviewed_by_email}</span>
                      </div>
                    )}
                    {exception.notes && (
                      <div>
                        <span className="text-sm text-secondary-600 block mb-1">Notes:</span>
                        <div className="bg-white border border-secondary-200 rounded p-2">
                          <p className="text-sm text-secondary-700 whitespace-pre-wrap">{exception.notes}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Make Decision */}
              {exception.status === 'pending' && (
                <div className="bg-yellow-50 rounded-lg p-4">
                  <h4 className="text-md font-medium text-secondary-900 mb-3">Make Decision</h4>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary-700 mb-1">
                        Review Notes (Optional)
                      </label>
                      <textarea
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        rows={3}
                        className="input"
                        placeholder="Add notes about your decision..."
                        disabled={loading}
                      />
                    </div>

                    <div className="flex space-x-3">
                      <button
                        onClick={() => handleDecisionSubmit('approved')}
                        disabled={loading}
                        className="flex-1 btn bg-green-600 hover:bg-green-700 text-white"
                      >
                        {loading ? (
                          <div className="flex items-center justify-center">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                            Processing...
                          </div>
                        ) : (
                          <>
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            Approve
                          </>
                        )}
                      </button>
                      
                      <button
                        onClick={() => handleDecisionSubmit('rejected')}
                        disabled={loading}
                        className="flex-1 btn bg-red-600 hover:bg-red-700 text-white"
                      >
                        {loading ? (
                          <div className="flex items-center justify-center">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                            Processing...
                          </div>
                        ) : (
                          <>
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            Reject
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Timeline */}
              <div>
                <h4 className="text-md font-medium text-secondary-900 mb-3">Timeline</h4>
                <div className="space-y-3">
                  
                  {/* Exception Reported */}
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                      <span className="text-yellow-600 text-sm">⚠️</span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-secondary-900">Exception Reported</p>
                      <p className="text-xs text-secondary-500">
                        {exception.created_at ? format(new Date(exception.created_at), 'MMM d, yyyy h:mm a') : 'Unknown time'}
                      </p>
                    </div>
                  </div>

                  {/* Trip Events */}
                  {exception.loading_start_time && (
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 text-sm">🚛</span>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-secondary-900">Loading Started</p>
                        <p className="text-xs text-secondary-500">
                          {format(new Date(exception.loading_start_time), 'MMM d, yyyy h:mm a')}
                        </p>
                      </div>
                    </div>
                  )}

                  {exception.loading_end_time && (
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 text-sm">✅</span>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-secondary-900">Loading Completed</p>
                        <p className="text-xs text-secondary-500">
                          {format(new Date(exception.loading_end_time), 'MMM d, yyyy h:mm a')}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Decision Made */}
                  {exception.reviewed_at && (
                    <div className="flex items-start space-x-3">
                      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                        exception.status === 'approved' ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        <span className={`text-sm ${
                          exception.status === 'approved' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {exception.status === 'approved' ? '✅' : '❌'}
                        </span>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-secondary-900">
                          Exception {exception.status === 'approved' ? 'Approved' : 'Rejected'}
                        </p>
                        <p className="text-xs text-secondary-500">
                          {format(new Date(exception.reviewed_at), 'MMM d, yyyy h:mm a')}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-6 pt-6 border-t border-secondary-200">
            <div className="flex justify-end">
              <button
                onClick={onClose}
                disabled={loading}
                className="btn btn-secondary"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExceptionDetailModal;
