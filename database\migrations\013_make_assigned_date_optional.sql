-- Migration: Make assigned_date optional in assignments table
-- This prevents "null value in column 'assigned_date' violates not-null constraint" errors

-- Add comment explaining the change
COMMENT ON COLUMN assignments.assigned_date IS 'Planned date for the assignment, can be null for flexible assignments';

-- Alter the column to allow NULL values
ALTER TABLE assignments ALTER COLUMN assigned_date DROP NOT NULL;

-- Update any existing records that might have null values
UPDATE assignments 
SET assigned_date = created_at::date 
WHERE assigned_date IS NULL;
