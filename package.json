{"name": "hauling-qr-trip-system", "version": "1.0.0", "description": "QR Code-based Hauling Truck Trip Management System", "main": "server/server.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-deps": "npm install && cd server && npm install && cd ../client && npm install", "setup": "npm run install-deps && npm run setup-db", "setup-db": "psql -U postgres -d postgres -f database/init.sql"}, "keywords": ["qr-code", "hauling", "truck", "trip-management", "logistics"], "author": "Hauling Management System", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"file-saver": "^2.0.5", "pg": "^8.16.1", "ws": "^8.18.2"}}