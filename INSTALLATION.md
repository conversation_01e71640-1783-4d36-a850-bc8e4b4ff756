# 🚀 Installation Guide - Step 1: Project Setup

## Prerequisites

Before starting, ensure you have the following installed:

- **Node.js** (v16 or higher) - [Download](https://nodejs.org/)
- **PostgreSQL** (v12 or higher) - [Download](https://www.postgresql.org/download/)
- **pgAdmin** (optional but recommended) - [Download](https://www.pgadmin.org/)
- **Git** - [Download](https://git-scm.com/)

## Step-by-Step Installation

### 1. Download and Extract Project

1. Download the project files
2. Extract to your desired location (e.g., `C:\Users\<USER>\Documents\Hauling-QR-Trip-System`)
3. Open the folder in VS Code or your preferred IDE

### 2. Install Dependencies

Open terminal in the project root directory and run:

```bash
# Install all dependencies (frontend + backend)
npm run install-deps
```

This will install:
- Root dependencies (concurrently for running both servers)
- Backend dependencies (Express, PostgreSQL, JWT, etc.)
- Frontend dependencies (React, Tailwind CSS, QR Reader, etc.)

### 3. PostgreSQL Setup

#### Option A: Using Command Line
```bash
# Connect to PostgreSQL
psql -U postgres

# Create database
CREATE DATABASE hauling_qr_system;

# Exit psql
\q
```

#### Option B: Using pgAdmin
1. Open pgAdmin
2. Connect to your PostgreSQL server
3. Right-click "Databases" → "Create" → "Database"
4. Name: `hauling_qr_system`
5. Click "Save"

### 4. Environment Configuration

```bash
# Navigate to server directory
cd server

# Copy environment template
cp .env.example .env

# Edit .env file with your settings
```

Update `server/.env` with your PostgreSQL credentials:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system
DB_USER=postgres
DB_PASSWORD=your_postgres_password
JWT_SECRET=your_super_secure_jwt_secret_key_here
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
```

### 5. Start Development Servers

From the project root directory:

```bash
# Start both frontend and backend servers
npm run dev
```

Or start them separately:

```bash
# Terminal 1 - Backend
npm run server

# Terminal 2 - Frontend  
npm run client
```

### 6. Verify Installation

1. **Backend Health Check**: http://localhost:5000/health
   - Should return: `{"status":"OK","message":"Hauling QR Trip Management System API is running"}`

2. **Frontend Application**: http://localhost:3000
   - Should display the login page with "Step 1 Complete!" message

3. **Database Connection**: Check backend terminal for:
   ```
   📦 Connected to PostgreSQL database
   ✅ Database connection successful: [timestamp]
   ```

## Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Kill process on port 3000
npx kill-port 3000

# Kill process on port 5000
npx kill-port 5000
```

#### PostgreSQL Connection Failed
1. Verify PostgreSQL service is running:
   ```bash
   # Windows
   net start postgresql-x64-13

   # Check service status
   pg_ctl status
   ```

2. Check credentials in `.env` file
3. Verify database exists:
   ```bash
   psql -U postgres -l
   ```

#### Dependencies Installation Failed
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
rm -rf server/node_modules server/package-lock.json  
rm -rf client/node_modules client/package-lock.json
npm run install-deps
```

#### Permission Issues (Windows)
Run terminal as Administrator and try again.

### Verification Checklist

- [ ] Node.js installed and accessible via `node --version`
- [ ] PostgreSQL installed and service running
- [ ] Database `hauling_qr_system` created
- [ ] All dependencies installed without errors
- [ ] Backend server starts on port 5000
- [ ] Frontend server starts on port 3000
- [ ] Health check endpoint responds correctly
- [ ] Frontend displays login page
- [ ] No console errors in browser

## Next Steps

Once Step 1 is complete and verified:

1. **Confirm completion** with the project team
2. **Proceed to Step 2**: Database Creation
   - SQL schema creation
   - Table relationships
   - Sample data insertion

## File Structure Created

```
hauling-qr-trip-system/
├── package.json                     ✅ Root configuration
├── README.md                        ✅ Project documentation
├── INSTALLATION.md                  ✅ Setup instructions
├── ARCHITECTURE_PLAN.md             ✅ Technical architecture
├── .gitignore                       ✅ Git ignore rules
├── client/                          ✅ React Frontend
│   ├── package.json                 ✅ Frontend dependencies
│   ├── tailwind.config.js           ✅ Tailwind configuration
│   ├── postcss.config.js            ✅ PostCSS configuration
│   ├── public/index.html            ✅ HTML template
│   └── src/
│       ├── index.js                 ✅ React entry point
│       ├── index.css                ✅ Global styles
│       ├── App.js                   ✅ Main App component
│       ├── components/
│       │   ├── AppRoutes.js         ✅ Route configuration
│       │   ├── common/
│       │   │   └── LoadingSpinner.js ✅ Loading component
│       │   └── layout/
│       │       └── DashboardLayout.js ✅ Dashboard layout
│       ├── context/
│       │   └── AuthContext.js       ✅ Authentication context
│       └── pages/
│           └── auth/
│               └── LoginPage.js     ✅ Login page
└── server/                          ✅ Node.js Backend
    ├── package.json                 ✅ Backend dependencies
    ├── .env.example                 ✅ Environment template
    ├── server.js                    ✅ Express server
    ├── config/
    │   └── database.js              ✅ Database configuration
    └── routes/                      ✅ API route placeholders
        ├── auth.js                  ✅ Authentication routes
        ├── trucks.js                ✅ Truck management routes
        ├── drivers.js               ✅ Driver management routes
        ├── locations.js             ✅ Location management routes
        ├── assignments.js           ✅ Assignment routes
        ├── trips.js                 ✅ Trip management routes
        ├── scanner.js               ✅ QR scanner routes
        └── analytics.js             ✅ Analytics routes
```

**Status: ✅ Step 1 Complete - Ready for Step 2!**