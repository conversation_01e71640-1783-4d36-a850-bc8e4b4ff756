import React from 'react';

/**
 * Displays exception history for a trip, including approval status and timeline
 */
const ExceptionHistoryCard = ({ trip }) => {
  if (!trip) return null;

  // Check if this trip had an exception that was approved
  const wasExceptionApproved = trip.exception_approved_at && trip.exception_approved_by;
  
  // Parse trip notes if they exist
  let tripNotes = {};
  try {
    if (trip.notes && typeof trip.notes === 'string' && trip.notes.trim() !== '') {
      tripNotes = JSON.parse(trip.notes);
    } else if (trip.notes && typeof trip.notes === 'object') {
      tripNotes = trip.notes;
    }
  } catch (error) {
    console.error('Failed to parse trip notes:', error);
  }

  // If no exception history, don't render anything
  if (!trip.is_exception && !wasExceptionApproved && !tripNotes.revised_flow) {
    return null;
  }

  // Format the timestamp
  const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-white border border-secondary-200 rounded-lg p-4 mt-4">
      <h3 className="text-lg font-medium text-secondary-900 mb-2 flex items-center">
        <span className="mr-2">⚠️</span>
        Exception History
      </h3>

      <div className="mt-3 space-y-3">
        {/* Current Exception Status */}
        {trip.is_exception && (
          <div className={`p-3 rounded-lg ${trip.status === 'exception_pending' 
            ? 'bg-danger-50 border border-danger-200' 
            : 'bg-warning-50 border border-warning-200'}`}>
            <div className="flex items-center">
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center 
                ${trip.status === 'exception_pending' ? 'bg-danger-100 text-danger-600' : 'bg-warning-100 text-warning-600'}`}>
                {trip.status === 'exception_pending' ? '⚠️' : '⏱️'}
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium">
                  {trip.status === 'exception_pending' ? 'Pending Approval' : 'Exception Reported'}
                </h4>
                <p className="text-sm">{trip.exception_reason}</p>
              </div>
            </div>
          </div>
        )}

        {/* Approved Exception */}
        {wasExceptionApproved && (
          <div className="p-3 rounded-lg bg-success-50 border border-success-200">
            <div className="flex items-center">
              <div className="flex-shrink-0 w-8 h-8 bg-success-100 text-success-600 rounded-full flex items-center justify-center">
                <span>✅</span>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium">Exception Approved</h4>
                <p className="text-xs text-secondary-500">
                  Approved at: {formatDateTime(trip.exception_approved_at)}
                </p>
              </div>
            </div>

            {tripNotes.revised_flow && (
              <div className="mt-2 text-sm">
                <div className="font-medium">Revised Route:</div>
                <div className="text-secondary-700">{tripNotes.revised_flow}</div>
              </div>
            )}
            
            {tripNotes.approval_timestamp && (
              <div className="mt-2 text-xs text-secondary-500">
                Approval processed: {formatDateTime(tripNotes.approval_timestamp)}
              </div>
            )}
          </div>
        )}

        {/* Original Assignment Info (if available) */}
        {tripNotes.original_assignment_id && (
          <div className="mt-2 text-sm border-t border-secondary-200 pt-2">
            <div className="font-medium text-secondary-700">Original Assignment ID:</div>
            <div className="text-secondary-600">#{tripNotes.original_assignment_id}</div>
            
            {tripNotes.is_multiple_deviation && (
              <div className="mt-1 text-xs font-medium text-warning-600">
                Multiple deviations detected for this trip
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ExceptionHistoryCard;
