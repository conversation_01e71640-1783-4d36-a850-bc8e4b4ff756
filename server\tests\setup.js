/**
 * Jest Test Setup
 * 
 * This file runs before all tests and sets up the testing environment
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-for-testing-only';
process.env.DB_NAME = 'hauling_qr_system_test';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_USER = 'postgres';
process.env.DB_PASSWORD = 'PostgreSQLPassword';

// Increase timeout for database operations
jest.setTimeout(30000);

// Global test utilities
global.testUtils = {
  // Helper to generate unique test identifiers
  generateTestId: () => `TEST-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  
  // Helper to wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Helper to clean up test data
  cleanupTestData: async (client, testIds) => {
    try {
      await client.query('BEGIN');
      
      // Clean up in reverse dependency order
      for (const id of testIds) {
        await client.query('DELETE FROM approvals WHERE trip_log_id IN (SELECT id FROM trip_logs WHERE assignment_id IN (SELECT id FROM assignments WHERE assignment_code LIKE $1))', [`%${id}%`]);
        await client.query('DELETE FROM trip_logs WHERE assignment_id IN (SELECT id FROM assignments WHERE assignment_code LIKE $1)', [`%${id}%`]);
        await client.query('DELETE FROM assignments WHERE assignment_code LIKE $1', [`%${id}%`]);
        await client.query('DELETE FROM locations WHERE location_code LIKE $1', [`%${id}%`]);
        await client.query('DELETE FROM dump_trucks WHERE truck_number LIKE $1', [`%${id}%`]);
        await client.query('DELETE FROM drivers WHERE employee_id LIKE $1', [`%${id}%`]);
        await client.query('DELETE FROM users WHERE username LIKE $1', [`%${id}%`]);
      }
      
      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Cleanup error:', error);
    }
  }
};

// Console override for cleaner test output
const originalConsoleError = console.error;
console.error = (...args) => {
  // Only show errors that aren't expected test errors
  if (!args[0]?.toString().includes('Test cleanup') && 
      !args[0]?.toString().includes('Expected test error')) {
    originalConsoleError(...args);
  }
};

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Cleanup after all tests
afterAll(async () => {
  // Give time for any pending operations to complete
  await global.testUtils.wait(1000);
});
