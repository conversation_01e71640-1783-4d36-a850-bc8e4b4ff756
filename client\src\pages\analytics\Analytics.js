import React, { useState, useEffect, useCallback } from 'react';
import { analyticsAPI } from '../../services/api';
import DashboardStats from './components/DashboardStats';
import PerformanceCharts from './components/PerformanceCharts';
import ExceptionsReport from './components/ExceptionsReport';
import toast from 'react-hot-toast';

const Analytics = () => {
  const [loading, setLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [performanceData, setPerformanceData] = useState(null);
  const [exceptionsData, setExceptionsData] = useState(null);

  // Filter states
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end: new Date().toISOString().split('T')[0] // today
  });

  const [activeTab, setActiveTab] = useState('dashboard');

  // Load dashboard data
  const loadDashboardData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await analyticsAPI.getDashboard();
      setDashboardData(response.data.data); // FIX: use .data.data for correct structure
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
      // Set fallback data to prevent crashes
      setDashboardData({
        fleet: { totalTrucks: 0, activeTrucks: 0, totalDrivers: 0, activeDrivers: 0, totalLocations: 0, activeLocations: 0 },
        trips: { todayTrips: 0, todayCompleted: 0, weeklyTrips: 0, weeklyCompleted: 0, monthlyTrips: 0, monthlyCompleted: 0 },
        performance: { avgTripTime: 0, completionRate: 0, onTimeRate: 0, exceptionRate: 0 }
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Transform backend data for charts
  const transformPerformanceData = (backendData, routeData = null) => {
    if (!backendData) return null;

    // Transform trends data for trip volume chart
    const tripVolume = {
      labels: backendData.trends?.map(trend => 
        new Date(trend.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      ) || [],
      completed: backendData.trends?.map(trend => trend.completedTrips) || [],
      assigned: backendData.trends?.map(trend => trend.totalTrips) || []
    };

    // Transform for efficiency chart (using average duration data)
    const efficiency = {
      labels: backendData.trends?.slice(-7).map(trend => 
        new Date(trend.date).toLocaleDateString('en-US', { weekday: 'short' })
      ) || ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      avgTripTime: backendData.trends?.slice(-7).map(trend => trend.avgDuration || 0) || [0, 0, 0, 0, 0, 0, 0],
      target: 150 // Default target time
    };

    // Transform truck utilization data
    const trucks = backendData.truckUtilization?.map(truck => ({
      name: truck.truckNumber,
      utilization: parseFloat(truck.completionRate),
      trips: truck.tripCount
    })) || [];

    // Transform driver performance data
    const drivers = backendData.driverPerformance?.map(driver => ({
      name: driver.driverName,
      utilization: parseFloat(driver.completionRate),
      trips: driver.tripCount
    })) || [];

    // Use real route data if available, otherwise empty array
    const routes = routeData ? {
      performance: routeData.performance || []
    } : {
      performance: []
    };

    return {
      tripVolume,
      efficiency,
      utilization: {
        trucks,
        drivers
      },
      routes
    };
  };

  // Load performance data - FIXED API CALL
  const loadPerformanceData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Fix the API calls - pass parameters correctly
      const [performanceResponse, routeResponse] = await Promise.all([
        analyticsAPI.getPerformance({
          start_date: dateRange.start,
          end_date: dateRange.end
        }),
        analyticsAPI.getRoutes({
          start_date: dateRange.start,
          end_date: dateRange.end
        })
      ]);
      
      const transformedData = transformPerformanceData(
        performanceResponse.data.data, // Fixed: access .data.data from backend response
        routeResponse.data.data
      );
      setPerformanceData(transformedData);
    } catch (error) {
      console.error('Error loading performance data:', error);
      toast.error('Failed to load performance data');
      // Set fallback data to prevent crashes
      setPerformanceData({
        tripVolume: { labels: [], completed: [], assigned: [] },
        efficiency: { labels: [], avgTripTime: [], target: 150 },
        utilization: { trucks: [], drivers: [] },
        routes: { performance: [] }
      });
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  // Load exceptions data - FIXED API CALL
  const loadExceptionsData = useCallback(async () => {
    try {
      setLoading(true);
      
      const response = await analyticsAPI.getExceptions({
        start_date: dateRange.start,
        end_date: dateRange.end
      });
      
      setExceptionsData(response.data.data); // Fixed: access .data.data from backend response
    } catch (error) {
      console.error('Error loading exceptions data:', error);
      toast.error('Failed to load exceptions data');
      // Set fallback data to prevent crashes
      setExceptionsData({
        summary: { total: 0, pending: 0, resolved: 0, rate: '0.0' },
        recent: [],
        trends: []
      });
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  // Initial load
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // Load data based on active tab
  useEffect(() => {
    if (activeTab === 'performance') {
      loadPerformanceData();
    } else if (activeTab === 'exceptions') {
      loadExceptionsData();
    }
  }, [activeTab, loadPerformanceData, loadExceptionsData]);

  // Handle date range change
  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle refresh
  const handleRefresh = () => {
    if (activeTab === 'dashboard') {
      loadDashboardData();
    } else if (activeTab === 'performance') {
      loadPerformanceData();
    } else if (activeTab === 'exceptions') {
      loadExceptionsData();
    }
    toast.success('Data refreshed');
  };

  // Handle data export
  const handleExportData = async (data, format) => {
    try {
      if (format === 'csv') {
        exportToCSV(data);
      } else if (format === 'pdf') {
        exportToPDF(data);
      }
      toast.success(`Data exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export data');
    }
  };

  // Export to CSV
  const exportToCSV = (data) => {
    let csvContent = '';
    
    // Trip Volume Data
    if (data?.tripVolume && data.tripVolume.labels?.length > 0) {
      csvContent += 'Trip Volume Data\n';
      csvContent += 'Period,Completed Trips,Assigned Trips\n';
      data.tripVolume.labels.forEach((label, index) => {
        csvContent += `${label},${data.tripVolume.completed[index] || 0},${data.tripVolume.assigned[index] || 0}\n`;
      });
      csvContent += '\n';
    }

    // Efficiency Data
    if (data?.efficiency && data.efficiency.labels?.length > 0) {
      csvContent += 'Efficiency Data\n';
      csvContent += 'Day,Average Trip Time (minutes),Target Time\n';
      data.efficiency.labels.forEach((label, index) => {
        csvContent += `${label},${data.efficiency.avgTripTime[index] || 0},${data.efficiency.target}\n`;
      });
      csvContent += '\n';
    }

    // Truck Utilization
    if (data?.utilization?.trucks && data.utilization.trucks.length > 0) {
      csvContent += 'Truck Utilization\n';
      csvContent += 'Truck,Utilization %,Trips\n';
      data.utilization.trucks.forEach(truck => {
        csvContent += `${truck.name},${truck.utilization},${truck.trips}\n`;
      });
      csvContent += '\n';
    }

    // Driver Performance
    if (data?.utilization?.drivers && data.utilization.drivers.length > 0) {
      csvContent += 'Driver Performance\n';
      csvContent += 'Driver,Utilization %,Trips\n';
      data.utilization.drivers.forEach(driver => {
        csvContent += `${driver.name},${driver.utilization},${driver.trips}\n`;
      });
      csvContent += '\n';
    }

    // Route Performance
    if (data?.routes?.performance && data.routes.performance.length > 0) {
      csvContent += 'Route Performance\n';
      csvContent += 'Route,Average Time (minutes),Trips,Efficiency %\n';
      data.routes.performance.forEach(route => {
        csvContent += `${route.route},${route.avgTime},${route.trips},${route.efficiency}\n`;
      });
    }

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `analytics-data-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Export to PDF (simplified version)
  const exportToPDF = (data) => {
    const printWindow = window.open('', '_blank');
    let htmlContent = `
      <html>
        <head>
          <title>Analytics Report - ${new Date().toLocaleDateString()}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            h1, h2 { color: #333; }
            .section { margin-bottom: 30px; }
          </style>
        </head>
        <body>
          <h1>Analytics Report</h1>
          <p>Generated on: ${new Date().toLocaleDateString()}</p>
          <p>Period: ${formatDateRange()}</p>
    `;

    // Trip Volume Table
    if (data?.tripVolume && data.tripVolume.labels?.length > 0) {
      htmlContent += `
        <div class="section">
          <h2>Trip Volume Data</h2>
          <table>
            <thead>
              <tr><th>Period</th><th>Completed Trips</th><th>Assigned Trips</th></tr>
            </thead>
            <tbody>
      `;
      data.tripVolume.labels.forEach((label, index) => {
        htmlContent += `<tr><td>${label}</td><td>${data.tripVolume.completed[index] || 0}</td><td>${data.tripVolume.assigned[index] || 0}</td></tr>`;
      });
      htmlContent += '</tbody></table></div>';
    }

    // Truck Utilization Table
    if (data?.utilization?.trucks && data.utilization.trucks.length > 0) {
      htmlContent += `
        <div class="section">
          <h2>Truck Utilization</h2>
          <table>
            <thead>
              <tr><th>Truck</th><th>Utilization %</th><th>Trips</th></tr>
            </thead>
            <tbody>
      `;
      data.utilization.trucks.forEach(truck => {
        htmlContent += `<tr><td>${truck.name}</td><td>${truck.utilization}%</td><td>${truck.trips}</td></tr>`;
      });
      htmlContent += '</tbody></table></div>';
    }

    htmlContent += '</body></html>';
    
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };

  const formatDateRange = () => {
    const start = new Date(dateRange.start).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    const end = new Date(dateRange.end).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    return `${start} - ${end}`;
  };

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'performance', name: 'Performance', icon: '📈' },
    { id: 'exceptions', name: 'Exceptions', icon: '⚠️' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Analytics & Reports</h1>
          <p className="text-secondary-600 mt-1">Performance metrics and operational insights</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {/* Date Range Selector */}
          {activeTab !== 'dashboard' && (
            <div className="flex items-center space-x-2">
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => handleDateRangeChange('start', e.target.value)}
                className="text-sm border-secondary-300 rounded-md"
              />
              <span className="text-secondary-500">to</span>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => handleDateRangeChange('end', e.target.value)}
                className="text-sm border-secondary-300 rounded-md"
              />
            </div>
          )}
          
          <button
            onClick={handleRefresh}
            className="btn btn-secondary"
            disabled={loading}
          >
            <svg className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {loading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white shadow-sm border border-secondary-200 rounded-lg">
        <div className="border-b border-secondary-200">
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
              >
                <span className="text-lg">{tab.icon}</span>
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'dashboard' && (
            <DashboardStats 
              data={dashboardData} 
              loading={loading} 
            />
          )}

          {activeTab === 'performance' && (
            <PerformanceCharts 
              data={performanceData} 
              loading={loading}
              dateRange={dateRange}
              onExportData={handleExportData}
            />
          )}

          {activeTab === 'exceptions' && (
            <ExceptionsReport 
              data={exceptionsData} 
              loading={loading}
              dateRange={dateRange}
            />
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-secondary-200">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button 
            className="flex items-center p-4 border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors"
            onClick={() => handleExportData(performanceData || dashboardData, 'csv')}
          >
            <span className="text-2xl mr-3">📄</span>
            <div className="text-left">
              <h4 className="font-medium text-secondary-900">Export Report (CSV)</h4>
              <p className="text-sm text-secondary-500">Download analytics data as CSV</p>
            </div>
          </button>

          <button 
            className="flex items-center p-4 border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors"
            onClick={() => handleExportData(performanceData || dashboardData, 'pdf')}
          >
            <span className="text-2xl mr-3">📄</span>
            <div className="text-left">
              <h4 className="font-medium text-secondary-900">Export Report (PDF)</h4>
              <p className="text-sm text-secondary-500">Download analytics data as PDF</p>
            </div>
          </button>

          <button className="flex items-center p-4 border border-secondary-300 rounded-lg hover:bg-secondary-50 transition-colors">
            <span className="text-2xl mr-3">🔔</span>
            <div className="text-left">
              <h4 className="font-medium text-secondary-900">Alert Settings</h4>
              <p className="text-sm text-secondary-500">Configure performance alerts</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Analytics;