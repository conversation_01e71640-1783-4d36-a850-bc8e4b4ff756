const winston = require('winston');
const path = require('path');

// Custom log format for enhanced readability
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, service, context, data, error, stack, ...meta }) => {
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      service: service || 'HAULING-SYSTEM',
      context: context || 'GENERAL',
      message,
      ...(data && { data }),
      ...(error && { error }),
      ...(stack && { stack }),
      ...meta
    };
    return JSON.stringify(logEntry, null, 2);
  })
);

// Create winston logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'hauling-qr-system' },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    // File transport for errors
    new winston.transports.File({
      filename: path.join(__dirname, '../logs/error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    // File transport for all logs
    new winston.transports.File({
      filename: path.join(__dirname, '../logs/combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 10
    })
  ]
});

// Enhanced logging functions with context and structured data
class EnhancedLogger {
  static logScanRequest(context, scanData, userInfo) {
    logger.info('Processing scan request', {
      context: `SCANNER.${context}`,
      data: {
        scan_type: scanData.scan_type,
        user_id: userInfo.id,
        user_role: userInfo.role,
        ip_address: scanData.ip_address,
        user_agent: scanData.user_agent,
        session_id: userInfo.session_id,
        request_id: scanData.request_id || this.generateRequestId()
      },
      performance: {
        request_start: Date.now()
      }
    });
  }

  static logDatabaseQuery(queryText, params, duration, rowCount, context = 'DB_QUERY') {
    const isSlowQuery = duration > 1000; // Mark queries over 1 second as slow
    const level = isSlowQuery ? 'warn' : 'info';
    
    logger.log(level, `Database query executed`, {
      context: `DATABASE.${context}`,
      data: {
        query_type: this.extractQueryType(queryText),
        duration_ms: duration,
        row_count: rowCount,
        is_slow_query: isSlowQuery,
        params_count: params ? params.length : 0
      },
      query: {
        text: queryText.replace(/\s+/g, ' ').trim(),
        ...(params && params.length > 0 && { parameters: params })
      },
      performance: {
        execution_time_ms: duration,
        rows_per_ms: rowCount / duration
      }
    });
  }

  static logDatabaseError(error, queryText, params, context = 'DB_ERROR') {
    logger.error('Database query failed', {
      context: `DATABASE.${context}`,
      error: {
        message: error.message,
        code: error.code,
        constraint: error.constraint,
        table: error.table,
        column: error.column,
        severity: error.severity
      },
      query: {
        text: queryText ? queryText.replace(/\s+/g, ' ').trim() : 'Unknown',
        ...(params && params.length > 0 && { parameters: params })
      },
      stack: error.stack,
      troubleshooting: this.getDatabaseErrorSuggestions(error)
    });
  }

  static logScanError(context, error, scanData, userInfo) {
    logger.error('Scan processing failed', {
      context: `SCANNER.${context}`,
      error: {
        message: error.message,
        type: error.constructor.name,
        code: error.code
      },
      data: {
        scan_type: scanData.scan_type,
        user_id: userInfo?.id,
        ip_address: scanData.ip_address,
        qr_data_valid: this.isValidJSON(scanData.scanned_data)
      },
      stack: error.stack,
      troubleshooting: this.getScanErrorSuggestions(error, scanData)
    });
  }

  static logAssignmentLookup(truckId, searchCriteria, results, context = 'ASSIGNMENT_LOOKUP') {
    logger.info('Assignment lookup performed', {
      context: `SCANNER.${context}`,
      data: {
        truck_id: truckId,
        search_criteria: searchCriteria,
        results_found: results.length,
        assignments: results.map(r => ({
          id: r.id,
          status: r.status,
          assigned_date: r.assigned_date,
          loading_location: r.loading_location_name,
          unloading_location: r.unloading_location_name
        }))
      },
      troubleshooting: results.length === 0 ? this.getNoAssignmentSuggestions(truckId) : null
    });
  }

  static logRouteDeviation(truckId, expectedLocation, actualLocation, severity = 'medium') {
    logger.warn('Route deviation detected', {
      context: 'SCANNER.ROUTE_DEVIATION',
      data: {
        truck_id: truckId,
        expected_location: {
          id: expectedLocation.id,
          name: expectedLocation.name,
          type: expectedLocation.type
        },
        actual_location: {
          id: actualLocation.id,
          name: actualLocation.name,
          type: actualLocation.type
        },
        severity,
        deviation_type: this.getDeviationType(expectedLocation, actualLocation)
      },
      impact: {
        requires_approval: true,
        affects_schedule: severity !== 'low',
        cost_implications: severity === 'high' || severity === 'critical'
      }
    });
  }

  static logPerformanceMetrics(context, metrics) {
    logger.info('Performance metrics captured', {
      context: `PERFORMANCE.${context}`,
      data: metrics,
      analysis: {
        is_performance_issue: metrics.processing_time_ms > 5000,
        bottleneck_detected: this.identifyBottleneck(metrics)
      }
    });
  }

  static logConnectionPoolStatus(poolStats) {
    const isHealthy = poolStats.idleCount > 0 && poolStats.totalCount < poolStats.max;
    const level = isHealthy ? 'info' : 'warn';
    
    logger.log(level, 'Database connection pool status', {
      context: 'DATABASE.POOL',
      data: {
        total_connections: poolStats.totalCount,
        idle_connections: poolStats.idleCount,
        waiting_clients: poolStats.waitingCount,
        max_connections: poolStats.max,
        pool_utilization_percent: Math.round((poolStats.totalCount / poolStats.max) * 100)
      },
      health: {
        is_healthy: isHealthy,
        warnings: this.getPoolWarnings(poolStats)
      }
    });
  }

  // Helper methods
  static generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  static extractQueryType(queryText) {
    if (!queryText) return 'UNKNOWN';
    const trimmed = queryText.trim().toUpperCase();
    if (trimmed.startsWith('SELECT')) return 'SELECT';
    if (trimmed.startsWith('INSERT')) return 'INSERT';
    if (trimmed.startsWith('UPDATE')) return 'UPDATE';
    if (trimmed.startsWith('DELETE')) return 'DELETE';
    if (trimmed.startsWith('WITH')) return 'CTE';
    return 'OTHER';
  }

  static isValidJSON(str) {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }

  static getDatabaseErrorSuggestions(error) {
    const suggestions = [];
    
    if (error.message.includes('connection timeout')) {
      suggestions.push('Check database connection pool configuration');
      suggestions.push('Verify database server is responding');
      suggestions.push('Consider increasing connection timeout');
    }
    
    if (error.message.includes('column') && error.message.includes('ambiguous')) {
      suggestions.push('Use table aliases to resolve column ambiguity');
      suggestions.push('Specify table names explicitly in SELECT statements');
    }
    
    if (error.code === '23505') { // Unique violation
      suggestions.push('Check for duplicate entries before insertion');
      suggestions.push('Use ON CONFLICT clauses for upsert operations');
    }
    
    return suggestions;
  }

  static getScanErrorSuggestions(error, scanData) {
    const suggestions = [];
    
    if (error.message.includes('No active assignment')) {
      suggestions.push('Verify truck has assignment for current date');
      suggestions.push('Check assignment status is "assigned" or "in_progress"');
      suggestions.push('Ensure truck status is "active"');
    }
    
    if (error.message.includes('QR code')) {
      suggestions.push('Verify QR code format and structure');
      suggestions.push('Check QR code matches expected type');
      suggestions.push('Ensure QR code data is valid JSON');
    }
    
    return suggestions;
  }

  static getNoAssignmentSuggestions(truckId) {
    return [
      `Create an assignment for truck ${truckId} for today`,
      'Verify truck status is active in database',
      'Check if assignment exists with different status',
      'Ensure assignment date matches current date'
    ];
  }

  static getDeviationType(expected, actual) {
    if (expected.type !== actual.type) {
      return 'LOCATION_TYPE_MISMATCH';
    }
    if (expected.id !== actual.id) {
      return 'LOCATION_CHANGE';
    }
    return 'UNKNOWN';
  }

  static identifyBottleneck(metrics) {
    if (metrics.database_time_ms > metrics.processing_time_ms * 0.7) {
      return 'DATABASE';
    }
    if (metrics.validation_time_ms > metrics.processing_time_ms * 0.3) {
      return 'VALIDATION';
    }
    if (metrics.network_time_ms > metrics.processing_time_ms * 0.2) {
      return 'NETWORK';
    }
    return 'NONE';
  }

  static getPoolWarnings(poolStats) {
    const warnings = [];
    
    if (poolStats.totalCount >= poolStats.max * 0.9) {
      warnings.push('Pool utilization is high (>90%)');
    }
    
    if (poolStats.waitingCount > 0) {
      warnings.push(`${poolStats.waitingCount} clients waiting for connections`);
    }
    
    if (poolStats.idleCount === 0) {
      warnings.push('No idle connections available');
    }
    
    return warnings;
  }
}

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

module.exports = {
  logger,
  EnhancedLogger
};