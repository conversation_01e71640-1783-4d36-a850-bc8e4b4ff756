const jwt = require('jsonwebtoken');

/**
 * JWT Authentication Middleware
 * Verifies JWT token from Authorization header
 * Adds user info to req.user if valid
 */
module.exports = (req, res, next) => {
  // Get token from header
  const token = req.header('Authorization');

  // Check if no token
  if (!token) {
    return res.status(401).json({
      error: 'Access Denied',
      message: 'No token provided. Please login to access this resource.'
    });
  }

  try {
    // Extract token from "Bearer <token>" format
    let tokenValue = token;
    if (token.startsWith('Bearer ')) {
      tokenValue = token.slice(7, token.length);
    }

    // Verify token
    const decoded = jwt.verify(tokenValue, process.env.JWT_SECRET);
    
    // Add user from payload to request object
    req.user = decoded.user;
    
    next();
  } catch (error) {
    // Token is not valid
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token Expired',
        message: 'Your session has expired. Please login again.'
      });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid Token',
        message: 'Invalid token provided. Please login again.'
      });
    } else {
      return res.status(401).json({
        error: 'Authentication Failed',
        message: 'Token verification failed.'
      });
    }
  }
};