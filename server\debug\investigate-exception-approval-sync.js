/**
 * Investigate Exception Approval Workflow Synchronization Issues
 * 
 * This script examines the database state and exception approval workflow to identify:
 * 1. Undefined route deviation message issue
 * 2. Assignment status not synchronized after exception approval
 * 3. Trip status inconsistency
 */

const { getClient } = require('../config/database');

async function investigateExceptionApprovalSync() {
  console.log('🔍 Investigating Exception Approval Workflow Synchronization Issues...\n');
  
  const client = await getClient();
  
  try {
    // Issue Investigation: Check DT-100 data for Jun 27, 2025
    console.log('📊 Examining DT-100 data for Jun 27, 2025...\n');
    
    // 1. Check assignments
    console.log('1️⃣ ASSIGNMENTS for DT-100:');
    await examineAssignments(client);
    
    // 2. Check trip logs
    console.log('\n2️⃣ TRIP LOGS for DT-100:');
    await examineTripLogs(client);
    
    // 3. Check approvals/exceptions
    console.log('\n3️⃣ APPROVALS/EXCEPTIONS for DT-100:');
    await examineApprovals(client);
    
    // 4. Check relationships between all three
    console.log('\n4️⃣ RELATIONSHIP ANALYSIS:');
    await analyzeRelationships(client);
    
    // 5. Test exception approval workflow
    console.log('\n5️⃣ EXCEPTION APPROVAL WORKFLOW ANALYSIS:');
    await analyzeExceptionWorkflow(client);
    
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    client.release();
  }
}

async function examineAssignments(client) {
  const assignmentsResult = await client.query(`
    SELECT 
        a.id,
        a.assignment_code,
        a.status,
        a.assigned_date,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        a.created_at,
        a.updated_at
    FROM assignments a
    JOIN dump_trucks dt ON a.truck_id = dt.id
    LEFT JOIN locations ll ON a.loading_location_id = ll.id
    LEFT JOIN locations ul ON a.unloading_location_id = ul.id
    WHERE dt.truck_number = 'DT-100'
        AND a.assigned_date >= '2025-06-27'
    ORDER BY a.created_at DESC
  `);
  
  if (assignmentsResult.rows.length === 0) {
    console.log('   ❌ No assignments found for DT-100 on/after Jun 27, 2025');
    return;
  }
  
  assignmentsResult.rows.forEach((assignment, index) => {
    console.log(`   ${index + 1}. Assignment ID: ${assignment.id}`);
    console.log(`      Code: ${assignment.assignment_code}`);
    console.log(`      Status: ${assignment.status} ${assignment.status === 'pending_approval' ? '⚠️' : '✅'}`);
    console.log(`      Date: ${assignment.assigned_date}`);
    console.log(`      Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
    console.log(`      Created: ${assignment.created_at}`);
    console.log(`      Updated: ${assignment.updated_at}`);
    console.log('');
  });
}

async function examineTripLogs(client) {
  const tripsResult = await client.query(`
    SELECT 
        tl.id,
        tl.trip_number,
        tl.status,
        tl.assignment_id,
        tl.is_exception,
        tl.exception_approved_by,
        tl.exception_approved_at,
        tl.loading_start_time,
        tl.trip_completed_time,
        tl.notes,
        a.assignment_code,
        dt.truck_number
    FROM trip_logs tl
    JOIN assignments a ON tl.assignment_id = a.id
    JOIN dump_trucks dt ON a.truck_id = dt.id
    WHERE dt.truck_number = 'DT-100'
        AND a.assigned_date >= '2025-06-27'
    ORDER BY tl.created_at DESC
  `);
  
  if (tripsResult.rows.length === 0) {
    console.log('   ❌ No trip logs found for DT-100 on/after Jun 27, 2025');
    return;
  }
  
  tripsResult.rows.forEach((trip, index) => {
    console.log(`   ${index + 1}. Trip ID: ${trip.id} (Trip #${trip.trip_number})`);
    console.log(`      Status: ${trip.status} ${trip.status === 'trip_completed' ? '✅' : '⚠️'}`);
    console.log(`      Assignment: ${trip.assignment_code} (ID: ${trip.assignment_id})`);
    console.log(`      Is Exception: ${trip.is_exception ? 'Yes' : 'No'}`);
    if (trip.exception_approved_by) {
      console.log(`      Exception Approved By: ${trip.exception_approved_by}`);
      console.log(`      Exception Approved At: ${trip.exception_approved_at}`);
    }
    console.log(`      Loading Start: ${trip.loading_start_time}`);
    console.log(`      Trip Completed: ${trip.trip_completed_time}`);
    if (trip.notes) {
      try {
        const notes = typeof trip.notes === 'string' ? JSON.parse(trip.notes) : trip.notes;
        console.log(`      Notes: ${JSON.stringify(notes, null, 8)}`);
      } catch (e) {
        console.log(`      Notes: ${trip.notes}`);
      }
    }
    console.log('');
  });
}

async function examineApprovals(client) {
  const approvalsResult = await client.query(`
    SELECT 
        ap.id,
        ap.trip_log_id,
        ap.exception_type,
        ap.exception_description,
        ap.status,
        ap.reviewed_by,
        ap.reviewed_at,
        ap.created_at,
        tl.trip_number,
        a.assignment_code,
        dt.truck_number
    FROM approvals ap
    JOIN trip_logs tl ON ap.trip_log_id = tl.id
    JOIN assignments a ON tl.assignment_id = a.id
    JOIN dump_trucks dt ON a.truck_id = dt.id
    WHERE dt.truck_number = 'DT-100'
        AND a.assigned_date >= '2025-06-27'
    ORDER BY ap.created_at DESC
  `);
  
  if (approvalsResult.rows.length === 0) {
    console.log('   ❌ No approvals found for DT-100 on/after Jun 27, 2025');
    return;
  }
  
  approvalsResult.rows.forEach((approval, index) => {
    console.log(`   ${index + 1}. Approval ID: ${approval.id}`);
    console.log(`      Trip: ${approval.trip_number} (Trip Log ID: ${approval.trip_log_id})`);
    console.log(`      Assignment: ${approval.assignment_code}`);
    console.log(`      Exception Type: ${approval.exception_type}`);
    console.log(`      Description: ${approval.exception_description}`);
    console.log(`      Status: ${approval.status} ${approval.status === 'approved' ? '✅' : '⚠️'}`);
    console.log(`      Reviewed By: ${approval.reviewed_by || 'Not reviewed'}`);
    console.log(`      Reviewed At: ${approval.reviewed_at || 'Not reviewed'}`);
    console.log(`      Created: ${approval.created_at}`);
    console.log('');
  });
}

async function analyzeRelationships(client) {
  const relationshipResult = await client.query(`
    SELECT 
        a.id as assignment_id,
        a.assignment_code,
        a.status as assignment_status,
        tl.id as trip_id,
        tl.trip_number,
        tl.status as trip_status,
        tl.is_exception,
        ap.id as approval_id,
        ap.status as approval_status,
        ap.exception_type,
        dt.truck_number
    FROM assignments a
    JOIN dump_trucks dt ON a.truck_id = dt.id
    LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
    LEFT JOIN approvals ap ON tl.id = ap.trip_log_id
    WHERE dt.truck_number = 'DT-100'
        AND a.assigned_date >= '2025-06-27'
    ORDER BY a.created_at DESC, tl.trip_number DESC
  `);
  
  if (relationshipResult.rows.length === 0) {
    console.log('   ❌ No relationship data found');
    return;
  }
  
  // Group by assignment
  const assignmentGroups = {};
  relationshipResult.rows.forEach(row => {
    if (!assignmentGroups[row.assignment_id]) {
      assignmentGroups[row.assignment_id] = {
        assignment: {
          id: row.assignment_id,
          code: row.assignment_code,
          status: row.assignment_status
        },
        trips: [],
        approvals: []
      };
    }
    
    if (row.trip_id && !assignmentGroups[row.assignment_id].trips.find(t => t.id === row.trip_id)) {
      assignmentGroups[row.assignment_id].trips.push({
        id: row.trip_id,
        number: row.trip_number,
        status: row.trip_status,
        is_exception: row.is_exception
      });
    }
    
    if (row.approval_id && !assignmentGroups[row.assignment_id].approvals.find(a => a.id === row.approval_id)) {
      assignmentGroups[row.assignment_id].approvals.push({
        id: row.approval_id,
        status: row.approval_status,
        type: row.exception_type
      });
    }
  });
  
  Object.values(assignmentGroups).forEach((group, index) => {
    console.log(`   ${index + 1}. Assignment ${group.assignment.code} (ID: ${group.assignment.id})`);
    console.log(`      Assignment Status: ${group.assignment.status} ${group.assignment.status === 'pending_approval' ? '⚠️ ISSUE' : '✅'}`);
    
    console.log(`      Trips (${group.trips.length}):`);
    group.trips.forEach(trip => {
      console.log(`        - Trip #${trip.number}: ${trip.status} ${trip.is_exception ? '(Exception)' : ''}`);
    });
    
    console.log(`      Approvals (${group.approvals.length}):`);
    group.approvals.forEach(approval => {
      console.log(`        - ${approval.type}: ${approval.status}`);
    });
    
    // Identify inconsistencies
    const hasApprovedExceptions = group.approvals.some(a => a.status === 'approved');
    const hasCompletedTrips = group.trips.some(t => t.status === 'trip_completed');
    const assignmentPending = group.assignment.status === 'pending_approval';
    
    if (hasApprovedExceptions && assignmentPending) {
      console.log(`      🚨 INCONSISTENCY: Assignment is 'pending_approval' but has approved exceptions!`);
    }
    
    if (hasCompletedTrips && assignmentPending) {
      console.log(`      🚨 INCONSISTENCY: Assignment is 'pending_approval' but has completed trips!`);
    }
    
    console.log('');
  });
}

async function analyzeExceptionWorkflow(client) {
  console.log('   📋 Checking exception approval workflow implementation...\n');
  
  // Check if there are any pending approvals that should trigger assignment updates
  const pendingApprovalsResult = await client.query(`
    SELECT 
        ap.id,
        ap.trip_log_id,
        ap.status as approval_status,
        tl.notes as trip_notes,
        a.id as assignment_id,
        a.status as assignment_status,
        dt.truck_number
    FROM approvals ap
    JOIN trip_logs tl ON ap.trip_log_id = tl.id
    JOIN assignments a ON tl.assignment_id = a.id
    JOIN dump_trucks dt ON a.truck_id = dt.id
    WHERE ap.status = 'approved'
        AND a.status = 'pending_approval'
        AND dt.truck_number = 'DT-100'
  `);
  
  if (pendingApprovalsResult.rows.length > 0) {
    console.log('   🚨 FOUND SYNCHRONIZATION ISSUES:');
    pendingApprovalsResult.rows.forEach((row, index) => {
      console.log(`   ${index + 1}. Approval ID ${row.id} is 'approved' but Assignment ID ${row.assignment_id} is still 'pending_approval'`);
      
      // Check trip notes for pending assignment info
      if (row.trip_notes) {
        try {
          const notes = typeof row.trip_notes === 'string' ? JSON.parse(row.trip_notes) : row.trip_notes;
          if (notes.pending_assignment_id) {
            console.log(`      Trip notes indicate pending assignment ID: ${notes.pending_assignment_id}`);
          }
        } catch (e) {
          console.log(`      Trip notes (raw): ${row.trip_notes}`);
        }
      }
    });
  } else {
    console.log('   ✅ No obvious synchronization issues found in current data');
  }
  
  console.log('\n   📋 Workflow Analysis Summary:');
  console.log('   1. Check if exception approval properly updates assignment status');
  console.log('   2. Verify route deviation message generation');
  console.log('   3. Ensure database transactions are atomic');
  console.log('   4. Validate Trip Flow Logic implementation');
}

// Run the investigation
if (require.main === module) {
  investigateExceptionApprovalSync()
    .then(() => {
      console.log('\n🎯 Investigation completed. Check the output above for issues.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Investigation failed:', error.message);
      process.exit(1);
    });
}

module.exports = { investigateExceptionApprovalSync };
