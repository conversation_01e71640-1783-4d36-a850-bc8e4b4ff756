import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ExceptionsReport from '../ExceptionsReport';

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  error: jest.fn(),
  success: jest.fn(),
}));

describe('ExceptionsReport Component', () => {
  const mockDateRange = {
    start: '2025-05-01',
    end: '2025-05-31'
  };

  test('renders loading state correctly', () => {
    render(<ExceptionsReport loading={true} dateRange={mockDateRange} />);
    
    // Check for loading state by looking for a specific element that appears during loading
    expect(screen.getByTestId('loading-skeleton') || document.body).toBeInTheDocument();
  });

  test('renders no data state when data is null', () => {
    render(<ExceptionsReport data={null} loading={false} dateRange={mockDateRange} />);
    
    expect(screen.getByText('No Exception Data Available')).toBeInTheDocument();
    expect(screen.getByText('Exception reports will appear here once there are trip exceptions.')).toBeInTheDocument();
    expect(screen.getByText('Check your network connection or try refreshing the page.')).toBeInTheDocument();
  });

  test('renders with empty data gracefully', () => {
    const emptyData = {
      summary: {
        total: 0,
        pending: 0,
        resolved: 0,
        rate: '0.0'
      },
      recent: [],
      trends: []
    };

    render(<ExceptionsReport data={emptyData} loading={false} dateRange={mockDateRange} />);
    
    expect(screen.getByText('Exceptions Report')).toBeInTheDocument();
    expect(screen.getByText('Total Exceptions')).toBeInTheDocument();
    expect(screen.getByText('0')).toBeInTheDocument(); // Total exceptions count
    expect(screen.getByText('No exception types data available')).toBeInTheDocument();
    expect(screen.getByText('No recent exceptions found')).toBeInTheDocument();
  });

  test('renders with real exception data', () => {
    const mockData = {
      summary: {
        total: 5,
        pending: 2,
        resolved: 3,
        rate: '10.5'
      },
      recent: [
        {
          id: 1,
          tripNumber: 'T001',
          reason: 'Equipment malfunction during loading',
          truckNumber: 'DT-100',
          driverName: 'John Doe',
          status: 'pending',
          createdAt: '2025-05-15T10:30:00Z'
        },
        {
          id: 2,
          tripNumber: 'T002',
          reason: 'Route deviation due to road closure',
          truckNumber: 'DT-101',
          driverName: 'Jane Smith',
          status: 'resolved',
          createdAt: '2025-05-14T14:20:00Z'
        }
      ],
      trends: [
        { date: '2025-05-15', count: 2 },
        { date: '2025-05-14', count: 1 },
        { date: '2025-05-13', count: 0 }
      ]
    };

    render(<ExceptionsReport data={mockData} loading={false} dateRange={mockDateRange} />);
    
    // Check summary cards
    expect(screen.getByText('5')).toBeInTheDocument(); // Total exceptions
    expect(screen.getByText('2')).toBeInTheDocument(); // Pending
    expect(screen.getByText('3')).toBeInTheDocument(); // Resolved
    expect(screen.getByText('60%')).toBeInTheDocument(); // Resolution rate (3/5 * 100)
    
    // Check recent exceptions table
    expect(screen.getByText('Trip #T001')).toBeInTheDocument();
    expect(screen.getByText('Trip #T002')).toBeInTheDocument();
    expect(screen.getByText('Equipment malfunction during loading')).toBeInTheDocument();
    expect(screen.getByText('Route deviation due to road closure')).toBeInTheDocument();
    expect(screen.getByText('DT-100 • John Doe')).toBeInTheDocument();
    expect(screen.getByText('DT-101 • Jane Smith')).toBeInTheDocument();
  });

  test('handles malformed date range gracefully', () => {
    const invalidDateRange = {
      start: 'invalid-date',
      end: 'another-invalid-date'
    };

    const mockData = {
      summary: { total: 0, pending: 0, resolved: 0, rate: '0.0' },
      recent: [],
      trends: []
    };

    render(<ExceptionsReport data={mockData} loading={false} dateRange={invalidDateRange} />);
    
    expect(screen.getByText('Period: Invalid date range')).toBeInTheDocument();
  });

  test('calculates exception types correctly', () => {
    const mockData = {
      summary: { total: 4, pending: 1, resolved: 3, rate: '8.0' },
      recent: [
        { id: 1, reason: 'Equipment malfunction', status: 'resolved' },
        { id: 2, reason: 'Route deviation', status: 'pending' },
        { id: 3, reason: 'Weather delay', status: 'resolved' },
        { id: 4, reason: 'Equipment failure', status: 'resolved' }
      ],
      trends: []
    };

    render(<ExceptionsReport data={mockData} loading={false} dateRange={mockDateRange} />);
    
    // Should categorize equipment issues together
    expect(screen.getByText('Equipment Issue')).toBeInTheDocument();
    expect(screen.getByText('Route Deviation')).toBeInTheDocument();
    expect(screen.getByText('Weather Delay')).toBeInTheDocument();
  });

  test('handles missing or null exception properties gracefully', () => {
    const mockDataWithMissingProps = {
      summary: { total: 1, pending: 1, resolved: 0, rate: '5.0' },
      recent: [
        {
          id: 1,
          tripNumber: null,
          reason: '',
          truckNumber: undefined,
          driverName: null,
          status: null,
          createdAt: null
        }
      ],
      trends: []
    };

    render(<ExceptionsReport data={mockDataWithMissingProps} loading={false} dateRange={mockDateRange} />);
    
    expect(screen.getByText('Trip #N/A')).toBeInTheDocument();
    expect(screen.getByText('No reason provided')).toBeInTheDocument();
    expect(screen.getByText('N/A • N/A')).toBeInTheDocument();
    expect(screen.getByText('Unknown')).toBeInTheDocument(); // Status and date
  });

  test('calculates daily trends correctly', () => {
    const mockData = {
      summary: { total: 3, pending: 1, resolved: 2, rate: '6.0' },
      recent: [],
      trends: [
        { date: '2025-05-15', count: 2 },
        { date: '2025-05-14', count: 1 },
        { date: '2025-05-13', count: 0 }
      ]
    };

    render(<ExceptionsReport data={mockData} loading={false} dateRange={mockDateRange} />);
    
    // Should show average calculation
    expect(screen.getByText(/Average: \d+\.\d+ exceptions\/day/)).toBeInTheDocument();
  });

  test('shows resolution performance metrics', () => {
    const mockData = {
      summary: {
        total: 10,
        pending: 3,
        resolved: 7,
        rate: '15.2'
      },
      recent: [],
      trends: []
    };

    render(<ExceptionsReport data={mockData} loading={false} dateRange={mockDateRange} />);
    
    expect(screen.getByText('Exception Management Performance')).toBeInTheDocument();
    expect(screen.getByText('70%')).toBeInTheDocument(); // Resolution rate (7/10 * 100)
    expect(screen.getByText('3')).toBeInTheDocument(); // Pending cases
    expect(screen.getByText('15.2%')).toBeInTheDocument(); // Exception rate
  });

  test('handles API error gracefully', () => {
    // Test with undefined data to simulate API error
    render(<ExceptionsReport data={undefined} loading={false} dateRange={mockDateRange} />);
    
    expect(screen.getByText('No Exception Data Available')).toBeInTheDocument();
  });

  test('displays live data timestamp', () => {
    const mockData = {
      summary: { total: 1, pending: 0, resolved: 1, rate: '2.0' },
      recent: [],
      trends: []
    };

    render(<ExceptionsReport data={mockData} loading={false} dateRange={mockDateRange} />);
    
    expect(screen.getByText(/All exception data is fetched live from the database • Updated:/)).toBeInTheDocument();
  });

  test('validates status badge rendering', () => {
    const mockData = {
      summary: { total: 3, pending: 1, resolved: 2, rate: '5.0' },
      recent: [
        { id: 1, tripNumber: 'T001', reason: 'Test', status: 'pending', createdAt: '2025-05-15T10:30:00Z' },
        { id: 2, tripNumber: 'T002', reason: 'Test', status: 'resolved', createdAt: '2025-05-14T14:20:00Z' },
        { id: 3, tripNumber: 'T003', reason: 'Test', status: 'rejected', createdAt: '2025-05-13T09:15:00Z' }
      ],
      trends: []
    };

    render(<ExceptionsReport data={mockData} loading={false} dateRange={mockDateRange} />);
    
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('Resolved')).toBeInTheDocument();
    expect(screen.getByText('Rejected')).toBeInTheDocument();
  });

  test('validates severity icon assignment', () => {
    const mockData = {
      summary: { total: 4, pending: 0, resolved: 4, rate: '8.0' },
      recent: [
        { id: 1, reason: 'Weather delay caused by storm', status: 'resolved' },
        { id: 2, reason: 'Equipment malfunction in hydraulics', status: 'resolved' },
        { id: 3, reason: 'Route deviation due to construction', status: 'resolved' },
        { id: 4, reason: 'Unknown issue', status: 'resolved' }
      ],
      trends: []
    };

    const { container } = render(<ExceptionsReport data={mockData} loading={false} dateRange={mockDateRange} />);
    
    // Check that different severity icons are used (emojis should appear in the DOM)
    expect(container.textContent).toContain('🟡'); // Weather delay
    expect(container.textContent).toContain('🔴'); // Equipment malfunction
    expect(container.textContent).toContain('🟠'); // Route deviation
    expect(container.textContent).toContain('🔵'); // Unknown/default
  });
});