/**
 * Test Assignment Date Logic
 * 
 * This script tests the updated assignment validation logic to ensure that:
 * 1. assigned_date is no longer blocking exception flows
 * 2. Duplicate checking focuses on truck-location combination regardless of date
 * 3. Exception-triggered assignments can be created without date conflicts
 * 4. True duplicates are still prevented (same truck, same locations, same active status)
 */

const { getClient } = require('../config/database');
const { assignmentValidator } = require('../utils/AssignmentValidator');

async function testAssignmentDateLogic() {
  console.log('🧪 Testing Assignment Date Logic for Exception Flows...\n');
  
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // Setup test data
    console.log('📋 Setting up test data...');
    const testData = await setupTestData(client);
    console.log('✅ Test data created successfully\n');
    
    // Test 1: Same truck-location combination with different dates
    console.log('📅 Test 1: Same truck-location combination with different dates');
    await testDifferentDatesScenario(client, testData);
    console.log('✅ Different dates scenario passed\n');
    
    // Test 2: Exception flow assignment creation
    console.log('⚠️  Test 2: Exception flow assignment creation');
    await client.query('SAVEPOINT test_savepoint');
    await testExceptionFlowAssignment(client, testData);
    console.log('✅ Exception flow assignment creation passed\n');
    
    // Test 3: True duplicate prevention (same truck, same locations, same status)
    console.log('🚫 Test 3: True duplicate prevention');
    await client.query('SAVEPOINT test3_savepoint');
    await testTrueDuplicatePrevention(client, testData);
    console.log('✅ True duplicate prevention passed\n');
    
    // Test 4: Multiple assignments for same truck at different locations
    console.log('🔄 Test 4: Multiple assignments for same truck at different locations');
    await testMultipleLocationAssignments(client, testData);
    console.log('✅ Multiple location assignments passed\n');
    
    await client.query('ROLLBACK'); // Clean up test data
    
    console.log('🎉 All assignment date logic tests passed successfully!');
    console.log('\n📊 Summary:');
    console.log('  ✅ assigned_date no longer blocks exception flows');
    console.log('  ✅ Duplicate checking focuses on truck-location combination');
    console.log('  ✅ Exception assignments can be created with different dates');
    console.log('  ✅ True duplicates are still prevented');
    console.log('  ✅ Multiple location assignments work correctly');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  } finally {
    client.release();
  }
}

async function setupTestData(client) {
  // Create test truck
  const qrCodeData = {
    type: 'truck',
    id: 'DT-DATE-TEST-100',
    timestamp: new Date().toISOString()
  };

  const truckResult = await client.query(`
    INSERT INTO dump_trucks (truck_number, license_plate, make, model, status, qr_code_data)
    VALUES ('DT-DATE-TEST-100', 'DATE-001', 'Test Make', 'Test Model', 'active', $1)
    RETURNING *
  `, [qrCodeData]);
  const truck = truckResult.rows[0];
  
  // Create test driver
  const driverResult = await client.query(`
    INSERT INTO drivers (employee_id, full_name, license_number, license_expiry, hire_date, status)
    VALUES ('EMP-DATE-001', 'Date Test Driver', 'LIC-DATE-001', '2026-12-31', '2024-01-01', 'active')
    RETURNING *
  `);
  const driver = driverResult.rows[0];
  
  // Create test locations
  const locationA = await createLocation(client, 'DATE-A', 'Date Test Point A', 'loading');
  const locationB = await createLocation(client, 'DATE-B', 'Date Test Point B', 'unloading');
  const locationC = await createLocation(client, 'DATE-C', 'Date Test Point C', 'loading');
  
  return { truck, driver, locationA, locationB, locationC };
}

async function createLocation(client, code, name, type) {
  const qrCodeData = {
    type: 'location',
    id: code,
    name: name,
    location_type: type,
    timestamp: new Date().toISOString()
  };

  const result = await client.query(`
    INSERT INTO locations (location_code, name, type, status, coordinates, qr_code_data)
    VALUES ($1, $2, $3, 'active', '0.0,0.0', $4)
    RETURNING *
  `, [code, name, type, qrCodeData]);
  return result.rows[0];
}

async function testDifferentDatesScenario(client, testData) {
  const today = new Date().toISOString().split('T')[0];

  // Create assignment for today
  const todayAssignment = await createAssignment(
    client,
    testData.truck.id,
    testData.driver.id,
    testData.locationA.id,
    testData.locationB.id,
    { assigned_date: today }
  );

  console.log(`  📅 Created assignment for today: ${todayAssignment.assignment_code}`);

  // Try to create assignment for tomorrow with same truck-location combination
  // This should be detected as duplicate because we focus on truck-location, not date
  const duplicateCheck = await assignmentValidator.checkDuplicateAssignment({
    truckId: testData.truck.id,
    loadingLocationId: testData.locationA.id,
    unloadingLocationId: testData.locationB.id,
    client
  });

  if (!duplicateCheck.isDuplicate) {
    throw new Error('Should detect duplicate assignment regardless of date');
  }

  console.log(`  ✅ Duplicate detected correctly: ${duplicateCheck.existingAssignment.assignment_code}`);
  console.log(`  ✅ Date-independent duplicate checking working`);

  // Complete the assignment so we can create new ones in subsequent tests
  await client.query(`
    UPDATE assignments
    SET status = 'completed'
    WHERE id = $1
  `, [todayAssignment.id]);

  console.log(`  🏁 Completed assignment to allow subsequent tests`);
}

async function testExceptionFlowAssignment(client, testData) {
  // Create original assignment A→B
  const originalAssignment = await createAssignment(
    client,
    testData.truck.id,
    testData.driver.id,
    testData.locationA.id,
    testData.locationB.id
  );

  console.log(`  📋 Original assignment: ${originalAssignment.assignment_code} (A→B)`);

  // Simulate exception flow: truck goes to location C instead of A
  // This should be allowed to create a new assignment C→B with pending_approval status
  const exceptionAssignment = await createAssignment(
    client,
    testData.truck.id,
    testData.driver.id,
    testData.locationC.id, // Different loading location
    testData.locationB.id, // Same unloading location
    {
      status: 'pending_approval',
      assigned_date: new Date().toISOString().split('T')[0] // Could be different date
    }
  );

  console.log(`  ⚠️  Exception assignment: ${exceptionAssignment.assignment_code} (C→B, pending)`);

  // Verify that this is not considered a duplicate because locations are different
  const duplicateCheck = await assignmentValidator.checkDuplicateAssignment({
    truckId: testData.truck.id,
    loadingLocationId: testData.locationC.id,
    unloadingLocationId: testData.locationB.id,
    client
  });

  if (!duplicateCheck.isDuplicate) {
    console.log(`  ✅ Exception assignment created successfully (different locations)`);
  } else {
    console.log(`  ✅ Exception assignment detected as duplicate (same locations): ${duplicateCheck.existingAssignment.assignment_code}`);
  }

  // Test that trying to create exact duplicate (same truck, same locations) is prevented
  try {
    await createAssignment(
      client,
      testData.truck.id,
      testData.driver.id,
      testData.locationA.id, // Same loading location as original
      testData.locationB.id, // Same unloading location as original
      { status: 'assigned' }
    );
    throw new Error('Should not allow duplicate assignment creation');
  } catch (error) {
    if (error.message.includes('idx_assignments_exact_duplicate')) {
      console.log(`  ✅ Database constraint correctly prevented duplicate assignment`);
      // Rollback to a savepoint to continue with other tests
      await client.query('ROLLBACK TO SAVEPOINT test_savepoint');
    } else {
      throw error;
    }
  }
}

async function testTrueDuplicatePrevention(client, testData) {
  // Create first assignment
  const firstAssignment = await createAssignment(
    client,
    testData.truck.id,
    testData.driver.id,
    testData.locationA.id,
    testData.locationB.id
  );
  
  console.log(`  📋 First assignment: ${firstAssignment.assignment_code}`);
  
  // Try to create exact duplicate (same truck, same locations, active status)
  const duplicateCheck = await assignmentValidator.checkDuplicateAssignment({
    truckId: testData.truck.id,
    loadingLocationId: testData.locationA.id,
    unloadingLocationId: testData.locationB.id,
    client
  });
  
  if (!duplicateCheck.isDuplicate) {
    throw new Error('Should prevent true duplicates');
  }
  
  console.log(`  🚫 True duplicate correctly prevented: ${duplicateCheck.existingAssignment.assignment_code}`);
  console.log(`  ✅ Duplicate prevention working for identical assignments`);
}

async function testMultipleLocationAssignments(client, testData) {
  // First, complete any existing assignments to avoid conflicts
  await client.query(`
    UPDATE assignments
    SET status = 'completed'
    WHERE truck_id = $1 AND status IN ('assigned', 'in_progress')
  `, [testData.truck.id]);

  // Create assignment A→B
  const assignmentAB = await createAssignment(
    client,
    testData.truck.id,
    testData.driver.id,
    testData.locationA.id,
    testData.locationB.id
  );

  // Create assignment C→B (different loading location)
  const assignmentCB = await createAssignment(
    client,
    testData.truck.id,
    testData.driver.id,
    testData.locationC.id,
    testData.locationB.id
  );
  
  console.log(`  📋 Assignment 1: ${assignmentAB.assignment_code} (A→B)`);
  console.log(`  📋 Assignment 2: ${assignmentCB.assignment_code} (C→B)`);
  
  // Verify truck has valid assignments at both locations
  const validAtA = await assignmentValidator.hasValidAssignmentForLocation({
    truckNumber: testData.truck.truck_number,
    locationId: testData.locationA.id,
    client
  });
  
  const validAtC = await assignmentValidator.hasValidAssignmentForLocation({
    truckNumber: testData.truck.truck_number,
    locationId: testData.locationC.id,
    client
  });
  
  if (!validAtA.hasValidAssignment || !validAtC.hasValidAssignment) {
    throw new Error('Should find valid assignments at both locations');
  }
  
  console.log(`  ✅ Valid assignment found at location A: ${validAtA.assignments[0].assignment_code}`);
  console.log(`  ✅ Valid assignment found at location C: ${validAtC.assignments[0].assignment_code}`);
  console.log(`  ✅ Multiple location assignments working correctly`);
}

// Helper function
async function createAssignment(client, truckId, driverId, loadingLocationId, unloadingLocationId, options = {}) {
  const assignmentCode = `DATE-TEST-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
  const result = await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
      assigned_date, status, priority, expected_loads_per_day
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING *
  `, [
    assignmentCode,
    truckId,
    driverId,
    loadingLocationId,
    unloadingLocationId,
    options.assigned_date || new Date().toISOString().split('T')[0],
    options.status || 'assigned',
    options.priority || 'normal',
    options.expected_loads_per_day || 1
  ]);
  return result.rows[0];
}

// Run the test
if (require.main === module) {
  testAssignmentDateLogic()
    .then(() => {
      console.log('\n🎯 Assignment date logic is working correctly for exception flows!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testAssignmentDateLogic };
