import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';

const Sidebar = ({ isOpen, setIsOpen }) => {
  const location = useLocation();

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: '📊',
      description: 'Overview and analytics'
    },
    {
      name: 'Users',
      href: '/users',
      icon: '👥',
      description: 'Manage system users'
    },
    {
      name: 'Trucks',
      href: '/trucks',
      icon: '🚛',
      description: 'Manage dump trucks'
    },
    {
      name: 'Drivers',
      href: '/drivers',
      icon: '👤',
      description: 'Manage drivers'
    },
    {
      name: 'Locations',
      href: '/locations',
      icon: '📍',
      description: 'Loading & unloading points'
    },
    {
      name: 'Assignments',
      href: '/assignments',
      icon: '📋',
      description: 'Route assignments'
    },
    {
      name: 'Trip Monitoring',
      href: '/trips',
      icon: '🛣️',
      description: 'Track active trips'
    },
    {
      name: 'QR Scanner',
      href: '/scanner',
      icon: '📱',
      description: 'Scan QR codes'
    },
    {
      name: 'Exceptions',
      href: '/exceptions',
      icon: '⚠️',
      description: 'Manage exceptions'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: '📈',
      description: 'Reports and insights'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: '⚙️',
      description: 'System configuration'
    },
    {
      name: 'Truck Trip Summary',
      href: '/trucks/trip-summary',
      icon: '📝',
      description: 'Summary of trips per truck'
    }
  ];

  const closeSidebar = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
        <div className="flex-1 flex flex-col min-h-0 bg-white border-r border-secondary-200">
          {/* Logo */}
          <div className="flex items-center h-16 flex-shrink-0 px-4 border-b border-secondary-200">
            <div className="flex items-center">
              <span className="text-2xl">🚛</span>
              <div className="ml-3">
                <h1 className="text-lg font-semibold text-secondary-800">
                  Hauling QR
                </h1>
                <p className="text-xs text-secondary-500">
                  Trip Management
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex-1 flex flex-col overflow-y-auto">
            <nav className="flex-1 px-2 py-4 space-y-2">
              {navigation.map((item) => {
                const isActive = location.pathname === item.href;
                return (
                  <NavLink
                    key={item.name}
                    to={item.href}
                    className={`group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      isActive
                        ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                        : 'text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900'
                    }`}
                  >
                    <span className="text-lg mr-3">{item.icon}</span>
                    <div className="flex-1">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-xs text-secondary-500 mt-0.5">
                        {item.description}
                      </div>
                    </div>
                  </NavLink>
                );
              })}
            </nav>

            {/* User Management Status */}
            <div className="p-4 border-t border-secondary-200">
              <div className="bg-success-50 border border-success-200 rounded-lg p-3">
                <div className="flex items-center">
                  <span className="text-success-500 text-lg">✅</span>
                  <div className="ml-2">
                    <p className="text-xs font-medium text-success-800">
                      User Management
                    </p>
                    <p className="text-xs text-success-600">
                      Full CRUD + Advanced Search
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`lg:hidden fixed inset-y-0 left-0 z-50 w-64 bg-white transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex-1 flex flex-col min-h-0 border-r border-secondary-200">
          {/* Mobile Logo */}
          <div className="flex items-center justify-between h-16 flex-shrink-0 px-4 border-b border-secondary-200">
            <div className="flex items-center">
              <span className="text-2xl">🚛</span>
              <div className="ml-3">
                <h1 className="text-lg font-semibold text-secondary-800">
                  Hauling QR
                </h1>
                <p className="text-xs text-secondary-500">
                  Trip Management
                </p>
              </div>
            </div>
            <button
              onClick={closeSidebar}
              className="p-2 rounded-md text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100"
            >
              <span className="sr-only">Close sidebar</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Mobile Navigation */}
          <div className="flex-1 flex flex-col overflow-y-auto">
            <nav className="flex-1 px-2 py-4 space-y-2">
              {navigation.map((item) => {
                const isActive = location.pathname === item.href;
                return (
                  <NavLink
                    key={item.name}
                    to={item.href}
                    onClick={closeSidebar}
                    className={`group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      isActive
                        ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                        : 'text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900'
                    }`}
                  >
                    <span className="text-lg mr-3">{item.icon}</span>
                    <div className="flex-1">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-xs text-secondary-500 mt-0.5">
                        {item.description}
                      </div>
                    </div>
                  </NavLink>
                );
              })}
            </nav>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;