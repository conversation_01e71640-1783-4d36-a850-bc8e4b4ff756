import React from 'react';
import ExceptionHistoryCard from './ExceptionHistoryCard';

const TripDetailModal = ({ trip, onClose, onRefresh }) => {
  // Format datetime for display
  const formatDateTime = (dateString) => {
    if (!dateString) return 'Not started';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Format duration in minutes to human readable
  const formatDuration = (minutes) => {
    if (!minutes) return '-';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // Get status badge for trip status
  const getStatusBadge = (status) => {
    const statusStyles = {
      'assigned': 'bg-blue-100 text-blue-800',
      'loading_start': 'bg-yellow-100 text-yellow-800',
      'loading_end': 'bg-green-100 text-green-800',
      'unloading_start': 'bg-orange-100 text-orange-800',
      'unloading_end': 'bg-purple-100 text-purple-800',
      'trip_completed': 'bg-success-100 text-success-800',
      'exception_pending': 'bg-danger-100 text-danger-800',
      'cancelled': 'bg-secondary-100 text-secondary-800'
    };

    const statusLabels = {
      'assigned': 'Assigned',
      'loading_start': 'Loading Started',
      'loading_end': 'Loading Complete',
      'unloading_start': 'Unloading Started',
      'unloading_end': 'Unloading Complete',
      'trip_completed': 'Completed',
      'exception_pending': 'Exception Pending',
      'cancelled': 'Cancelled'
    };

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusStyles[status] || 'bg-secondary-100 text-secondary-800'}`}>
        {statusLabels[status] || status}
      </span>
    );
  };

  // Calculate trip progress percentage
  const getTripProgress = () => {
    const statusOrder = [
      'assigned',
      'loading_start',
      'loading_end',
      'unloading_start',
      'unloading_end',
      'trip_completed'
    ];
    
    const currentIndex = statusOrder.indexOf(trip.status);
    if (currentIndex === -1) return 0;
    
    return Math.round((currentIndex / (statusOrder.length - 1)) * 100);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-secondary-200">
          <div className="flex items-center space-x-3">
            <span className="text-3xl">🚚</span>
            <div>
              <h2 className="text-xl font-semibold text-secondary-900">
                Trip #{trip.trip_number} Details
              </h2>
              <p className="text-secondary-600">
                {trip.truck_number} • {trip.driver_name}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-secondary-400 hover:text-secondary-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status and Progress */}
          <div className="bg-secondary-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-secondary-900">Current Status</h3>
              {getStatusBadge(trip.status)}
            </div>
            
            {/* Progress Bar */}
            <div className="flex items-center mb-2">
              <span className="text-sm text-secondary-600 mr-3">Progress:</span>
              <div className="flex-1 bg-secondary-200 rounded-full h-3">
                <div 
                  className="bg-primary-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${getTripProgress()}%` }}
                ></div>
              </div>
              <span className="text-sm text-secondary-600 ml-3 font-medium">
                {getTripProgress()}%
              </span>
            </div>            {/* Exception Alert - Only show for pending exceptions */}
            {trip.is_exception && trip.status === 'exception_pending' && (
              <div className="mt-3 p-3 bg-danger-50 border border-danger-200 rounded-lg">
                <div className="flex items-center">
                  <span className="text-danger-600 text-lg mr-2">⚠️</span>
                  <div>
                    <h4 className="text-sm font-medium text-danger-800">Exception Pending Approval</h4>
                    <p className="text-sm text-danger-700">
                      {trip.exception_reason || 'This trip has an exception that requires attention.'}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Trip Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Assignment Details */}
            <div className="bg-white border border-secondary-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-secondary-900 mb-4">Assignment Details</h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-secondary-700">Truck:</label>
                  <p className="text-sm text-secondary-900">
                    {trip.truck_number} ({trip.license_plate})
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-secondary-700">Driver:</label>
                  <p className="text-sm text-secondary-900">{trip.driver_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-secondary-700">Route:</label>
                  <div className="text-sm text-secondary-900">
                    <div className="flex items-center mb-1">
                      <span className="text-green-600 mr-2">📍</span>
                      {trip.loading_location || 'Loading Location'}
                    </div>
                    <div className="flex items-center">
                      <span className="text-red-600 mr-2">📍</span>
                      {trip.unloading_location || 'Unloading Location'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Timeline */}
            <div className="bg-white border border-secondary-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-secondary-900 mb-4">Timeline</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-secondary-700">Loading Start:</span>
                  <span className="text-sm text-secondary-900">
                    {formatDateTime(trip.loading_start_time)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-secondary-700">Loading End:</span>
                  <span className="text-sm text-secondary-900">
                    {formatDateTime(trip.loading_end_time)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-secondary-700">Unloading Start:</span>
                  <span className="text-sm text-secondary-900">
                    {formatDateTime(trip.unloading_start_time)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-secondary-700">Unloading End:</span>
                  <span className="text-sm text-secondary-900">
                    {formatDateTime(trip.unloading_end_time)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-secondary-700">Trip Completed:</span>
                  <span className="text-sm text-secondary-900">
                    {formatDateTime(trip.trip_completed_time)}
                  </span>
                </div>
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="bg-white border border-secondary-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-secondary-900 mb-4">Performance</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-secondary-700">Total Duration:</span>
                  <span className="text-sm text-secondary-900 font-medium">
                    {formatDuration(trip.total_duration_minutes)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-secondary-700">Loading Time:</span>
                  <span className="text-sm text-secondary-900">
                    {formatDuration(trip.loading_duration_minutes)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-secondary-700">Travel Time:</span>
                  <span className="text-sm text-secondary-900">
                    {formatDuration(trip.travel_duration_minutes)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-secondary-700">Unloading Time:</span>
                  <span className="text-sm text-secondary-900">
                    {formatDuration(trip.unloading_duration_minutes)}
                  </span>
                </div>
              </div>
            </div>            {/* Additional Information */}
            <div className="bg-white border border-secondary-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-secondary-900 mb-4">Additional Info</h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-secondary-700">Trip Started:</label>
                  <p className="text-sm text-secondary-900">
                    {formatDateTime(trip.created_at)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-secondary-700">Last Updated:</label>
                  <p className="text-sm text-secondary-900">
                    {formatDateTime(trip.updated_at)}
                  </p>
                </div>
                {trip.notes && typeof trip.notes === 'string' && (
                  <div>
                    <label className="text-sm font-medium text-secondary-700">Notes:</label>
                    <p className="text-sm text-secondary-900">
                      {trip.notes.length > 100 && !trip.notes.startsWith('{') 
                        ? `${trip.notes.substring(0, 100)}...` 
                        : trip.notes}
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Exception History Card */}
            <ExceptionHistoryCard trip={trip} />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-secondary-200">
            <button
              onClick={onRefresh}
              className="btn btn-secondary"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh Data
            </button>
            <button
              onClick={onClose}
              className="btn btn-primary"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TripDetailModal;