// Test script for route deviation functionality
// Tests the specific flow: DT-100 assigned Point A → Point B, but scans at Point C

const { getClient } = require('../config/database');

async function testRouteDeviationFlow() {
  console.log('🧪 Testing Route Deviation Flow...\n');
  
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // 1. Setup test data - ensure we have required resources
    console.log('1. Setting up test data...');
    
    // Ensure we have active truck DT-100
    const truckResult = await client.query(`
      SELECT id FROM dump_trucks 
      WHERE truck_number = 'DT-100' AND status = 'active'
    `);
    
    if (truckResult.rows.length === 0) {
      console.log('   Creating test truck DT-100...');
      await client.query(`
        INSERT INTO dump_trucks (truck_number, license_plate, make, model, status, qr_code_data, created_at, updated_at)
        VALUES ('DT-100', 'TEST-100', 'Test', 'Model', 'active', '{"type":"truck","id":"DT-100"}', NOW(), NOW())
        ON CONFLICT (truck_number) DO NOTHING
      `);
    }
    
    // Ensure we have an active driver
    const driverResult = await client.query(`
      SELECT id FROM drivers WHERE status = 'active' LIMIT 1
    `);
    
    if (driverResult.rows.length === 0) {
      console.log('   Creating test driver...');
      await client.query(`
        INSERT INTO drivers (employee_id, full_name, license_number, status, created_at, updated_at)
        VALUES ('TEST-001', 'Test Driver', 'LIC-001', 'active', NOW(), NOW())
        ON CONFLICT (employee_id) DO NOTHING
      `);
    }
    
    // Ensure we have test locations
    const locations = ['Point-A', 'Point-B', 'Point-C'];
    const locationTypes = ['loading', 'unloading', 'loading'];
    
    for (let i = 0; i < locations.length; i++) {
      await client.query(`
        INSERT INTO locations (location_code, name, type, is_active, coordinates, qr_code_data, created_at, updated_at)
        VALUES ($1, $1, $2, true, '{"lat": 40.7128, "lng": -74.0060}', $3, NOW(), NOW())
        ON CONFLICT (location_code) DO NOTHING
      `, [locations[i], locationTypes[i], `{"type":"location","id":"${locations[i]}"}`]);
    }
    
    // 2. Get resource IDs for testing
    const [truck, driver, pointA, pointB, pointC] = await Promise.all([
      client.query(`SELECT id, truck_number FROM dump_trucks WHERE truck_number = 'DT-100'`),
      client.query(`SELECT id, full_name FROM drivers WHERE status = 'active' LIMIT 1`),
      client.query(`SELECT id, name FROM locations WHERE location_code = 'Point-A'`),
      client.query(`SELECT id, name FROM locations WHERE location_code = 'Point-B'`),
      client.query(`SELECT id, name FROM locations WHERE location_code = 'Point-C'`)
    ]);
    
    console.log('   ✅ Test resources ready:');
    console.log(`   - Truck: ${truck.rows[0].truck_number} (ID: ${truck.rows[0].id})`);
    console.log(`   - Driver: ${driver.rows[0].full_name} (ID: ${driver.rows[0].id})`);
    console.log(`   - Point A: ${pointA.rows[0].name} (ID: ${pointA.rows[0].id})`);
    console.log(`   - Point B: ${pointB.rows[0].name} (ID: ${pointB.rows[0].id})`);
    console.log(`   - Point C: ${pointC.rows[0].name} (ID: ${pointC.rows[0].id})`);
    
    // 3. Create assignment: DT-100 Point A → Point B
    console.log('\n2. Creating assignment: DT-100 Point A → Point B...');
    
    const assignmentCode = `TEST-${Date.now()}`;
    const assignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        assigned_date, status, priority, expected_loads_per_day,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'assigned', 'normal', 1, NOW(), NOW())
      RETURNING *
    `, [
      assignmentCode,
      truck.rows[0].id,
      driver.rows[0].id,
      pointA.rows[0].id,
      pointB.rows[0].id
    ]);
    
    console.log(`   ✅ Assignment created: ${assignmentResult.rows[0].assignment_code}`);
    console.log(`   - Loading: Point A → Unloading: Point B`);
    console.log(`   - Status: ${assignmentResult.rows[0].status}`);
    
    // 4. Test route deviation detection
    console.log('\n3. Testing route deviation detection...');
    console.log('   Scenario: DT-100 assigned Point A → Point B, but scans at Point C');
    
    // Simulate the scanner logic that would detect this deviation
    const todayAssignmentCheck = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = 'DT-100'
        AND a.assigned_date = CURRENT_DATE
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `);
    
    if (todayAssignmentCheck.rows.length > 0) {
      const assignment = todayAssignmentCheck.rows[0];
      console.log(`   ✅ Found today's assignment: ${assignment.assignment_code}`);
      console.log(`   - Assigned route: ${assignment.loading_location} → ${assignment.unloading_location}`);
      console.log(`   - Driver: ${assignment.driver_name} (ID: ${assignment.driver_id})`);
      
      // Check if Point C is different from assigned Point A
      if (pointC.rows[0].id !== assignment.loading_location_id) {
        console.log(`   ✅ Route deviation detected: Point C ≠ Point A`);
        console.log(`   - Expected: ${assignment.loading_location} (ID: ${assignment.loading_location_id})`);
        console.log(`   - Actual: Point C (ID: ${pointC.rows[0].id})`);
        
        // Verify all required fields are present for auto-assignment
        const requiredFields = {
          truck_id: assignment.truck_id,
          driver_id: assignment.driver_id,
          loading_location_id: assignment.loading_location_id,
          unloading_location_id: assignment.unloading_location_id,
          priority: assignment.priority,
          expected_loads_per_day: assignment.expected_loads_per_day
        };
        
        console.log('\n   🔍 Validating assignment data for route deviation:');
        let allValid = true;
        
        Object.entries(requiredFields).forEach(([field, value]) => {
          const isValid = value !== null && value !== undefined;
          console.log(`   - ${field}: ${value} ${isValid ? '✅' : '❌ NULL'}`);
          if (!isValid) allValid = false;
        });
        
        if (allValid) {
          console.log('\n   ✅ All required fields present - route deviation can be processed');
          console.log('   📋 Expected behavior:');
          console.log('   1. Create route deviation exception');
          console.log('   2. Auto-create alternative assignment: Point C → Point B');
          console.log('   3. Create approval request');
          console.log('   4. Send WebSocket notification');
          console.log('   5. Return exception pending status');
        } else {
          console.log('\n   ❌ Missing required fields - this would cause NULL constraint errors');
        }
      } else {
        console.log('   ❌ No route deviation - truck is at correct location');
      }
    } else {
      console.log('   ❌ No assignment found for today - different test scenario');
    }
    
    await client.query('ROLLBACK');
    console.log('\n✅ Route deviation test completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('\n❌ Route deviation test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    client.release();
  }
}

async function checkDatabaseConstraints() {
  console.log('\n🔍 Checking database constraints...');
  
  const client = await getClient();
  
  try {
    // Check assignments table schema
    const schemaResult = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'assignments' 
      ORDER BY ordinal_position
    `);
    
    console.log('\nAssignments table schema:');
    schemaResult.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : '(nullable)'}`);
    });
    
    // Check constraints
    const constraintResult = await client.query(`
      SELECT constraint_name, constraint_type 
      FROM information_schema.table_constraints 
      WHERE table_name = 'assignments'
    `);
    
    console.log('\nAssignments table constraints:');
    constraintResult.rows.forEach(constraint => {
      console.log(`  ${constraint.constraint_name}: ${constraint.constraint_type}`);
    });
    
  } catch (error) {
    console.error('Schema check failed:', error.message);
  } finally {
    client.release();
  }
}

if (require.main === module) {
  testRouteDeviationFlow()
    .then(() => checkDatabaseConstraints())
    .then(() => {
      console.log('\n🎯 Route deviation functionality test complete!');
      console.log('\nIf the test shows all required fields present, the implementation should work.');
      console.log('If there are NULL constraint errors, check the specific fields mentioned above.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testRouteDeviationFlow, checkDatabaseConstraints };
