const { query, getClient } = require('../config/database');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function fixInvalidApprovals() {
  console.log('🔧 Fixing invalid approval statuses...');

  const client = await getClient();
  
  try {
    await client.query('BEGIN');

    // First, let's check what invalid statuses exist by casting to text
    console.log('🔍 Checking for invalid status values...');
    
    // Find rows with potentially invalid status
    const invalidRows = await client.query(`
      SELECT id, trip_log_id, exception_type, exception_description, created_at,
             status::text as status_text
      FROM approvals 
      WHERE status::text NOT IN ('pending', 'approved', 'rejected')
    `);

    console.log(`Found ${invalidRows.rows.length} rows with invalid status:`);
    invalidRows.rows.forEach(row => {
      console.log(`  ID: ${row.id}, Status: ${row.status_text}, Type: ${row.exception_type}, Created: ${row.created_at}`);
    });

    if (invalidRows.rows.length > 0) {
      console.log('🔧 Fixing invalid status values...');
      
      // Update invalid statuses to 'pending'
      const fixResult = await client.query(`
        UPDATE approvals 
        SET status = 'pending'::approval_status,
            updated_at = CURRENT_TIMESTAMP
        WHERE status::text NOT IN ('pending', 'approved', 'rejected')
        RETURNING id, status
      `);

      console.log(`✅ Fixed ${fixResult.rows.length} rows:`);
      fixResult.rows.forEach(row => {
        console.log(`  ID: ${row.id} -> Status: ${row.status}`);
      });
    }

    // Also check for trip_logs with invalid status that might be causing issues
    console.log('🔍 Checking trip_logs for invalid "exception_pending" status...');
    
    const invalidTripLogs = await client.query(`
      SELECT id, status, trip_number, created_at
      FROM trip_logs 
      WHERE status = 'exception_pending'
    `);

    console.log(`Found ${invalidTripLogs.rows.length} trip logs with invalid status:`);
    invalidTripLogs.rows.forEach(row => {
      console.log(`  ID: ${row.id}, Status: ${row.status}, Trip: ${row.trip_number}, Created: ${row.created_at}`);
    });

    if (invalidTripLogs.rows.length > 0) {
      console.log('🔧 Fixing trip_logs with invalid status...');
      
      // Update trip logs with invalid status to 'exception' (which should be valid)
      const fixTripResult = await client.query(`
        UPDATE trip_logs 
        SET status = 'exception',
            updated_at = CURRENT_TIMESTAMP
        WHERE status = 'exception_pending'
        RETURNING id, status, trip_number
      `);

      console.log(`✅ Fixed ${fixTripResult.rows.length} trip log rows:`);
      fixTripResult.rows.forEach(row => {
        console.log(`  ID: ${row.id}, Trip: ${row.trip_number} -> Status: ${row.status}`);
      });
    }

    await client.query('COMMIT');
    console.log('✅ All invalid data fixed successfully!');

    // Verify the fix
    console.log('🔍 Verifying fix...');
    const verification = await query(`
      SELECT DISTINCT status FROM approvals ORDER BY status
    `);
    console.log('All approval statuses now:', verification.rows.map(r => r.status));

    const tripVerification = await query(`
      SELECT DISTINCT status FROM trip_logs WHERE is_exception = true ORDER BY status
    `);
    console.log('Exception trip log statuses:', tripVerification.rows.map(r => r.status));

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Error fixing invalid data:', error);
    throw error;
  } finally {
    client.release();
  }
}

fixInvalidApprovals().then(() => {
  console.log('Done');
  process.exit(0);
}).catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
