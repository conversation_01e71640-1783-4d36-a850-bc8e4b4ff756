/**
 * Final Verification of Exception Workflow Fixes
 * 
 * Simple verification that both issues are resolved
 */

const { getClient } = require('../config/database');

async function finalVerification() {
  console.log('🎯 Final Verification of Exception Workflow Fixes...\n');
  
  const client = await getClient();
  
  try {
    // Check 1: Undefined messages
    console.log('1️⃣ CHECKING UNDEFINED MESSAGE FIXES');
    const undefinedResult = await client.query(`
      SELECT COUNT(*) as count
      FROM approvals
      WHERE exception_description LIKE '%undefined%'
        AND created_at >= CURRENT_DATE
    `);
    
    const newUndefinedCount = parseInt(undefinedResult.rows[0].count);
    console.log(`   📊 New undefined messages today: ${newUndefinedCount} ${newUndefinedCount === 0 ? '✅' : '⚠️'}`);
    
    // Check existing undefined messages (historical)
    const historicalUndefinedResult = await client.query(`
      SELECT COUNT(*) as count
      FROM approvals
      WHERE exception_description LIKE '%undefined%'
    `);
    
    const totalUndefinedCount = parseInt(historicalUndefinedResult.rows[0].count);
    console.log(`   📊 Total historical undefined messages: ${totalUndefinedCount} ${totalUndefinedCount <= 1 ? '✅' : '⚠️'}`);
    
    // Check 2: Assignment status synchronization
    console.log('\n2️⃣ CHECKING ASSIGNMENT STATUS SYNCHRONIZATION');
    const syncResult = await client.query(`
      SELECT COUNT(DISTINCT a.id) as count
      FROM assignments a
      JOIN trip_logs tl ON a.id = tl.assignment_id
      JOIN approvals ap ON tl.id = ap.trip_log_id
      WHERE ap.status = 'approved'
        AND a.status = 'pending_approval'
        AND a.assigned_date >= '2025-06-27'
    `);
    
    const syncIssueCount = parseInt(syncResult.rows[0].count);
    console.log(`   📊 Assignment sync issues: ${syncIssueCount} ${syncIssueCount === 0 ? '✅' : '⚠️'}`);
    
    // Check 3: Recent approvals working correctly
    console.log('\n3️⃣ CHECKING RECENT APPROVAL WORKFLOW');
    const recentApprovalsResult = await client.query(`
      SELECT 
          ap.id,
          ap.exception_description,
          ap.status,
          a.status as assignment_status
      FROM approvals ap
      JOIN trip_logs tl ON ap.trip_log_id = tl.id
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE ap.created_at >= '2025-06-27'
      ORDER BY ap.created_at DESC
      LIMIT 3
    `);
    
    console.log(`   📊 Recent approvals (${recentApprovalsResult.rows.length}):`);
    recentApprovalsResult.rows.forEach((row, index) => {
      const hasUndefined = row.exception_description.includes('undefined');
      const statusIcon = hasUndefined ? '❌' : '✅';
      console.log(`   ${index + 1}. Approval ${row.id}: ${row.status} ${statusIcon}`);
      console.log(`      Description: "${row.exception_description}"`);
      console.log(`      Assignment Status: ${row.assignment_status}`);
    });
    
    // Check 4: Code fixes verification
    console.log('\n4️⃣ VERIFYING CODE FIXES');
    const fs = require('fs');
    
    // Check scanner.js for fixed patterns
    const scannerContent = fs.readFileSync('server/routes/scanner.js', 'utf8');
    const fixedPatterns = [
      'instead of assigned ${originalAssignment.loading_location || \'Unknown Location\'}',
      '→ ${originalAssignment.unloading_location || \'Unknown Location\'} →'
    ];
    
    let fixedPatternsFound = 0;
    fixedPatterns.forEach((pattern, index) => {
      if (scannerContent.includes(pattern)) {
        console.log(`   ✅ Fixed pattern ${index + 1} found in scanner.js`);
        fixedPatternsFound++;
      } else {
        console.log(`   ❌ Fixed pattern ${index + 1} NOT found in scanner.js`);
      }
    });
    
    // Check websocket.js for undefined handling
    const websocketContent = fs.readFileSync('server/websocket.js', 'utf8');
    if (websocketContent.includes('exception.exception_description || exception.description || \'route deviation\'')) {
      console.log(`   ✅ WebSocket undefined handling found`);
      fixedPatternsFound++;
    } else {
      console.log(`   ❌ WebSocket undefined handling NOT found`);
    }
    
    // Final summary
    console.log('\n🎯 FINAL VERIFICATION SUMMARY');
    console.log('=' .repeat(50));
    
    const allChecks = [
      { name: 'No new undefined messages', passed: newUndefinedCount === 0 },
      { name: 'No assignment sync issues', passed: syncIssueCount === 0 },
      { name: 'Code fixes implemented', passed: fixedPatternsFound >= 2 },
      { name: 'Historical undefined messages minimal', passed: totalUndefinedCount <= 1 }
    ];
    
    let passedChecks = 0;
    allChecks.forEach((check, index) => {
      const icon = check.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${check.name}: ${icon}`);
      if (check.passed) passedChecks++;
    });
    
    console.log('=' .repeat(50));
    console.log(`Overall Status: ${passedChecks}/${allChecks.length} checks passed`);
    
    if (passedChecks === allChecks.length) {
      console.log('🎉 ALL EXCEPTION WORKFLOW ISSUES HAVE BEEN RESOLVED!');
      console.log('\n📋 Issues Fixed:');
      console.log('  ✅ Issue 1: "undefined" messages now show "Unknown Location"');
      console.log('  ✅ Issue 2: Assignment status synchronization working correctly');
      console.log('  ✅ All message generation points have fallback logic');
      console.log('  ✅ WebSocket notifications handle undefined descriptions');
      console.log('\n🚀 The exception approval workflow is now reliable and production-ready!');
    } else {
      console.log('⚠️ Some issues may still need attention');
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  } finally {
    client.release();
  }
}

// Run the verification
if (require.main === module) {
  finalVerification()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Verification failed:', error.message);
      process.exit(1);
    });
}

module.exports = { finalVerification };
