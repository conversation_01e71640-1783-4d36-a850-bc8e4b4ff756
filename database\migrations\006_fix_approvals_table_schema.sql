-- Migration: Ensure approvals table exists and has required columns

CREATE TABLE IF NOT EXISTS approvals (
    id SERIAL PRIMARY KEY,
    trip_log_id INTEGER NOT NULL REFERENCES trip_logs(id) ON DELETE CASCADE,
    exception_type VARCHAR(50) NOT NULL,
    exception_description TEXT NOT NULL,
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    reported_by INTEGER REFERENCES users(id),
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INTEGER REFERENCES users(id),
    reviewed_at TIMESTAMP,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add missing columns if they do not exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='approvals' AND column_name='severity') THEN
        ALTER TABLE approvals ADD COLUMN severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical'));
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='approvals' AND column_name='reported_by') THEN
        ALTER TABLE approvals ADD COLUMN reported_by INTEGER REFERENCES users(id);
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='approvals' AND column_name='exception_description') THEN
        ALTER TABLE approvals ADD COLUMN exception_description TEXT NOT NULL DEFAULT '';
    END IF;
END $$;
