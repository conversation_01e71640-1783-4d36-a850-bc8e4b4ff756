/**
 * Find the Real Root Cause
 * 
 * Why did the scanner query return undefined for the location name
 * when the exception was created?
 */

const { getClient } = require('../config/database');

async function findRealRootCause() {
  console.log('🔍 Finding Real Root Cause...\n');
  
  const client = await getClient();
  
  try {
    // Get the trip log that created the exception
    const tripResult = await client.query(`
      SELECT 
          tl.id,
          tl.assignment_id,
          tl.created_at as trip_created,
          tl.notes,
          ap.id as approval_id,
          ap.created_at as approval_created,
          ap.exception_description
      FROM trip_logs tl
      JOIN approvals ap ON tl.id = ap.trip_log_id
      WHERE ap.id = 6
    `);
    
    if (tripResult.rows.length === 0) {
      console.log('❌ Trip log for approval 6 not found');
      return;
    }
    
    const trip = tripResult.rows[0];
    console.log('📋 Trip Log Details:');
    console.log(`   Trip ID: ${trip.id}`);
    console.log(`   Assignment ID: ${trip.assignment_id}`);
    console.log(`   Trip Created: ${trip.trip_created}`);
    console.log(`   Approval Created: ${trip.approval_created}`);
    console.log(`   Exception Description: "${trip.exception_description}"`);
    
    // Check what the assignment looked like when the trip was created
    console.log('\n🔍 Checking assignment state when trip was created...');
    
    // Get the assignment details
    const assignmentResult = await client.query(`
      SELECT 
          a.*,
          ll.name as loading_location_name,
          ul.name as unloading_location_name,
          dt.truck_number
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.id = $1
    `, [trip.assignment_id]);
    
    if (assignmentResult.rows.length === 0) {
      console.log('❌ Assignment not found');
      return;
    }
    
    const assignment = assignmentResult.rows[0];
    console.log('📋 Assignment Details (current state):');
    console.log(`   Assignment Code: ${assignment.assignment_code}`);
    console.log(`   Status: ${assignment.status}`);
    console.log(`   Truck: ${assignment.truck_number}`);
    console.log(`   Loading Location ID: ${assignment.loading_location_id}`);
    console.log(`   Loading Location Name: "${assignment.loading_location_name}"`);
    console.log(`   Unloading Location ID: ${assignment.unloading_location_id}`);
    console.log(`   Unloading Location Name: "${assignment.unloading_location_name}"`);
    console.log(`   Created: ${assignment.created_at}`);
    console.log(`   Updated: ${assignment.updated_at}`);
    
    // The key question: Was the assignment status different when the exception was created?
    console.log('\n🔍 Timeline Analysis:');
    const tripTime = new Date(trip.trip_created);
    const assignmentCreated = new Date(assignment.created_at);
    const assignmentUpdated = new Date(assignment.updated_at);
    
    console.log(`   Assignment Created: ${assignmentCreated.toISOString()}`);
    console.log(`   Trip Created: ${tripTime.toISOString()}`);
    console.log(`   Assignment Updated: ${assignmentUpdated.toISOString()}`);
    
    // Check if the assignment was updated after the trip was created
    if (assignmentUpdated > tripTime) {
      console.log('🔍 Assignment was UPDATED after the trip was created');
      console.log('🎯 This could explain why the location name was undefined at trip creation time');
    } else {
      console.log('🔍 Assignment was not updated after trip creation');
    }
    
    // Test the exact scanner query that would have been used
    console.log('\n🧪 Testing Scanner Query (as it would have been executed):');
    const scannerTestResult = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [assignment.truck_number]);
    
    if (scannerTestResult.rows.length > 0) {
      const scannerResult = scannerTestResult.rows[0];
      console.log('📊 Scanner Query Result (current):');
      console.log(`   Assignment: ${scannerResult.assignment_code}`);
      console.log(`   Status: ${scannerResult.status}`);
      console.log(`   Loading Location: "${scannerResult.loading_location}"`);
      console.log(`   Unloading Location: "${scannerResult.unloading_location}"`);
      
      if (scannerResult.loading_location) {
        console.log('✅ Scanner query NOW returns proper location name');
        console.log('🔍 The issue was likely that the assignment status was different when the exception was created');
      } else {
        console.log('❌ Scanner query STILL returns null location name');
        console.log('🔍 There is a deeper issue with the location data or JOIN');
      }
    } else {
      console.log('❌ Scanner query returns no results');
      console.log('🔍 The assignment might not have been in "assigned" or "in_progress" status when the exception was created');
    }
    
    // Check if there were any other assignments for this truck at that time
    console.log('\n🔍 Checking for other assignments for this truck:');
    const otherAssignmentsResult = await client.query(`
      SELECT 
          a.assignment_code,
          a.status,
          a.created_at,
          a.updated_at,
          ll.name as loading_location
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      WHERE dt.truck_number = $1
      ORDER BY a.created_at DESC
    `, [assignment.truck_number]);
    
    console.log(`Found ${otherAssignmentsResult.rows.length} assignments for truck ${assignment.truck_number}:`);
    otherAssignmentsResult.rows.forEach((row, i) => {
      console.log(`   ${i+1}. ${row.assignment_code}: ${row.status} - "${row.loading_location || 'NULL'}"`);
      console.log(`      Created: ${row.created_at}`);
      console.log(`      Updated: ${row.updated_at}`);
    });
    
  } catch (error) {
    console.error('Analysis failed:', error.message);
  } finally {
    client.release();
  }
}

findRealRootCause();
