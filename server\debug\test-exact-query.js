/**
 * Test the Exact Query that was executed when the exception was created
 */

const { getClient } = require('../config/database');

async function testExactQuery() {
  console.log('🔍 Testing Exact Query...\n');
  
  const client = await getClient();
  
  try {
    // Test the exact query that was used in scanner.js
    console.log('🧪 Testing anyAssignmentToday query for DT-100:');
    
    const anyAssignmentToday = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, ['DT-100']);
    
    console.log(`📊 Query returned ${anyAssignmentToday.rows.length} row(s):`);
    
    if (anyAssignmentToday.rows.length > 0) {
      const existingAssignment = anyAssignmentToday.rows[0];
      
      console.log('📋 Assignment Details:');
      console.log(`   ID: ${existingAssignment.id}`);
      console.log(`   Code: ${existingAssignment.assignment_code}`);
      console.log(`   Status: ${existingAssignment.status}`);
      console.log(`   Truck: ${existingAssignment.truck_number}`);
      console.log(`   Loading Location ID: ${existingAssignment.loading_location_id}`);
      console.log(`   Loading Location Name: "${existingAssignment.loading_location}" ${!existingAssignment.loading_location ? '❌ NULL/UNDEFINED' : '✅'}`);
      console.log(`   Unloading Location ID: ${existingAssignment.unloading_location_id}`);
      console.log(`   Unloading Location Name: "${existingAssignment.unloading_location}" ${!existingAssignment.unloading_location ? '❌ NULL/UNDEFINED' : '✅'}`);
      
      // Test the exact logic used in scanner.js
      const expectedLocationName = existingAssignment.loading_location || 'Unknown Location';
      console.log(`\n🧪 Testing scanner.js logic:`);
      console.log(`   expectedLocationName = existingAssignment.loading_location || 'Unknown Location'`);
      console.log(`   Result: "${expectedLocationName}"`);
      
      // Test the exception message that would be generated
      const exceptionMessage = `Truck DT-100 loading at POINT C - LOADING instead of assigned ${expectedLocationName}`;
      console.log(`\n📝 Exception message that would be generated:`);
      console.log(`   "${exceptionMessage}"`);
      
      if (exceptionMessage.includes('undefined')) {
        console.log('❌ Message contains "undefined"');
      } else {
        console.log('✅ Message is properly formatted');
      }
      
      // The key question: Why did the original query return undefined?
      console.log('\n🔍 Analysis:');
      if (existingAssignment.loading_location) {
        console.log('✅ Current query returns proper location name');
        console.log('🤔 The original exception must have been created with different data');
        console.log('💡 Possible causes:');
        console.log('   1. Location data was missing when exception was created');
        console.log('   2. Different assignment was returned by the query');
        console.log('   3. Database transaction issue');
        console.log('   4. Location was soft-deleted or had different status');
      } else {
        console.log('❌ Current query STILL returns null location name');
        console.log('🚨 There is a current issue with the location data or JOIN');
      }
      
    } else {
      console.log('❌ Query returned no results');
      console.log('🔍 No assignments found with status "assigned" or "in_progress"');
    }
    
    // Check all assignments for DT-100 regardless of status
    console.log('\n🔍 Checking ALL assignments for DT-100 (regardless of status):');
    
    const allAssignments = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.created_at,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_location, ul.name as unloading_location
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
      ORDER BY a.created_at DESC
    `);
    
    console.log(`📊 Found ${allAssignments.rows.length} total assignments:`);
    
    allAssignments.rows.forEach((assignment, i) => {
      console.log(`   ${i+1}. ${assignment.assignment_code} (${assignment.status})`);
      console.log(`      Created: ${assignment.created_at}`);
      console.log(`      Loading: ID ${assignment.loading_location_id} → "${assignment.loading_location || 'NULL'}"`);
      console.log(`      Unloading: ID ${assignment.unloading_location_id} → "${assignment.unloading_location || 'NULL'}"`);
    });
    
    // Check if any locations have been deleted or have different status
    console.log('\n🔍 Checking location status:');
    
    const locationStatusResult = await client.query(`
      SELECT DISTINCT
        l.id, l.name, l.status, l.created_at, l.updated_at
      FROM locations l
      JOIN assignments a ON (l.id = a.loading_location_id OR l.id = a.unloading_location_id)
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.truck_number = 'DT-100'
      ORDER BY l.id
    `);
    
    console.log(`📊 Location status for DT-100 assignments:`);
    locationStatusResult.rows.forEach((location, i) => {
      console.log(`   ${i+1}. ID ${location.id}: "${location.name}" (Status: ${location.status})`);
      console.log(`      Created: ${location.created_at}`);
      console.log(`      Updated: ${location.updated_at}`);
    });
    
  } catch (error) {
    console.error('Test failed:', error.message);
  } finally {
    client.release();
  }
}

testExactQuery();
