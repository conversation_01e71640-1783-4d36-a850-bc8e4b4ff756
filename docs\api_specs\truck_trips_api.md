# Truck Trips API Specifications

## Base URL
`/api/trips`

## Authentication
<PERSON><PERSON> (JWT) required for all endpoints

## Endpoints

### 1. GET /summary
**Description**: Get aggregated trip counts by truck

**Parameters**:
```json
{
  "start_date": "YYYY-MM-DD",
  "end_date": "YYYY-MM-DD",
  "material_type": "optional string"
}
```

**Response**:
```json
{
  "data": [
    {
      "truck_id": "string",
      "license_plate": "string",
      "trip_count": "integer",
      "total_distance": "number"
    }
  ],
  "meta": {
    "total_trips": "integer",
    "average_trips": "number"
  }
}
```

### 2. GET /details
**Description**: Get detailed trip records

**Parameters**:
```json
{
  "truck_id": "string",
  "start_date": "YYYY-MM-DD",
  "end_date": "YYYY-MM-DD",
  "page": "integer",
  "limit": "integer"
}
```

**Response**:
```json
{
  "data": [
    {
      "trip_id": "string",
      "start_time": "ISO8601",
      "end_time": "ISO8601",
      "origin": "string",
      "destination": "string",
      "distance": "number",
      "material_type": "string"
    }
  ],
  "pagination": {
    "total": "integer",
    "page": "integer",
    "limit": "integer"
  }
}
```

### 3. GET /stats
**Description**: Get trip statistics

**Parameters**:
```json
{
  "time_frame": "day|week|month|year",
  "truck_id": "optional string"
}
```

**Response**:
```json
{
  "data": {
    "trip_counts": [
      {
        "period": "string",
        "count": "integer"
      }
    ],
    "distance_stats": {
      "total": "number",
      "average": "number"
    }
  }
}
```

## Error Responses
```json
{
  "error": {
    "code": "string",
    "message": "string",
    "details": "optional object"
  }
}