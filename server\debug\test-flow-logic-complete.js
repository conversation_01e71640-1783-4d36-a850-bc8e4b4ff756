#!/usr/bin/env node

/**
 * Test Complete Flow Logic Implementation
 * Tests the A→B→A, A→B→C, C→B→C flow patterns as documented in FLOW_LOGIC_COMPLETE.md
 */

const { getClient, query } = require('../config/database');

async function testCompleteFlowLogic() {
  console.log('🧪 Testing Complete Flow Logic Implementation...\n');
  
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // Test 1: Standard Flow (A→B→A)
    console.log('1. Testing Standard Flow (A→B→A)...');
    await testStandardFlow(client);
    
    // Test 2: Exception Flow (A→B→C with exception)
    console.log('\n2. Testing Exception Flow (A→B→C)...');
    await testExceptionFlow(client);
    
    // Test 3: New Standard Flow (C→B→C after approval)
    console.log('\n3. Testing New Standard Flow (C→B→C)...');
    await testNewStandardFlow(client);
    
    // Test 4: Duplicate Prevention Logic
    console.log('\n4. Testing Duplicate Prevention Logic...');
    await testDuplicatePrevention(client);
    
    await client.query('ROLLBACK');
    console.log('\n🎉 All flow logic tests completed successfully!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    client.release();
  }
}

async function testStandardFlow(client) {
  console.log('   Creating standard assignment: Point A → Point B...');
  
  // Get test data
  const locations = await client.query(`
    SELECT id, name, location_code, type 
    FROM locations 
    WHERE type IN ('loading', 'unloading') 
    ORDER BY type, name 
    LIMIT 3
  `);
  
  if (locations.rows.length < 2) {
    console.log('   ⚠️  Insufficient locations for testing');
    return;
  }
  
  const pointA = locations.rows.find(l => l.type === 'loading') || locations.rows[0];
  const pointB = locations.rows.find(l => l.type === 'unloading') || locations.rows[1];
  
  const truck = await client.query('SELECT id, truck_number FROM dump_trucks LIMIT 1');
  const driver = await client.query('SELECT id, full_name FROM drivers LIMIT 1');
  
  if (truck.rows.length === 0 || driver.rows.length === 0) {
    console.log('   ⚠️  Missing truck or driver data');
    return;
  }
  
  // Create standard assignment
  const assignmentCode = `TEST-STANDARD-${Date.now()}`;
  const assignment = await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id,
      loading_location_id, unloading_location_id,
      assigned_date, status, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING *
  `, [assignmentCode, truck.rows[0].id, driver.rows[0].id, pointA.id, pointB.id]);
  
  console.log(`   ✅ Created assignment: ${pointA.name} → ${pointB.name}`);
  console.log(`   ✅ Assignment Code: ${assignment.rows[0].assignment_code}`);
  console.log(`   ✅ Expected Flow: ${pointA.name} → ${pointB.name} → ${pointA.name} → Trip Complete`);
}

async function testExceptionFlow(client) {
  console.log('   Testing route deviation (A→B→C exception)...');
  
  // Get different locations for exception
  const locations = await client.query(`
    SELECT id, name, location_code, type 
    FROM locations 
    ORDER BY name 
    LIMIT 4
  `);
  
  if (locations.rows.length < 4) {
    console.log('   ⚠️  Insufficient locations for testing');
    return;
  }
  
  const pointA = locations.rows[0];
  const pointB = locations.rows[1]; 
  const pointC = locations.rows[2]; // Exception location (different from pointA)
  const pointD = locations.rows[3]; // Different unloading to avoid constraint violation
  
  const trucks = await client.query('SELECT id, truck_number FROM dump_trucks LIMIT 2');
  const driver = await client.query('SELECT id, full_name FROM drivers LIMIT 1');
  
  if (trucks.rows.length < 2) {
    console.log('   ⚠️  Need at least 2 trucks for testing');
    return;
  }
  
  // Use different truck for this test to avoid constraint violation
  const truck = trucks.rows[1]; // Use second truck
  
  // Create original assignment A→D (using different unloading location)
  const assignmentCode = `TEST-EXCEPTION-${Date.now()}`;
  const originalAssignment = await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id,
      loading_location_id, unloading_location_id,
      assigned_date, status, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING *
  `, [assignmentCode, truck.id, driver.rows[0].id, pointA.id, pointD.id]);
  
  // Create route deviation assignment C→D (pending approval)
  const deviationCode = `TEST-DEVIATION-${Date.now()}`;
  const deviationAssignment = await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id,
      loading_location_id, unloading_location_id,
      assigned_date, status, notes, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'pending_approval', $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING *
  `, [
    deviationCode, 
    truck.id, 
    driver.rows[0].id, 
    pointC.id, // C as loading
    pointD.id, // D as unloading (same as original)
    JSON.stringify({
      type: 'route_deviation',
      original_assignment_id: originalAssignment.rows[0].id,
      original_flow: `${pointA.name} → ${pointD.name} → ${pointA.name}`,
      new_flow: `${pointC.name} → ${pointD.name} → ${pointC.name}`
    })
  ]);
  
  console.log(`   ✅ Original Assignment: ${pointA.name} → ${pointD.name}`);
  console.log(`   ✅ Exception Assignment: ${pointC.name} → ${pointD.name} (PENDING APPROVAL)`);
  console.log(`   ✅ Flow Pattern: ${pointA.name} → ${pointD.name} → ${pointC.name} → Exception!`);
}

async function testNewStandardFlow(client) {
  console.log('   Testing new standard flow after approval (C→B→C)...');
  
  // Simulate an approved deviation assignment
  const locations = await client.query(`
    SELECT id, name, location_code, type 
    FROM locations 
    ORDER BY name 
    LIMIT 4
  `);
  
  const pointC = locations.rows[2]; // Use 3rd location
  const pointE = locations.rows[3]; // Use 4th location for unloading
  
  const trucks = await client.query('SELECT id, truck_number FROM dump_trucks LIMIT 3');
  const driver = await client.query('SELECT id, full_name FROM drivers LIMIT 1');
  
  if (trucks.rows.length < 3) {
    console.log('   ⚠️  Need at least 3 trucks for testing');
    return;
  }
  
  // Use third truck to avoid constraint violations
  const truck = trucks.rows[2];
  
  // Create approved new standard assignment C→E
  const newStandardCode = `TEST-NEW-STANDARD-${Date.now()}`;
  const newStandardAssignment = await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id,
      loading_location_id, unloading_location_id,
      assigned_date, status, notes, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'assigned', $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING *
  `, [
    newStandardCode, 
    truck.id, 
    driver.rows[0].id, 
    pointC.id, // C as loading
    pointE.id, // E as unloading
    JSON.stringify({
      type: 'approved_deviation',
      is_standard_flow: true,
      flow_type: 'revised',
      pattern: `${pointC.name} → ${pointE.name} → ${pointC.name}`
    })
  ]);
  
  console.log(`   ✅ New Standard Assignment: ${pointC.name} → ${pointE.name}`);
  console.log(`   ✅ Expected Flow: ${pointC.name} → ${pointE.name} → ${pointC.name} → Trip Complete`);
  console.log(`   ✅ Status: This is now the new standard flow for this truck`);
}

async function testDuplicatePrevention(client) {
  console.log('   Testing duplicate prevention logic...');
  
  const truck = await client.query('SELECT id, truck_number FROM dump_trucks LIMIT 1');
  const driver = await client.query('SELECT id, full_name FROM drivers LIMIT 1');
  const locations = await client.query(`
    SELECT id, name FROM locations 
    WHERE type IN ('loading', 'unloading') 
    ORDER BY type, name 
    LIMIT 3
  `);
  
  const pointA = locations.rows[0];
  const pointB = locations.rows[1];
  const pointC = locations.rows[2];
  
  // Test 1: Same truck, same locations (should be prevented)
  console.log('   Testing exact duplicate prevention...');
  
  const assignment1Code = `TEST-DUP1-${Date.now()}`;
  await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id,
      loading_location_id, unloading_location_id,
      assigned_date, status, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `, [assignment1Code, truck.rows[0].id, driver.rows[0].id, pointA.id, pointB.id]);
  
  console.log(`   ✅ Created first assignment: ${pointA.name} → ${pointB.name}`);
  
  // Try to create exact duplicate (should fail)
  try {
    const duplicateCode = `TEST-DUP2-${Date.now()}`;
    await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        assigned_date, status, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `, [duplicateCode, truck.rows[0].id, driver.rows[0].id, pointA.id, pointB.id]);
    
    console.log('   ❌ Duplicate assignment was allowed (this should not happen)');
  } catch (error) {
    if (error.constraint === 'idx_assignments_exact_duplicate') {
      console.log('   ✅ Exact duplicate correctly prevented by constraint');
    } else {
      console.log(`   ⚠️  Unexpected error: ${error.message}`);
    }
  }
  
  // Test 2: Same truck, different locations (should be allowed)
  console.log('   Testing different locations (should be allowed)...');
  
  const assignment3Code = `TEST-DUP3-${Date.now()}`;
  await client.query(`
    INSERT INTO assignments (
      assignment_code, truck_id, driver_id,
      loading_location_id, unloading_location_id,
      assigned_date, status, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'assigned', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `, [assignment3Code, truck.rows[0].id, driver.rows[0].id, pointC.id, pointB.id]);
  
  console.log(`   ✅ Different loading location allowed: ${pointC.name} → ${pointB.name}`);
  
  // Summary
  console.log('   ✅ Duplicate prevention working correctly:');
  console.log('       - Exact duplicates (same truck + same locations) are prevented');
  console.log('       - Different locations (same truck + different locations) are allowed');
  console.log('       - This enables the A→B→A, A→B→C, C→B→C flow pattern evolution');
}

// Run the test if called directly
if (require.main === module) {
  testCompleteFlowLogic().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { testCompleteFlowLogic };
