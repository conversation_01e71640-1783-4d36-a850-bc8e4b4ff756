import React, { useState, useEffect } from 'react';
import * as ReactHookForm from 'react-hook-form';
const { useForm } = ReactHookForm;

const TruckFormModal = ({ truck, onClose, onSubmit }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = !!truck;

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    defaultValues: {
      truck_number: '',
      license_plate: '',
      make: '',
      model: '',
      year: '',
      capacity_tons: '',
      status: 'active',
      notes: ''
    }
  });

  // Populate form when editing
  useEffect(() => {
    if (truck) {
      reset({
        truck_number: truck.truck_number || '',
        license_plate: truck.license_plate || '',
        make: truck.make || '',
        model: truck.model || '',
        year: truck.year || '',
        capacity_tons: truck.capacity_tons || '',
        status: truck.status || 'active',
        notes: truck.notes || ''
      });
    }
  }, [truck, reset]);

  const onFormSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // Convert empty strings to null for numeric fields
      const formData = {
        ...data,
        year: data.year ? parseInt(data.year) : null,
        capacity_tons: data.capacity_tons ? parseFloat(data.capacity_tons) : null
      };

      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      {/* Backdrop */}
      <div 
        className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
        onClick={handleBackdropClick}
      >
        <div className="fixed inset-0 bg-secondary-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        {/* Modal */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-secondary-900" id="modal-title">
              {isEditing ? 'Edit Truck' : 'Add New Truck'}
            </h3>
            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="text-secondary-400 hover:text-secondary-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
            {/* Truck Number */}
            <div>
              <label htmlFor="truck_number" className="block text-sm font-medium text-secondary-700 mb-1">
                Truck Number *
              </label>
              <input
                type="text"
                id="truck_number"
                {...register('truck_number', {
                  required: 'Truck number is required',
                  minLength: {
                    value: 3,
                    message: 'Truck number must be at least 3 characters'
                  },
                  maxLength: {
                    value: 20,
                    message: 'Truck number must not exceed 20 characters'
                  }
                })}
                className={`input ${errors.truck_number ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                placeholder="e.g., DT-103"
                disabled={isSubmitting}
              />
              {errors.truck_number && (
                <p className="text-danger-600 text-sm mt-1">{errors.truck_number.message}</p>
              )}
            </div>

            {/* License Plate */}
            <div>
              <label htmlFor="license_plate" className="block text-sm font-medium text-secondary-700 mb-1">
                License Plate *
              </label>
              <input
                type="text"
                id="license_plate"
                {...register('license_plate', {
                  required: 'License plate is required',
                  minLength: {
                    value: 3,
                    message: 'License plate must be at least 3 characters'
                  },
                  maxLength: {
                    value: 20,
                    message: 'License plate must not exceed 20 characters'
                  }
                })}
                className={`input ${errors.license_plate ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                placeholder="e.g., TRK-004"
                disabled={isSubmitting}
              />
              {errors.license_plate && (
                <p className="text-danger-600 text-sm mt-1">{errors.license_plate.message}</p>
              )}
            </div>

            {/* Make and Model */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="make" className="block text-sm font-medium text-secondary-700 mb-1">
                  Make
                </label>
                <input
                  type="text"
                  id="make"
                  {...register('make', {
                    maxLength: {
                      value: 50,
                      message: 'Make must not exceed 50 characters'
                    }
                  })}
                  className={`input ${errors.make ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                  placeholder="e.g., Volvo"
                  disabled={isSubmitting}
                />
                {errors.make && (
                  <p className="text-danger-600 text-sm mt-1">{errors.make.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="model" className="block text-sm font-medium text-secondary-700 mb-1">
                  Model
                </label>
                <input
                  type="text"
                  id="model"
                  {...register('model', {
                    maxLength: {
                      value: 50,
                      message: 'Model must not exceed 50 characters'
                    }
                  })}
                  className={`input ${errors.model ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                  placeholder="e.g., VHD"
                  disabled={isSubmitting}
                />
                {errors.model && (
                  <p className="text-danger-600 text-sm mt-1">{errors.model.message}</p>
                )}
              </div>
            </div>

            {/* Year and Capacity */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="year" className="block text-sm font-medium text-secondary-700 mb-1">
                  Year
                </label>
                <input
                  type="number"
                  id="year"
                  {...register('year', {
                    min: {
                      value: 1990,
                      message: 'Year must be 1990 or later'
                    },
                    max: {
                      value: new Date().getFullYear() + 1,
                      message: `Year must not exceed ${new Date().getFullYear() + 1}`
                    }
                  })}
                  className={`input ${errors.year ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                  placeholder="e.g., 2022"
                  disabled={isSubmitting}
                />
                {errors.year && (
                  <p className="text-danger-600 text-sm mt-1">{errors.year.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="capacity_tons" className="block text-sm font-medium text-secondary-700 mb-1">
                  Capacity (tons)
                </label>
                <input
                  type="number"
                  step="0.1"
                  id="capacity_tons"
                  {...register('capacity_tons', {
                    min: {
                      value: 0.1,
                      message: 'Capacity must be greater than 0'
                    },
                    max: {
                      value: 100,
                      message: 'Capacity must not exceed 100 tons'
                    }
                  })}
                  className={`input ${errors.capacity_tons ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                  placeholder="e.g., 15.5"
                  disabled={isSubmitting}
                />
                {errors.capacity_tons && (
                  <p className="text-danger-600 text-sm mt-1">{errors.capacity_tons.message}</p>
                )}
              </div>
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-secondary-700 mb-1">
                Status
              </label>
              <select
                id="status"
                {...register('status')}
                className="input"
                disabled={isSubmitting}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="maintenance">Maintenance</option>
                <option value="retired">Retired</option>
              </select>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-secondary-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                rows={3}
                {...register('notes', {
                  maxLength: {
                    value: 1000,
                    message: 'Notes must not exceed 1000 characters'
                  }
                })}
                className={`input ${errors.notes ? 'border-danger-500 focus:ring-danger-500' : ''}`}
                placeholder="Optional notes about this truck..."
                disabled={isSubmitting}
              />
              {errors.notes && (
                <p className="text-danger-600 text-sm mt-1">{errors.notes.message}</p>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className="btn btn-secondary disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`btn btn-primary disabled:opacity-50 ${isSubmitting ? 'cursor-not-allowed' : ''}`}
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </div>
                ) : (
                  isEditing ? 'Update Truck' : 'Create Truck'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default TruckFormModal;
