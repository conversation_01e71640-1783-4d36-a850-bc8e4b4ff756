const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');
const QRCode = require('qrcode');

// Validation schemas
const locationSchema = Joi.object({
  location_code: Joi.string().min(3).max(20).required(),
  name: Joi.string().min(2).max(100).required(),
  type: Joi.string().valid('loading', 'unloading', 'checkpoint').required(),
  address: Joi.string().max(500).optional().allow(''),
  coordinates: Joi.string().pattern(/^-?\d+\.?\d*,-?\d+\.?\d*$/).optional().allow(''),
  status: Joi.string().valid('active', 'inactive').optional(),
  notes: Joi.string().max(1000).optional().allow('')
});

const updateLocationSchema = locationSchema.fork(['location_code', 'name', 'type'], (schema) => schema.optional());

// @route   GET /api/locations
// @desc    Get all locations with filtering and search
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      type = '',
      status = '',
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const validSortColumns = ['location_code', 'name', 'type', 'address', 'status', 'created_at'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'name';
    const sortDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toUpperCase() : 'ASC';

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Search functionality
    if (search) {
      paramCount++;
      whereConditions.push(`(
        location_code ILIKE $${paramCount} OR 
        name ILIKE $${paramCount} OR 
        address ILIKE $${paramCount} OR
        coordinates ILIKE $${paramCount} OR
        notes ILIKE $${paramCount}
      )`);
      queryParams.push(`%${search}%`);
    }

    // Type filter
    if (type) {
      paramCount++;
      whereConditions.push(`type = $${paramCount}`);
      queryParams.push(type);
    }

    // Status filter
    if (status !== '') {
      paramCount++;
      whereConditions.push(`status = $${paramCount}`);
      queryParams.push(status);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM locations ${whereClause}`;
    const countResult = await query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data
    const dataQuery = `
      SELECT
        id, location_code, name, type, address, coordinates,
        status, notes, created_at, updated_at
      FROM locations
      ${whereClause}
      ORDER BY ${sortColumn} ${sortDirection}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
    
    queryParams.push(parseInt(limit), offset);
    const locationsResult = await query(dataQuery, queryParams);

    const totalPages = Math.ceil(totalItems / parseInt(limit));

    res.json({
      success: true,
      data: locationsResult.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get locations error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve locations'
    });
  }
});

// @route   GET /api/locations/:id
// @desc    Get single location by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      'SELECT * FROM locations WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Location not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get location error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve location'
    });
  }
});

// @route   POST /api/locations
// @desc    Create new location
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    // Validate input
    const { error } = locationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      location_code,
      name,
      type,
      address = '',
      coordinates = '',
      status = 'active',
      notes = ''
    } = req.body;

    // Check for duplicate location code
    const duplicateCheck = await query(
      'SELECT id FROM locations WHERE location_code = $1',
      [location_code]
    );

    if (duplicateCheck.rows.length > 0) {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Location code already exists'
      });
    }

    // Generate QR code data (as object for JSONB storage)
    const qrCodeData = {
      type: 'location',
      id: location_code,
      name: name,
      coordinates: coordinates || null,
      timestamp: new Date().toISOString()
    };

    // Insert new location
    const result = await query(
      `INSERT INTO locations
       (location_code, name, type, address, coordinates, qr_code_data, status, notes)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       RETURNING *`,
      [location_code, name, type, address, coordinates, qrCodeData, status, notes]
    );

    res.status(201).json({
      success: true,
      message: 'Location created successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Create location error:', error);
    
    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Location code already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create location'
    });
  }
});

// @route   PUT /api/locations/:id
// @desc    Update location
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateLocationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    // Check if location exists
    const existingLocation = await query(
      'SELECT * FROM locations WHERE id = $1',
      [id]
    );

    if (existingLocation.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Location not found'
      });
    }

    const {
      location_code,
      name,
      type,
      address,
      coordinates,
      status,
      notes
    } = req.body;

    // Check for duplicates (excluding current location)
    if (location_code) {
      const duplicateCheck = await query(
        'SELECT id FROM locations WHERE location_code = $1 AND id != $2',
        [location_code, id]
      );

      if (duplicateCheck.rows.length > 0) {
        return res.status(400).json({
          error: 'Duplicate Error',
          message: 'Location code already exists'
        });
      }
    }

    // Build update query dynamically
    const updates = [];
    const values = [];
    let paramCount = 0;

    const fields = {
      location_code,
      name,
      type,
      address,
      coordinates,
      status,
      notes
    };

    Object.entries(fields).forEach(([key, value]) => {
      if (value !== undefined) {
        paramCount++;
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
      }
    });

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No fields to update'
      });
    }

    // Update QR code data if relevant fields changed
    if (location_code || name || coordinates) {
      const finalLocationCode = location_code || existingLocation.rows[0].location_code;
      const finalName = name || existingLocation.rows[0].name;
      const finalCoordinates = coordinates !== undefined ? coordinates : existingLocation.rows[0].coordinates;

      const qrCodeData = {
        type: 'location',
        id: finalLocationCode,
        name: finalName,
        coordinates: finalCoordinates || null,
        timestamp: new Date().toISOString()
      };

      paramCount++;
      updates.push(`qr_code_data = $${paramCount}`);
      values.push(qrCodeData);
    }

    // Add updated_at
    paramCount++;
    updates.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add id for WHERE clause
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE locations 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await query(updateQuery, values);

    res.json({
      success: true,
      message: 'Location updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update location error:', error);
    
    if (error.code === '23505') {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Location code already exists'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update location'
    });
  }
});

// @route   DELETE /api/locations/:id
// @desc    Delete location
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if location exists
    const existingLocation = await query(
      'SELECT * FROM locations WHERE id = $1',
      [id]
    );

    if (existingLocation.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Location not found'
      });
    }

    // Check if location has active assignments
    const activeAssignments = await query(
      'SELECT id FROM assignments WHERE (loading_location_id = $1 OR unloading_location_id = $1) AND status IN ($2, $3)',
      [id, 'assigned', 'in_progress']
    );

    if (activeAssignments.rows.length > 0) {
      return res.status(400).json({
        error: 'Conflict',
        message: 'Cannot delete location with active assignments'
      });
    }

    // Delete location
    try {
      await query('DELETE FROM locations WHERE id = $1', [id]);
    } catch (dbError) {
      // Check for foreign key violation (other tables referencing this location)
      if (dbError.code === '23503') {
        return res.status(400).json({
          error: 'Conflict',
          message: 'Cannot delete location: it is referenced by other records (e.g., trips, scan logs, or historical assignments). Please remove related data first.'
        });
      }
      throw dbError;
    }

    res.json({
      success: true,
      message: 'Location deleted successfully'
    });

  } catch (error) {
    console.error('Delete location error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete location'
    });
  }
});

// @route   GET /api/locations/:id/qr
// @desc    Generate QR code for location
// @access  Private
router.get('/:id/qr', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query(
      'SELECT location_code, name, type, address, coordinates, qr_code_data FROM locations WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Location not found'
      });
    }

    const location = result.rows[0];

    // Convert JSONB qr_code_data to string for QR code generation
    const qrDataString = typeof location.qr_code_data === 'string'
      ? location.qr_code_data
      : JSON.stringify(location.qr_code_data);

    const qrCodeDataURL = await QRCode.toDataURL(qrDataString, {
      width: 300,
      margin: 2,
      color: { dark: '#000000', light: '#FFFFFF' }
    });

    res.json({
      success: true,
      data: {
        location_code: location.location_code,
        name: location.name,
        type: location.type,
        address: location.address,
        coordinates: location.coordinates,
        qr_code: qrCodeDataURL,
        qr_data: location.qr_code_data // Already parsed as JSONB
      }
    });
  } catch (error) {
    console.error('QR generation error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to generate QR code'
    });
  }
});

module.exports = router;