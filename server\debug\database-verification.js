#!/usr/bin/env node

/**
 * Database State Verification for Route Deviation Fix
 * 
 * This script connects to the database and verifies the actual state
 */


const fs = require('fs');
const path = require('path');

console.log('🔍 Database Verification for Route Deviation Fix');
console.log('='.repeat(70));

function checkEnvironmentConfiguration() {
  console.log('\n📝 1. CHECKING ENVIRONMENT CONFIGURATION');
  console.log('-'.repeat(50));
  
  const serverEnvPath = path.join(__dirname, '..', '.env');
  const envExamplePath = path.join(__dirname, '..', '.env.example');
  
  console.log(`   🔍 Looking for .env file at: ${serverEnvPath}`);
  
  if (fs.existsSync(serverEnvPath)) {
    console.log('   ✅ .env file exists');
    
    try {
      const envContent = fs.readFileSync(serverEnvPath, 'utf8');
      const hasPassword = envContent.includes('DB_PASSWORD=') && 
                         !envContent.includes('DB_PASSWORD=') || 
                         envContent.match(/DB_PASSWORD=.+/);
      
      if (hasPassword) {
        console.log('   ✅ DB_PASSWORD is configured');
      } else {
        console.log('   ❌ DB_PASSWORD is missing or empty');
        return false;
      }
      
      // Check other required vars
      const requiredVars = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER'];
      let allPresent = true;
      
      requiredVars.forEach(varName => {
        if (envContent.includes(`${varName}=`)) {
          console.log(`   ✅ ${varName} is configured`);
        } else {
          console.log(`   ❌ ${varName} is missing`);
          allPresent = false;
        }
      });
      
      return allPresent && hasPassword;
      
    } catch (error) {
      console.log('   ❌ Error reading .env file:', error.message);
      return false;
    }
  } else {
    console.log('   ❌ .env file does not exist');
    
    if (fs.existsSync(envExamplePath)) {
      console.log('   💡 .env.example file found');
      console.log('   🔧 Copy .env.example to .env and update the password');
    }
    
    return false;
  }
}

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

const { Pool } = require('pg');

// Create direct database connection to avoid the existing configuration issues
async function createDirectConnection() {
  console.log('🔌 Creating direct database connection...');
  
  // Read environment variables with fallbacks
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'hauling_qr_system',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '', // Handle empty password
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    connectionTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    max: 10
  };
  
  console.log('📊 Database configuration:');
  console.log(`   Host: ${dbConfig.host}`);
  console.log(`   Port: ${dbConfig.port}`);
  console.log(`   Database: ${dbConfig.database}`);
  console.log(`   User: ${dbConfig.user}`);
  console.log(`   Password: ${dbConfig.password ? '***configured***' : '(empty)'}`);
  
  // Handle empty password case
  if (!dbConfig.password || dbConfig.password.trim() === '') {
    console.log('⚠️  No password configured - trying without password for local development');
    delete dbConfig.password; // Remove password entirely for local PostgreSQL
  }
  
  const pool = new Pool(dbConfig);
  
  // Test connection
  try {
    const client = await pool.connect();
    console.log('✅ Database connection successful!');
    return { pool, client };
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    
    if (error.message.includes('password')) {
      console.log('');
      console.log('🔧 PASSWORD TROUBLESHOOTING:');
      console.log('1. Check if PostgreSQL is running locally without password');
      console.log('2. Try connecting with: psql -U postgres -d hauling_qr_system');
      console.log('3. If that works, remove or empty DB_PASSWORD in .env file');
      console.log('4. Or set the correct password in .env file');
    }
    
    throw error;
  }
}

async function verifyDatabaseState() {
  console.log('\n📊 CONNECTING TO DATABASE...');
  
  let pool, client;  try {
    const connection = await createDirectConnection();
    pool = connection.pool;
    client = connection.client;
    
    console.log('✅ Database connection successful!');
    
    // 1. Check for NULL driver_ids
    console.log('\n🔍 1. CHECKING FOR NULL DRIVER_IDS...');
    const nullDriverCheck = await client.query(`
      SELECT id, assignment_code, truck_id, driver_id, status, assigned_date
      FROM assignments 
      WHERE driver_id IS NULL
      ORDER BY created_at DESC
      LIMIT 5
    `);
    
    if (nullDriverCheck.rows.length === 0) {
      console.log('✅ No NULL driver_ids found - Migration successful!');
    } else {
      console.log(`❌ Found ${nullDriverCheck.rows.length} assignments with NULL driver_id:`);
      nullDriverCheck.rows.forEach(row => {
        console.log(`   ID: ${row.id}, Code: ${row.assignment_code}, Truck: ${row.truck_id}, Date: ${row.assigned_date}`);
      });
      console.log('\n🔧 FIXING NULL DRIVER_IDS...');
      await fixNullDriverIds(client);
    }
    
    // 2. Check active drivers
    console.log('\n🔍 2. CHECKING ACTIVE DRIVERS...');
    const activeDrivers = await client.query(`
      SELECT id, full_name, status, license_number
      FROM drivers 
      WHERE status = 'active'
      ORDER BY created_at ASC
    `);
    
    if (activeDrivers.rows.length > 0) {
      console.log(`✅ Found ${activeDrivers.rows.length} active driver(s):`);
      activeDrivers.rows.forEach(driver => {
        console.log(`   ID: ${driver.id}, Name: ${driver.full_name}, License: ${driver.license_number}`);
      });
    } else {
      console.log('❌ No active drivers found!');
      console.log('🔧 CREATING TEST DRIVER...');
      await createTestDriver(client);
    }
    
    // 3. Check DT-100 truck
    console.log('\n🔍 3. CHECKING DT-100 TRUCK...');
    const dt100Check = await client.query(`
      SELECT id, truck_number, status, license_plate, qr_code_data
      FROM dump_trucks 
      WHERE truck_number = 'DT-100'
    `);
    
    if (dt100Check.rows.length > 0) {
      const truck = dt100Check.rows[0];
      console.log(`✅ DT-100 truck found:`);
      console.log(`   ID: ${truck.id}, Status: ${truck.status}, License: ${truck.license_plate}`);
    } else {
      console.log('❌ DT-100 truck not found!');
      console.log('🔧 CREATING DT-100 TRUCK...');
      await createDT100Truck(client);
    }
    
    // 4. Check locations
    console.log('\n🔍 4. CHECKING LOCATIONS...');
    const locationsCheck = await client.query(`
      SELECT id, location_code, name, type, is_active
      FROM locations 
      WHERE is_active = true
      ORDER BY type, name
      LIMIT 10
    `);
    
    if (locationsCheck.rows.length >= 2) {
      console.log(`✅ Found ${locationsCheck.rows.length} active locations:`);
      locationsCheck.rows.forEach(loc => {
        console.log(`   ID: ${loc.id}, Code: ${loc.location_code}, Name: ${loc.name}, Type: ${loc.type}`);
      });
    } else {
      console.log('❌ Insufficient locations found!');
      console.log('🔧 CREATING TEST LOCATIONS...');
      await createTestLocations(client);
    }
    
    // 5. Check assignments for DT-100 today
    console.log('\n🔍 5. CHECKING DT-100 ASSIGNMENTS FOR TODAY...');
    const todayAssignments = await client.query(`
      SELECT 
        a.id, a.assignment_code, a.status, a.driver_id,
        a.loading_location_id, a.unloading_location_id,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        d.full_name as driver_name
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = 'DT-100'
        AND a.assigned_date = CURRENT_DATE
        AND a.status IN ('assigned', 'in_progress')
    `);
    
    if (todayAssignments.rows.length > 0) {
      console.log(`✅ Found ${todayAssignments.rows.length} assignment(s) for DT-100 today:`);
      todayAssignments.rows.forEach(assign => {
        console.log(`   ID: ${assign.id}, Code: ${assign.assignment_code}`);
        console.log(`   Route: ${assign.loading_location} → ${assign.unloading_location}`);
        console.log(`   Driver: ${assign.driver_name} (ID: ${assign.driver_id})`);
        console.log(`   Status: ${assign.status}`);
      });
    } else {
      console.log('❌ No assignments for DT-100 today!');
      console.log('🔧 CREATING TEST ASSIGNMENT...');
      await createTestAssignment(client);
    }
    
    return true;
    
  } catch (error) {
    console.log('❌ Database verification failed:', error.message);
    console.log('Full error:', error);
    return false;  } finally {
    if (client) client.release();
    if (pool) await pool.end();
  }
}

async function fixNullDriverIds(client) {
  try {
    // Get first active driver
    const activeDriver = await client.query(`
      SELECT id FROM drivers WHERE status = 'active' ORDER BY created_at ASC LIMIT 1
    `);
    
    if (activeDriver.rows.length === 0) {
      console.log('   ⚠️  No active drivers to assign, creating one first...');
      await createTestDriver(client);
      return await fixNullDriverIds(client);
    }
    
    const driverId = activeDriver.rows[0].id;
    
    const updateResult = await client.query(`
      UPDATE assignments 
      SET driver_id = $1, updated_at = NOW()
      WHERE driver_id IS NULL
    `, [driverId]);
    
    console.log(`   ✅ Fixed ${updateResult.rowCount} assignments with NULL driver_id`);
    
  } catch (error) {
    console.log('   ❌ Error fixing NULL driver_ids:', error.message);
  }
}

async function createTestDriver(client) {
  try {
    const result = await client.query(`
      INSERT INTO drivers (full_name, license_number, phone, status, created_at, updated_at)
      VALUES ('Test Driver', 'LIC-001', '555-0001', 'active', NOW(), NOW())
      ON CONFLICT (license_number) DO NOTHING
      RETURNING id, full_name
    `);
    
    if (result.rows.length > 0) {
      console.log(`   ✅ Created test driver: ${result.rows[0].full_name} (ID: ${result.rows[0].id})`);
    } else {
      console.log('   ℹ️  Test driver already exists');
    }
    
  } catch (error) {
    console.log('   ❌ Error creating test driver:', error.message);
  }
}

async function createDT100Truck(client) {
  try {
    const qrCodeData = JSON.stringify({ id: 'DT-100', type: 'truck' });
    
    const result = await client.query(`
      INSERT INTO dump_trucks (truck_number, license_plate, qr_code_data, status, created_at, updated_at)
      VALUES ('DT-100', 'DT-100-LP', $1, 'active', NOW(), NOW())
      ON CONFLICT (truck_number) DO NOTHING
      RETURNING id, truck_number
    `, [qrCodeData]);
    
    if (result.rows.length > 0) {
      console.log(`   ✅ Created DT-100 truck (ID: ${result.rows[0].id})`);
    } else {
      console.log('   ℹ️  DT-100 truck already exists');
    }
    
  } catch (error) {
    console.log('   ❌ Error creating DT-100 truck:', error.message);
  }
}

async function createTestLocations(client) {
  try {
    const locations = [
      { code: 'LOC-A', name: 'Point A', type: 'loading' },
      { code: 'LOC-B', name: 'Point B', type: 'unloading' },
      { code: 'LOC-C', name: 'Point C', type: 'loading' }
    ];
    
    for (const loc of locations) {
      const qrCodeData = JSON.stringify({ id: loc.code, type: 'location' });
      
      const result = await client.query(`
        INSERT INTO locations (location_code, name, type, qr_code_data, is_active, created_at, updated_at)
        VALUES ($1, $2, $3, $4, true, NOW(), NOW())
        ON CONFLICT (location_code) DO NOTHING
        RETURNING id, name
      `, [loc.code, loc.name, loc.type, qrCodeData]);
      
      if (result.rows.length > 0) {
        console.log(`   ✅ Created location: ${result.rows[0].name} (ID: ${result.rows[0].id})`);
      }
    }
    
  } catch (error) {
    console.log('   ❌ Error creating test locations:', error.message);
  }
}

async function createTestAssignment(client) {
  try {
    // Get required IDs
    const truckResult = await client.query(`
      SELECT id FROM dump_trucks WHERE truck_number = 'DT-100' LIMIT 1
    `);
    
    const driverResult = await client.query(`
      SELECT id FROM drivers WHERE status = 'active' ORDER BY created_at ASC LIMIT 1
    `);
    
    const loadingResult = await client.query(`
      SELECT id FROM locations WHERE type = 'loading' AND is_active = true ORDER BY name LIMIT 1
    `);
    
    const unloadingResult = await client.query(`
      SELECT id FROM locations WHERE type = 'unloading' AND is_active = true ORDER BY name LIMIT 1
    `);
    
    if (truckResult.rows.length === 0 || driverResult.rows.length === 0 || 
        loadingResult.rows.length === 0 || unloadingResult.rows.length === 0) {
      console.log('   ❌ Missing required data for creating assignment');
      return;
    }
    
    const assignmentCode = `TEST-DT100-${Date.now()}`;
    
    const result = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        assigned_date, status, priority, expected_loads_per_day,
        notes, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, CURRENT_DATE, 'assigned', 'normal', 1, 'Test assignment for route deviation', NOW(), NOW())
      RETURNING id, assignment_code
    `, [
      assignmentCode,
      truckResult.rows[0].id,
      driverResult.rows[0].id,
      loadingResult.rows[0].id,
      unloadingResult.rows[0].id
    ]);
    
    console.log(`   ✅ Created test assignment: ${result.rows[0].assignment_code} (ID: ${result.rows[0].id})`);
    
  } catch (error) {
    console.log('   ❌ Error creating test assignment:', error.message);
  }
}

async function main() {
  console.log('Starting database verification...\n');
  
  // Check environment first
  const envValid = checkEnvironmentConfiguration();
  if (!envValid) {
    console.log('\n❌ Environment configuration invalid. Please fix and try again.');
    return;
  }
  
  const success = await verifyDatabaseState();
  
  console.log('\n' + '='.repeat(70));
  
  if (success) {
    console.log('🎉 DATABASE VERIFICATION COMPLETE!');
    console.log('');
    console.log('✅ Database connection working');
    console.log('✅ No NULL driver_ids');
    console.log('✅ Active drivers exist');
    console.log('✅ DT-100 truck exists');
    console.log('✅ Test locations exist');
    console.log('✅ Assignment for DT-100 exists');
    console.log('');
    console.log('🚀 READY TO TEST ROUTE DEVIATION!');
    console.log('');
    console.log('📋 TEST SCENARIO:');
    console.log('1. DT-100 has assignment for today (Point A → Point B)');
    console.log('2. Scan DT-100 at Point C (route deviation)');
    console.log('3. Should create exception using existing assignment');
    console.log('4. Should NOT create new assignment');
    console.log('5. Should NOT get driver_id constraint error');
    console.log('');
    console.log('🎯 The route deviation flow should now work correctly!');
  } else {
    console.log('❌ DATABASE VERIFICATION FAILED');
    console.log('');
    console.log('Please fix the database connection issues above');
    console.log('and run this script again.');
  }
  
  console.log('='.repeat(70));
}

if (require.main === module) {
  main().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

module.exports = { verifyDatabaseState };
