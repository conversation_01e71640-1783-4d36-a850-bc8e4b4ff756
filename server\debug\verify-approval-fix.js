const { query, getClient } = require('../config/database');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

async function verifyApprovalEndpoint() {
  console.log('🧪 Testing approval endpoint with fixes...');

  const client = await getClient();
  
  try {
    await client.query('BEGIN');

    // Simulate the exact PUT /api/approvals/3 logic with our fixes
    const id = 3;
    const decision = 'approved';
    const notes = 'Test approval from debug';
    const userId = 1; // Mock user ID

    console.log('Step 1: Getting approval details with lock...');
    const approvalResult = await client.query(
      `SELECT a.id, a.status as approval_status, a.exception_type, a.exception_description,
             a.trip_log_id, a.severity, a.reported_by, a.reviewed_by, a.reviewed_at, 
             a.notes as approval_notes, a.created_at, a.updated_at,
             tl.id as trip_id, tl.status as trip_status, tl.notes as trip_notes,
             tl.assignment_id, tl.is_exception, tl.exception_reason
       FROM approvals a
       JOIN trip_logs tl ON a.trip_log_id = tl.id
       WHERE a.id = $1
       FOR UPDATE`,
      [id]
    );

    if (approvalResult.rows.length === 0) {
      console.log('❌ Approval not found');
      await client.query('ROLLBACK');
      return;
    }

    const approval = approvalResult.rows[0];
    console.log('✅ Found approval:', approval.id);
    console.log('  - Approval Status:', approval.approval_status);
    console.log('  - Trip Status:', approval.trip_status);
    console.log('  - Exception Type:', approval.exception_type);

    if (approval.approval_status !== 'pending') {
      console.log('❌ Approval is not pending, status:', approval.approval_status);
      await client.query('ROLLBACK');
      return;
    }

    console.log('Step 2: Updating approval decision...');
    const updateApprovalResult = await client.query(`
      UPDATE approvals 
      SET status = $1, reviewed_by = $2, reviewed_at = CURRENT_TIMESTAMP, 
          notes = $3, updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
      RETURNING *
    `, [decision, userId, notes, id]);

    console.log('✅ Approval updated:', updateApprovalResult.rows[0].status);

    console.log('Step 3: Testing handleApprovedException...');
    const { trip_log_id, exception_description } = approval;
    
    if (exception_description && exception_description.includes('Loading at')) {
      console.log('✅ This is a route deviation for loading');
      
      // Parse trip notes
      let tripNotes;
      try {
        tripNotes = approval.trip_notes ? JSON.parse(approval.trip_notes) : {};
        console.log('✅ Trip notes parsed:', tripNotes);
      } catch (e) {
        console.log('❌ Failed to parse trip notes:', e.message);
        tripNotes = {};
      }

      if (tripNotes.pending_assignment_id) {
        console.log('✅ Has pending assignment ID:', tripNotes.pending_assignment_id);
        
        // Check if assignment exists
        const assignmentCheck = await client.query(
          'SELECT id, status FROM assignments WHERE id = $1',
          [tripNotes.pending_assignment_id]
        );

        if (assignmentCheck.rows.length === 0) {
          console.log('❌ Assignment not found, updating trip status only');
          await client.query(`
            UPDATE trip_logs 
            SET status = 'loading_start',
                exception_approved_by = $1,
                exception_approved_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $2
          `, [userId, trip_log_id]);
        } else {
          console.log('✅ Assignment exists, updating trip and assignment');
          
          // Update trip log to use new assignment
          await client.query(`
            UPDATE trip_logs 
            SET assignment_id = $1, 
                status = 'loading_start',
                exception_approved_by = $2,
                exception_approved_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $3
          `, [tripNotes.pending_assignment_id, userId, trip_log_id]);

          // Update assignment status
          await client.query(`
            UPDATE assignments
            SET status = 'in_progress',
                start_time = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
          `, [tripNotes.pending_assignment_id]);
          
          console.log('✅ Assignment updated to in_progress');
        }
      } else {
        console.log('No pending assignment, just updating trip status');
        await client.query(`
          UPDATE trip_logs 
          SET status = 'loading_start',
              exception_approved_by = $1,
              exception_approved_at = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
        `, [userId, trip_log_id]);
      }
    } else {
      console.log('Not a route deviation, just updating trip status');
      await client.query(`
        UPDATE trip_logs 
        SET exception_approved_by = $1,
            exception_approved_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [userId, trip_log_id]);
    }

    // COMMIT the transaction since this is a test that works
    await client.query('COMMIT');
    
    console.log('Step 4: Getting final approval data...');
    const finalResult = await query(`
      SELECT 
        a.*,
        tl.status as trip_status
      FROM approvals a
      JOIN trip_logs tl ON a.trip_log_id = tl.id
      WHERE a.id = $1
    `, [id]);

    console.log('✅ Final approval state:', finalResult.rows[0].status);
    console.log('✅ Final trip state:', finalResult.rows[0].trip_status);

    console.log('✅ Approval endpoint test successful!');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Test error:', error);
  } finally {
    client.release();
  }
}

verifyApprovalEndpoint();
