const { query } = require('./config/database');

async function checkDatabaseData() {
  try {
    console.log('🔍 Checking database data for analytics...\n');

    // Check fleet data
    console.log('=== FLEET DATA ===');
    
    const trucksResult = await query('SELECT COUNT(*) as total, COUNT(CASE WHEN status = $1 THEN 1 END) as active FROM dump_trucks', ['active']);
    console.log('🚛 Trucks:', trucksResult.rows[0]);
    
    const driversResult = await query('SELECT COUNT(*) as total, COUNT(CASE WHEN status = $1 THEN 1 END) as active FROM drivers', ['active']);
    console.log('👨‍💼 Drivers:', driversResult.rows[0]);
    
    const locationsResult = await query('SELECT COUNT(*) as total, COUNT(CASE WHEN is_active = true THEN 1 END) as active FROM locations');
    console.log('📍 Locations:', locationsResult.rows[0]);
    
    const assignmentsResult = await query('SELECT COUNT(*) as total, COUNT(CASE WHEN status IN ($1, $2) THEN 1 END) as active FROM assignments', ['assigned', 'in_progress']);
    console.log('📋 Assignments:', assignmentsResult.rows[0]);

    // Check trip data
    console.log('\n=== TRIP DATA ===');
    const tripsResult = await query(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) as completed,
        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as today
      FROM trip_logs
    `);
    console.log('🚚 Trips:', tripsResult.rows[0]);

    // Check sample data from each table
    console.log('\n=== SAMPLE DATA ===');
    
    const sampleTrucks = await query('SELECT id, truck_number, status FROM dump_trucks LIMIT 3');
    console.log('Sample trucks:', sampleTrucks.rows);
    
    const sampleDrivers = await query('SELECT id, full_name, status FROM drivers LIMIT 3');
    console.log('Sample drivers:', sampleDrivers.rows);
    
    const sampleLocations = await query('SELECT id, name, type, is_active FROM locations LIMIT 3');
    console.log('Sample locations:', sampleLocations.rows);

    // Test the exact analytics query
    console.log('\n=== ANALYTICS QUERY TEST ===');
    const analyticsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM dump_trucks) as total_trucks,
        (SELECT COUNT(*) FROM dump_trucks WHERE status = 'active') as active_trucks,
        (SELECT COUNT(*) FROM drivers) as total_drivers,
        (SELECT COUNT(*) FROM drivers WHERE status = 'active') as active_drivers,
        (SELECT COUNT(*) FROM locations) as total_locations,
        (SELECT COUNT(*) FROM locations WHERE status = 'active') as active_locations,
        (SELECT COUNT(*) FROM assignments) as total_assignments,
        (SELECT COUNT(*) FROM assignments WHERE status IN ('assigned', 'in_progress')) as active_assignments
    `;
    
    const analyticsResult = await query(analyticsQuery);
    console.log('Analytics result:', analyticsResult.rows[0]);

  } catch (error) {
    console.error('❌ Error checking database data:', error);
  }
  
  process.exit(0);
}

checkDatabaseData();
