import React, { useEffect, useState } from 'react';
import api from '../../services/api';
import ResponsiveTableContainer from '../../components/common/ResponsiveTableContainer';
import { saveAs } from 'file-saver';

const columns = [
  { label: 'Truck Number', key: 'truck_number' },
  { label: 'Driver', key: 'driver_name' },
  { label: 'Loading Location', key: 'loading_location' },
  { label: 'Unloading Location', key: 'unloading_location' },
  { label: 'Trips Completed', key: 'trips_completed' },
  { label: 'Trips In Progress', key: 'trips_in_progress' },
];

const statusOptions = [
  { label: 'All', value: '' },
  { label: 'In Progress', value: 'in_progress' },
  { label: 'Assigned', value: 'assigned' },
  { label: 'Completed', value: 'completed' },
];

const TruckTripSummary = () => {
  const [data, setData] = useState([]);
  const [search, setSearch] = useState('');
  const [sortKey, setSortKey] = useState('truck_number');
  const [sortOrder, setSortOrder] = useState('asc');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState('');
  const [driverFilter, setDriverFilter] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await api.get('/trucks/trip-summary');
      setData(res.data.data || []);
    } catch (err) {
      setError('Failed to load truck trip summary');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (key) => {
    if (sortKey === key) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortKey(key);
      setSortOrder('asc');
    }
  };

  const handleExportCSV = () => {
    const headers = columns.map(col => col.label).join(',');
    const rows = sortedData.map(row =>
      columns.map(col => `"${(row[col.key] || '').toString().replace(/"/g, '""')}"`).join(',')
    );
    const csvContent = [headers, ...rows].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, 'truck_trip_summary.csv');
  };

  const filteredData = data.filter(row => {
    const matchesStatus = !statusFilter ||
      (statusFilter === 'in_progress' && row.trips_in_progress > 0) ||
      (statusFilter === 'assigned' && row.trips_in_progress === 0 && row.trips_completed === 0) ||
      (statusFilter === 'completed' && row.trips_completed > 0);
    const matchesDriver = !driverFilter || (row.driver_name || '').toLowerCase().includes(driverFilter.toLowerCase());
    const matchesSearch = columns.some(col =>
      String(row[col.key] || '').toLowerCase().includes(search.toLowerCase())
    );
    return matchesStatus && matchesDriver && matchesSearch;
  });

  const sortedData = [...filteredData].sort((a, b) => {
    if (a[sortKey] < b[sortKey]) return sortOrder === 'asc' ? -1 : 1;
    if (a[sortKey] > b[sortKey]) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });

  return (
    <div>
      <h2>Truck Trip Summary</h2>
      <div style={{ marginBottom: 16, display: 'flex', gap: 16, flexWrap: 'wrap' }}>
        <input
          type="text"
          placeholder="Search..."
          value={search}
          onChange={e => setSearch(e.target.value)}
          style={{ padding: 8, width: 200 }}
        />
        <select value={statusFilter} onChange={e => setStatusFilter(e.target.value)} style={{ padding: 8 }}>
          {statusOptions.map(opt => (
            <option key={opt.value} value={opt.value}>{opt.label}</option>
          ))}
        </select>
        <input
          type="text"
          placeholder="Filter by driver..."
          value={driverFilter}
          onChange={e => setDriverFilter(e.target.value)}
          style={{ padding: 8, width: 180 }}
        />
        <button onClick={handleExportCSV} style={{ padding: 8, background: '#2563eb', color: 'white', borderRadius: 4, border: 'none' }}>
          Export CSV
        </button>
      </div>
      <ResponsiveTableContainer>
        <table className="table-auto w-full border">
          <thead>
            <tr>
              {columns.map(col => (
                <th
                  key={col.key}
                  className="px-4 py-2 cursor-pointer"
                  onClick={() => handleSort(col.key)}
                >
                  {col.label} {sortKey === col.key ? (sortOrder === 'asc' ? '▲' : '▼') : ''}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr><td colSpan={columns.length}>Loading...</td></tr>
            ) : error ? (
              <tr><td colSpan={columns.length} className="text-red-500">{error}</td></tr>
            ) : sortedData.length === 0 ? (
              <tr><td colSpan={columns.length}>No data found</td></tr>
            ) : (
              sortedData.map(row => (
                <tr key={row.truck_number + row.driver_name + row.loading_location + row.unloading_location}>
                  {columns.map(col => (
                    <td key={col.key} className="px-4 py-2 border-t">{row[col.key]}</td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </ResponsiveTableContainer>
    </div>
  );
};

export default TruckTripSummary;
