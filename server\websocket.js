const WebSocket = require('ws');

let wss = null;
const clients = new Map(); // Store client connections with metadata

// Initialize WebSocket server
const initializeWebSocket = (server) => {
  wss = new WebSocket.Server({ server });

  wss.on('connection', (ws, req) => {
    const clientId = generateClientId();
    
    // Store client with metadata
    clients.set(clientId, {
      ws,
      userId: null,
      role: null,
      lastActivity: Date.now()
    });

    console.log(`WebSocket client connected: ${clientId}`);

    // Handle authentication message
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        
        if (data.type === 'auth') {
          const client = clients.get(clientId);
          if (client) {
            client.userId = data.userId;
            client.role = data.role;
            client.lastActivity = Date.now();
            
            // Send authentication confirmation
            ws.send(JSON.stringify({
              type: 'auth_success',
              message: 'Authenticated successfully'
            }));
            
            console.log(`Client ${clientId} authenticated as user ${data.userId} with role ${data.role}`);
          }
        }
      } catch (error) {
        console.error('WebSocket message parse error:', error);
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      clients.delete(clientId);
      console.log(`WebSocket client disconnected: ${clientId}`);
    });

    // Handle connection errors
    ws.on('error', (error) => {
      console.error(`WebSocket error for client ${clientId}:`, error);
      clients.delete(clientId);
    });

    // Send initial welcome message
    ws.send(JSON.stringify({
      type: 'welcome',
      clientId,
      message: 'Connected to Hauling System WebSocket'
    }));
  });

  // Cleanup inactive connections every 30 seconds
  setInterval(() => {
    const now = Date.now();
    const INACTIVE_THRESHOLD = 5 * 60 * 1000; // 5 minutes

    for (const [clientId, client] of clients.entries()) {
      if (now - client.lastActivity > INACTIVE_THRESHOLD) {
        console.log(`Removing inactive client: ${clientId}`);
        client.ws.terminate();
        clients.delete(clientId);
      }
    }
  }, 30000);

  console.log('WebSocket server initialized');
  return wss;
};

// Generate unique client ID
const generateClientId = () => {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
};

// Broadcast to all authenticated clients
const broadcast = (message) => {
  if (!wss) return;

  const payload = JSON.stringify(message);
  
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN && client.userId) {
      try {
        client.ws.send(payload);
        client.lastActivity = Date.now();
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
};

// Send to specific users by role
const sendToRole = (role, message) => {
  if (!wss) return;

  const payload = JSON.stringify(message);
  
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN && client.role === role) {
      try {
        client.ws.send(payload);
        client.lastActivity = Date.now();
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
};

// Send to specific user
const sendToUser = (userId, message) => {
  if (!wss) return;

  const payload = JSON.stringify(message);
  
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN && client.userId === userId) {
      try {
        client.ws.send(payload);
        client.lastActivity = Date.now();
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        clients.delete(clientId);
      }
    }
  });
};

// Notification functions for specific events
const notifyExceptionCreated = (exception) => {
  // Notify all admins and supervisors
  sendToRole('admin', {
    type: 'exception_created',
    title: 'New Exception Requires Approval',
    message: `Route deviation detected: ${exception.description}`,
    data: exception,
    timestamp: new Date().toISOString(),
    priority: exception.severity || 'medium'
  });

  sendToRole('supervisor', {
    type: 'exception_created',
    title: 'New Exception Requires Approval',
    message: `Route deviation detected: ${exception.description}`,
    data: exception,
    timestamp: new Date().toISOString(),
    priority: exception.severity || 'medium'
  });
};

const notifyExceptionUpdated = (exception, decision) => {
  // Notify all users about exception status update
  broadcast({
    type: 'exception_updated',
    title: `Exception ${decision}`,
    message: `Route deviation ${decision}: ${exception.description}`,
    data: exception,
    decision,
    timestamp: new Date().toISOString()
  });
};

const notifyTripStatusChanged = (trip) => {
  // Notify relevant users about trip status changes
  broadcast({
    type: 'trip_status_changed',
    title: 'Trip Status Updated',
    message: `Trip ${trip.trip_number} status: ${trip.status}`,
    data: trip,
    timestamp: new Date().toISOString()
  });
};

// Get connection statistics
const getStats = () => {
  const totalClients = clients.size;
  const authenticatedClients = Array.from(clients.values()).filter(c => c.userId).length;
  const roles = {};
  
  clients.forEach(client => {
    if (client.role) {
      roles[client.role] = (roles[client.role] || 0) + 1;
    }
  });

  return {
    totalClients,
    authenticatedClients,
    roles,
    uptime: wss ? Date.now() - wss.startTime : 0
  };
};

module.exports = {
  initializeWebSocket,
  broadcast,
  sendToRole,
  sendToUser,
  notifyExceptionCreated,
  notifyExceptionUpdated,
  notifyTripStatusChanged,
  getStats
};
