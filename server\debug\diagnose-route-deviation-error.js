// Diagnostic script to identify the exact cause of the route deviation driver_id error
const { Pool } = require('pg');

// Simple database connection without config file
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_system',
  password: process.env.DB_PASSWORD || '',  // Set this manually if needed
  port: process.env.DB_PORT || 5432,
});

async function diagnoseRouteDeviationError() {
  console.log('🔍 Diagnosing Route Deviation Driver ID Error...\n');
  
  const client = await pool.connect();
  
  try {
    // 1. Check if database migrations have been applied
    console.log('1. 📊 Checking Database Schema:');
    
    const schemaCheck = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'assignments' AND column_name = 'driver_id'
    `);
    
    if (schemaCheck.rows.length > 0) {
      const driverIdColumn = schemaCheck.rows[0];
      console.log(`   ✅ driver_id column exists: ${driverIdColumn.data_type}, nullable: ${driverIdColumn.is_nullable}`);
      
      if (driverIdColumn.is_nullable === 'NO') {
        console.log('   ⚠️  driver_id is NOT NULL - this could cause the error if not properly handled');
      } else {
        console.log('   ✅ driver_id allows NULL values');
      }
    } else {
      console.log('   ❌ driver_id column not found - migration may not be applied');
    }
    
    // 2. Check existing assignments for NULL driver_id
    console.log('\n2. 🔍 Checking Existing Assignments:');
    
    const nullDriverCheck = await client.query(`
      SELECT COUNT(*) as null_count
      FROM assignments 
      WHERE driver_id IS NULL
    `);
    
    console.log(`   Assignments with NULL driver_id: ${nullDriverCheck.rows[0].null_count}`);
    
    if (parseInt(nullDriverCheck.rows[0].null_count) > 0) {
      console.log('   ⚠️  Found assignments with NULL driver_id - this could cause issues');
      
      const nullAssignments = await client.query(`
        SELECT id, assignment_code, truck_id, status, assigned_date, created_at
        FROM assignments 
        WHERE driver_id IS NULL
        ORDER BY created_at DESC
        LIMIT 5
      `);
      
      console.log('   Recent assignments with NULL driver_id:');
      nullAssignments.rows.forEach(assignment => {
        console.log(`     - ${assignment.assignment_code} (ID: ${assignment.id}, Date: ${assignment.assigned_date})`);
      });
    } else {
      console.log('   ✅ All assignments have valid driver_id');
    }
    
    // 3. Check available drivers
    console.log('\n3. 👥 Checking Available Drivers:');
    
    const activeDrivers = await client.query(`
      SELECT id, full_name, status
      FROM drivers 
      WHERE status = 'active'
      ORDER BY created_at ASC
    `);
    
    console.log(`   Active drivers available: ${activeDrivers.rows.length}`);
    
    if (activeDrivers.rows.length > 0) {
      console.log('   Available drivers:');
      activeDrivers.rows.forEach(driver => {
        console.log(`     - ${driver.full_name} (ID: ${driver.id})`);
      });
    } else {
      console.log('   ❌ No active drivers found - this WILL cause errors in auto-assignment');
      console.log('   🔧 Fix: Ensure there are active drivers in the database');
    }
    
    // 4. Test route deviation scenario setup
    console.log('\n4. 🚛 Testing Route Deviation Scenario Setup:');
    
    // Check if DT-100 exists
    const truckCheck = await client.query(`
      SELECT id, truck_number, status
      FROM dump_trucks 
      WHERE truck_number = 'DT-100'
    `);
    
    if (truckCheck.rows.length > 0) {
      const truck = truckCheck.rows[0];
      console.log(`   ✅ Truck DT-100 found (ID: ${truck.id}, Status: ${truck.status})`);
      
      // Check if there's an assignment for today
      const todayAssignment = await client.query(`
        SELECT 
          a.id, a.assignment_code, a.driver_id, a.status,
          a.loading_location_id, a.unloading_location_id,
          ll.name as loading_location, ul.name as unloading_location,
          d.full_name as driver_name
        FROM assignments a
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        LEFT JOIN drivers d ON a.driver_id = d.id
        WHERE a.truck_id = $1 
          AND a.assigned_date = CURRENT_DATE
          AND a.status IN ('assigned', 'in_progress')
        ORDER BY a.created_at DESC
        LIMIT 1
      `, [truck.id]);
      
      if (todayAssignment.rows.length > 0) {
        const assignment = todayAssignment.rows[0];
        console.log(`   ✅ Today's assignment found: ${assignment.assignment_code}`);
        console.log(`   - Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
        console.log(`   - Driver: ${assignment.driver_name || 'NULL'} (ID: ${assignment.driver_id || 'NULL'})`);
        console.log(`   - Status: ${assignment.status}`);
        
        if (!assignment.driver_id) {
          console.log('   ❌ Assignment has NULL driver_id - THIS IS THE PROBLEM!');
          console.log('   🔧 Fix: Update this assignment with a valid driver_id');
        } else {
          console.log('   ✅ Assignment has valid driver_id');
        }
      } else {
        console.log('   ℹ️  No assignment for today - would trigger auto-assignment flow');
      }
    } else {
      console.log('   ❌ Truck DT-100 not found');
    }
    
    // 5. Provide specific recommendations
    console.log('\n5. 🔧 Recommendations:');
    
    if (parseInt(nullDriverCheck.rows[0].null_count) > 0) {
      console.log('   ❌ ISSUE FOUND: Assignments with NULL driver_id exist');
      console.log('   🔧 Fix 1: Update existing assignments with valid driver_id:');
      
      if (activeDrivers.rows.length > 0) {
        console.log(`   UPDATE assignments SET driver_id = ${activeDrivers.rows[0].id} WHERE driver_id IS NULL;`);
      }
    }
    
    if (activeDrivers.rows.length === 0) {
      console.log('   ❌ ISSUE FOUND: No active drivers available');
      console.log('   🔧 Fix 2: Add an active driver to the database');
    }
    
    console.log('\n6. 🎯 Route Deviation Test:');
    console.log('   To test route deviation without errors:');
    console.log('   1. Ensure DT-100 has an assignment for today with valid driver_id');
    console.log('   2. Ensure at least one active driver exists');
    console.log('   3. Scan DT-100 at a location different from assigned loading location');
    console.log('   4. The route deviation should be created using the existing assignment');
    
  } catch (error) {
    console.error('\n❌ Diagnosis failed:', error.message);
    console.error('   Full error:', error);
  } finally {
    client.release();
  }
}

if (require.main === module) {
  diagnoseRouteDeviationError()
    .then(() => {
      console.log('\n🎉 Route Deviation Diagnosis Complete!');
      process.exit(0);
    })
    .catch(error => {
      console.error('Diagnosis failed:', error.message);
      process.exit(1);
    });
}

module.exports = { diagnoseRouteDeviationError };
