/**
 * Test Database Setup Script
 * 
 * Run this script to create and initialize the test database
 * Usage: node tests/setup-test-db.js
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function setupTestDatabase() {
  console.log('🔧 Setting up test database...');
  
  // Connect to PostgreSQL server (not specific database)
  const adminClient = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
    database: 'postgres' // Connect to default postgres database
  });

  try {
    await adminClient.connect();
    
    // Drop test database if it exists
    console.log('📦 Dropping existing test database...');
    await adminClient.query('DROP DATABASE IF EXISTS hauling_qr_system_test');
    
    // Create test database
    console.log('🆕 Creating test database...');
    await adminClient.query('CREATE DATABASE hauling_qr_system_test');
    
    await adminClient.end();
    
    // Connect to the new test database
    const testClient = new Client({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
      database: 'hauling_qr_system_test'
    });
    
    await testClient.connect();
    
    // Read and execute the database schema
    console.log('📋 Initializing database schema...');

    // Try different possible locations for the schema file
    const possiblePaths = [
      path.join(__dirname, '../../database/init.sql'),
      path.join(__dirname, '../database/init.sql'),
      path.join(process.cwd(), 'database/init.sql'),
      path.join(process.cwd(), '../database/init.sql')
    ];

    let schemaPath = null;
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        schemaPath = testPath;
        break;
      }
    }

    if (!schemaPath) {
      console.error('❌ Schema file not found in any of these locations:');
      possiblePaths.forEach(p => console.error(`   - ${p}`));
      throw new Error('Database schema file (init.sql) not found');
    }

    console.log(`📄 Reading schema from: ${schemaPath}`);
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');
    
    await testClient.query(schemaSql);
    
    // Create test admin user
    console.log('👤 Creating test admin user...');
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('testpassword', 10);
    
    await testClient.query(`
      INSERT INTO users (username, email, password_hash, full_name, role, status)
      VALUES ('testadmin', '<EMAIL>', $1, 'Test Administrator', 'admin', 'active')
      ON CONFLICT (username) DO NOTHING
    `, [hashedPassword]);
    
    await testClient.end();
    
    console.log('✅ Test database setup complete!');
    console.log('📊 Database: hauling_qr_system_test');
    console.log('👤 Test user: testadmin / testpassword');
    
  } catch (error) {
    console.error('❌ Test database setup failed:', error);
    process.exit(1);
  }
}

// Run setup if called directly
if (require.main === module) {
  setupTestDatabase();
}

module.exports = { setupTestDatabase };
