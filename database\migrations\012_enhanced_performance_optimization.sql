-- ============================================================================
-- Migration: Enhanced Performance Optimization & Schema Refinements
-- Date: 2025-06-24
-- Description: Additional performance indexes, constraints, and optimizations
--              based on comprehensive schema analysis
-- ============================================================================

-- Add missing performance indexes for complex queries
CREATE INDEX IF NOT EXISTS idx_scan_logs_timestamp_user ON scan_logs(scan_timestamp DESC, scanner_user_id);
CREATE INDEX IF NOT EXISTS idx_trip_logs_duration_metrics ON trip_logs(total_duration_minutes, loading_duration_minutes) WHERE total_duration_minutes IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_approvals_severity_created ON approvals(severity, created_at) WHERE status = 'pending';
CREATE INDEX IF NOT EXISTS idx_assignments_status_date ON assignments(status, assigned_date) WHERE status IN ('assigned', 'in_progress');

-- Add partial indexes for commonly filtered data
CREATE INDEX IF NOT EXISTS idx_locations_active_type ON locations(type, location_code) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_drivers_active_status ON drivers(status, employee_id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_trucks_active_status ON dump_trucks(status, truck_number) WHERE status = 'active';

-- Add composite indexes for complex join patterns
CREATE INDEX IF NOT EXISTS idx_trip_logs_assignment_status_exception ON trip_logs(assignment_id, status, is_exception, created_at);
CREATE INDEX IF NOT EXISTS idx_assignments_truck_locations ON assignments(truck_id, loading_location_id, unloading_location_id, assigned_date);

-- Add function-based indexes for JSON operations
-- First ensure pg_trgm extension is available for text search
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Option 1: Convert TEXT columns containing JSON to JSONB for better performance
-- This is safer and more efficient for JSON operations
DO $$
BEGIN
    -- Check if notes column exists and is TEXT
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'trip_logs' AND column_name = 'notes' AND data_type = 'text') THEN
        -- Convert notes to JSONB if it contains valid JSON, otherwise keep as TEXT
        ALTER TABLE trip_logs ALTER COLUMN notes TYPE JSONB USING 
            CASE 
                WHEN notes IS NULL OR notes = '' THEN NULL
                WHEN notes::text ~ '^[{[].*[}\]]$' THEN notes::jsonb
                ELSE ('{"original_text": "' || replace(notes, '"', '\"') || '"}')::jsonb
            END;
    END IF;
    
    -- Convert qr_code_data to JSONB for dump_trucks
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dump_trucks' AND column_name = 'qr_code_data' AND data_type = 'text') THEN
        ALTER TABLE dump_trucks ALTER COLUMN qr_code_data TYPE JSONB USING qr_code_data::jsonb;
    END IF;
    
    -- Convert qr_code_data to JSONB for locations
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'locations' AND column_name = 'qr_code_data' AND data_type = 'text') THEN
        ALTER TABLE locations ALTER COLUMN qr_code_data TYPE JSONB USING qr_code_data::jsonb;
    END IF;
END $$;

-- Now create proper GIN indexes on JSONB columns
CREATE INDEX IF NOT EXISTS idx_trip_logs_notes_gin ON trip_logs USING gin(notes) WHERE notes IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_trucks_qr_data_gin ON dump_trucks USING gin(qr_code_data) WHERE qr_code_data IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_locations_qr_data_gin ON locations USING gin(qr_code_data) WHERE qr_code_data IS NOT NULL;

-- Add constraint to ensure trip timing logical sequence
ALTER TABLE trip_logs DROP CONSTRAINT IF EXISTS chk_trip_timing_sequence;
ALTER TABLE trip_logs ADD CONSTRAINT chk_trip_timing_sequence 
CHECK (
  (loading_start_time IS NULL OR loading_end_time IS NULL OR loading_end_time >= loading_start_time) AND
  (loading_end_time IS NULL OR unloading_start_time IS NULL OR unloading_start_time >= loading_end_time) AND
  (unloading_start_time IS NULL OR unloading_end_time IS NULL OR unloading_end_time >= unloading_start_time) AND
  (unloading_end_time IS NULL OR trip_completed_time IS NULL OR trip_completed_time >= unloading_end_time)
);

-- Add constraint to ensure duration fields are non-negative
ALTER TABLE trip_logs DROP CONSTRAINT IF EXISTS chk_duration_non_negative;
ALTER TABLE trip_logs ADD CONSTRAINT chk_duration_non_negative 
CHECK (
  (total_duration_minutes IS NULL OR total_duration_minutes >= 0) AND
  (loading_duration_minutes IS NULL OR loading_duration_minutes >= 0) AND
  (travel_duration_minutes IS NULL OR travel_duration_minutes >= 0) AND
  (unloading_duration_minutes IS NULL OR unloading_duration_minutes >= 0)
);

-- Add enhanced function for trip duration calculation
CREATE OR REPLACE FUNCTION calculate_trip_durations()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate loading duration
  IF NEW.loading_start_time IS NOT NULL AND NEW.loading_end_time IS NOT NULL THEN
    NEW.loading_duration_minutes := EXTRACT(EPOCH FROM (NEW.loading_end_time - NEW.loading_start_time)) / 60;
  END IF;
  
  -- Calculate travel duration
  IF NEW.loading_end_time IS NOT NULL AND NEW.unloading_start_time IS NOT NULL THEN
    NEW.travel_duration_minutes := EXTRACT(EPOCH FROM (NEW.unloading_start_time - NEW.loading_end_time)) / 60;
  END IF;
  
  -- Calculate unloading duration
  IF NEW.unloading_start_time IS NOT NULL AND NEW.unloading_end_time IS NOT NULL THEN
    NEW.unloading_duration_minutes := EXTRACT(EPOCH FROM (NEW.unloading_end_time - NEW.unloading_start_time)) / 60;
  END IF;
  
  -- Calculate total duration
  IF NEW.loading_start_time IS NOT NULL AND NEW.trip_completed_time IS NOT NULL THEN
    NEW.total_duration_minutes := EXTRACT(EPOCH FROM (NEW.trip_completed_time - NEW.loading_start_time)) / 60;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic duration calculation
DROP TRIGGER IF EXISTS trigger_calculate_trip_durations ON trip_logs;
CREATE TRIGGER trigger_calculate_trip_durations
  BEFORE INSERT OR UPDATE ON trip_logs
  FOR EACH ROW
  EXECUTE FUNCTION calculate_trip_durations();

-- Add materialized view for performance analytics
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_trip_performance_summary AS
SELECT 
  DATE_TRUNC('day', tl.created_at) as trip_date,
  dt.truck_number,
  d.full_name as driver_name,
  ll.name as loading_location,
  ul.name as unloading_location,
  COUNT(*) as total_trips,
  COUNT(CASE WHEN tl.status = 'trip_completed' THEN 1 END) as completed_trips,
  COUNT(CASE WHEN tl.is_exception THEN 1 END) as exception_trips,
  AVG(tl.total_duration_minutes) as avg_duration,
  AVG(tl.loading_duration_minutes) as avg_loading_duration,
  AVG(tl.travel_duration_minutes) as avg_travel_duration,
  AVG(tl.unloading_duration_minutes) as avg_unloading_duration,
  ROUND(
    COUNT(CASE WHEN tl.is_exception THEN 1 END)::numeric / 
    NULLIF(COUNT(*), 0) * 100, 2
  ) as exception_rate_percent
FROM trip_logs tl
JOIN assignments a ON tl.assignment_id = a.id
JOIN dump_trucks dt ON a.truck_id = dt.id
LEFT JOIN drivers d ON a.driver_id = d.id
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
WHERE tl.created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY 
  DATE_TRUNC('day', tl.created_at),
  dt.truck_number,
  d.full_name,
  ll.name,
  ul.name;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_trip_performance_unique 
ON mv_trip_performance_summary(trip_date, truck_number, COALESCE(driver_name, 'Unknown'), loading_location, unloading_location);

-- Add function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_trip_performance_summary()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_trip_performance_summary;
END;
$$ LANGUAGE plpgsql;

-- Add function for advanced exception analytics
CREATE OR REPLACE FUNCTION get_advanced_exception_analytics(
  p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
  p_end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
  metric_category VARCHAR(50),
  metric_name VARCHAR(100),
  metric_value NUMERIC,
  metric_unit VARCHAR(20),
  metric_trend VARCHAR(20)
) AS $$
BEGIN
  RETURN QUERY
  WITH current_period AS (
    SELECT 
      COUNT(*) as total_exceptions,
      COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_exceptions,
      COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_exceptions,
      COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_exceptions,
      COUNT(CASE WHEN exception_type = 'route_deviation' THEN 1 END) as route_deviations,
      AVG(CASE 
        WHEN status != 'pending' AND reviewed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (reviewed_at - requested_at))/3600 
      END) as avg_resolution_hours
    FROM approvals
    WHERE created_at BETWEEN p_start_date AND p_end_date
  ),
  previous_period AS (
    SELECT 
      COUNT(*) as total_exceptions,
      COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_exceptions,
      AVG(CASE 
        WHEN status != 'pending' AND reviewed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (reviewed_at - requested_at))/3600 
      END) as avg_resolution_hours
    FROM approvals
    WHERE created_at BETWEEN (p_start_date - (p_end_date - p_start_date)) AND p_start_date
  )
  SELECT 'exceptions'::VARCHAR(50), 'total_count', cp.total_exceptions::NUMERIC, 'count'::VARCHAR(20),
    CASE 
      WHEN pp.total_exceptions = 0 THEN 'new'
      WHEN cp.total_exceptions > pp.total_exceptions THEN 'increasing'
      WHEN cp.total_exceptions < pp.total_exceptions THEN 'decreasing'
      ELSE 'stable'
    END::VARCHAR(20)
  FROM current_period cp, previous_period pp
  
  UNION ALL
  
  SELECT 'exceptions', 'pending_count', cp.pending_exceptions::NUMERIC, 'count',
    CASE WHEN cp.pending_exceptions > 5 THEN 'high' ELSE 'normal' END
  FROM current_period cp
  
  UNION ALL
  
  SELECT 'exceptions', 'approval_rate', 
    ROUND(cp.approved_exceptions::NUMERIC / NULLIF(cp.total_exceptions, 0) * 100, 2), 'percentage',
    CASE 
      WHEN cp.approved_exceptions::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.8 THEN 'good'
      WHEN cp.approved_exceptions::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.6 THEN 'moderate'
      ELSE 'low'
    END
  FROM current_period cp
  
  UNION ALL
  
  SELECT 'performance', 'avg_resolution_time', 
    ROUND(cp.avg_resolution_hours::NUMERIC, 2), 'hours',
    CASE 
      WHEN cp.avg_resolution_hours < 2 THEN 'excellent'
      WHEN cp.avg_resolution_hours < 8 THEN 'good' 
      WHEN cp.avg_resolution_hours < 24 THEN 'acceptable'
      ELSE 'slow'
    END
  FROM current_period cp
  
  UNION ALL
  
  SELECT 'patterns', 'route_deviation_rate',
    ROUND(cp.route_deviations::NUMERIC / NULLIF(cp.total_exceptions, 0) * 100, 2), 'percentage',
    CASE 
      WHEN cp.route_deviations::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.5 THEN 'high'
      WHEN cp.route_deviations::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.3 THEN 'moderate'
      ELSE 'low'
    END
  FROM current_period cp;
END;
$$ LANGUAGE plpgsql;

-- Add view for real-time dashboard metrics
CREATE OR REPLACE VIEW v_realtime_dashboard AS
SELECT 
  -- Active trips count
  (SELECT COUNT(*) FROM trip_logs WHERE status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end') 
   AND DATE(created_at) = CURRENT_DATE) as active_trips,
  
  -- Pending exceptions count
  (SELECT COUNT(*) FROM approvals WHERE status = 'pending') as pending_exceptions,
  
  -- Today's completed trips
  (SELECT COUNT(*) FROM trip_logs WHERE status = 'trip_completed' 
   AND DATE(created_at) = CURRENT_DATE) as completed_trips_today,
  
  -- Today's exception rate
  ROUND(
    (SELECT COUNT(*) FROM trip_logs WHERE is_exception = true AND DATE(created_at) = CURRENT_DATE)::numeric /
    NULLIF((SELECT COUNT(*) FROM trip_logs WHERE DATE(created_at) = CURRENT_DATE), 0) * 100, 2
  ) as exception_rate_today,
  
  -- Average trip duration today
  ROUND(
    (SELECT AVG(total_duration_minutes) FROM trip_logs 
     WHERE status = 'trip_completed' AND DATE(created_at) = CURRENT_DATE), 2
  ) as avg_trip_duration_today,
  
  -- Active trucks count
  (SELECT COUNT(DISTINCT a.truck_id) FROM assignments a
   WHERE a.status IN ('assigned', 'in_progress') AND a.assigned_date = CURRENT_DATE) as active_trucks,
  
  -- Current timestamp for real-time updates
  CURRENT_TIMESTAMP as last_updated;

-- Add database performance monitoring function
CREATE OR REPLACE FUNCTION get_database_performance_metrics()
RETURNS TABLE (
  metric_name VARCHAR(100),
  metric_value NUMERIC,
  metric_unit VARCHAR(20),
  recommendation TEXT
) AS $$
BEGIN
  RETURN QUERY
  WITH table_stats AS (
    SELECT 
      schemaname,
      tablename,
      n_tup_ins as inserts,
      n_tup_upd as updates,
      n_tup_del as deletes,
      n_live_tup as live_tuples,
      n_dead_tup as dead_tuples,
      last_vacuum,
      last_autovacuum,
      last_analyze,
      last_autoanalyze
    FROM pg_stat_user_tables
    WHERE schemaname = 'public'
  ),
  index_usage AS (
    SELECT 
      schemaname,
      tablename,
      indexname,
      idx_tup_read,
      idx_tup_fetch
    FROM pg_stat_user_indexes
    WHERE schemaname = 'public'
  )
  SELECT 'table_stats'::VARCHAR(100), 
         SUM(ts.live_tuples)::NUMERIC, 
         'rows'::VARCHAR(20),
         'Total live tuples across all tables'::TEXT
  FROM table_stats ts
  
  UNION ALL
  
  SELECT 'dead_tuples_ratio',
         ROUND(SUM(ts.dead_tuples)::NUMERIC / NULLIF(SUM(ts.live_tuples), 0) * 100, 2),
         'percentage',
         CASE 
           WHEN SUM(ts.dead_tuples)::NUMERIC / NULLIF(SUM(ts.live_tuples), 0) > 0.1 
           THEN 'Consider running VACUUM on heavily updated tables'
           ELSE 'Dead tuple ratio is healthy'
         END
  FROM table_stats ts
  
  UNION ALL
  
  SELECT 'index_efficiency',
         ROUND(SUM(iu.idx_tup_fetch)::NUMERIC / NULLIF(SUM(iu.idx_tup_read), 0) * 100, 2),
         'percentage',
         CASE 
           WHEN SUM(iu.idx_tup_fetch)::NUMERIC / NULLIF(SUM(iu.idx_tup_read), 0) < 0.9
           THEN 'Some indexes may be underutilized'
           ELSE 'Index usage is efficient'
         END
  FROM index_usage iu;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON INDEX idx_scan_logs_timestamp_user IS 'Optimized for scan log queries by timestamp and user';
COMMENT ON INDEX idx_trip_logs_duration_metrics IS 'Performance index for duration-based analytics';
COMMENT ON MATERIALIZED VIEW mv_trip_performance_summary IS 'Pre-aggregated trip performance data for fast dashboard queries';
COMMENT ON FUNCTION get_advanced_exception_analytics IS 'Comprehensive exception analytics with trend analysis';
COMMENT ON VIEW v_realtime_dashboard IS 'Real-time dashboard metrics for operational monitoring';

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '============================================================================';
    RAISE NOTICE 'Enhanced Performance Optimization Migration - COMPLETED';
    RAISE NOTICE '============================================================================';
    RAISE NOTICE 'Changes Applied:';
    RAISE NOTICE '  ✅ Added 10 new performance indexes including partial and composite indexes';
    RAISE NOTICE '  ✅ Added data integrity constraints for trip timing and duration validation';
    RAISE NOTICE '  ✅ Created automatic trip duration calculation trigger';
    RAISE NOTICE '  ✅ Added materialized view for performance analytics';
    RAISE NOTICE '  ✅ Created advanced exception analytics function';
    RAISE NOTICE '  ✅ Added real-time dashboard metrics view';
    RAISE NOTICE '  ✅ Implemented database performance monitoring';
    RAISE NOTICE '';
    RAISE NOTICE 'Performance Improvements:';
    RAISE NOTICE '  - Enhanced query performance with targeted indexes';
    RAISE NOTICE '  - Automated duration calculations with triggers';
    RAISE NOTICE '  - Pre-aggregated analytics with materialized views';
    RAISE NOTICE '  - Real-time monitoring capabilities';
    RAISE NOTICE '  - Advanced trend analysis and recommendations';
    RAISE NOTICE '============================================================================';
END $$;