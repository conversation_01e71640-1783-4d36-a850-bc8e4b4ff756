/**
 * Trip Flow and Assignment Management Logic Validation Tests
 * 
 * This test suite validates the complete implementation of the Trip Flow Logic:
 * 1. Assignment validation for multiple locations
 * 2. Standard flow (A→B→A = 1 trip)
 * 3. Exception flow for unassigned locations
 * 4. Trip completion status after exception approval
 * 5. Duplicate assignment prevention
 */

const { getClient } = require('../config/database');
const { assignmentValidator } = require('../utils/AssignmentValidator');
const { processApprovalAndUpdateTrip } = require('../utils/exception-flow-manager');

describe('Trip Flow and Assignment Management Logic', () => {
  let client;
  let testData = {};

  beforeAll(async () => {
    client = await getClient();
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    if (client) {
      client.release();
    }
  });

  /**
   * Test 1: Assignment Validation for Multiple Locations
   * Requirement: Trucks can return to any previously assigned location without triggering exceptions
   */
  describe('Assignment Validation for Multiple Locations', () => {
    test('should find valid assignment when truck has multiple location assignments', async () => {
      // Create multiple assignments for the same truck
      await createTestAssignment(testData.truck.id, testData.locationA.id, testData.locationB.id);
      await createTestAssignment(testData.truck.id, testData.locationC.id, testData.locationB.id);

      // Test validation at location A (first assignment)
      const resultA = await assignmentValidator.hasValidAssignmentForLocation({
        truckNumber: testData.truck.truck_number,
        locationId: testData.locationA.id,
        client
      });

      expect(resultA.hasValidAssignment).toBe(true);
      expect(resultA.assignments.length).toBeGreaterThan(0);

      // Test validation at location C (second assignment)
      const resultC = await assignmentValidator.hasValidAssignmentForLocation({
        truckNumber: testData.truck.truck_number,
        locationId: testData.locationC.id,
        client
      });

      expect(resultC.hasValidAssignment).toBe(true);
      expect(resultC.assignments.length).toBeGreaterThan(0);
    });

    test('should not trigger exception when truck returns to any assigned location', async () => {
      // This test verifies that trucks can operate at multiple valid locations
      const resultUnassigned = await assignmentValidator.hasValidAssignmentForLocation({
        truckNumber: testData.truck.truck_number,
        locationId: testData.locationD.id, // Unassigned location
        client
      });

      expect(resultUnassigned.hasValidAssignment).toBe(false);
    });
  });

  /**
   * Test 2: Standard Flow (A→B→A = 1 trip)
   * Requirement: Trip is complete only after return to loading location
   */
  describe('Standard Trip Flow', () => {
    test('should enforce A→B→A pattern for trip completion', async () => {
      // Create assignment A→B
      const assignment = await createTestAssignment(
        testData.truck.id, 
        testData.locationA.id, 
        testData.locationB.id
      );

      // Create trip in unloading_end state (ready for completion)
      const trip = await createTestTrip(assignment.id, 'unloading_end');

      // Validate trip progression at loading location (A)
      const progressionResult = await assignmentValidator.validateTripProgression({
        currentTrip: trip,
        location: testData.locationA,
        assignment
      });

      expect(progressionResult.canProgress).toBe(true);
      expect(progressionResult.nextStatus).toBe('trip_completed');
    });

    test('should reject trip completion at non-loading locations', async () => {
      const assignment = await createTestAssignment(
        testData.truck.id, 
        testData.locationA.id, 
        testData.locationB.id
      );

      const trip = await createTestTrip(assignment.id, 'unloading_end');

      // Try to complete at unloading location (should fail)
      const progressionResult = await assignmentValidator.validateTripProgression({
        currentTrip: trip,
        location: testData.locationB, // Wrong location type
        assignment
      });

      expect(progressionResult.canProgress).toBe(false);
    });
  });

  /**
   * Test 3: Exception Flow for Unassigned Locations
   * Requirement: Only trigger exceptions when location is NOT in assignment list
   */
  describe('Exception Flow for Unassigned Locations', () => {
    test('should trigger exception when truck scans at unassigned location', async () => {
      // Create assignment A→B only
      await createTestAssignment(testData.truck.id, testData.locationA.id, testData.locationB.id);

      // Check if truck has assignment for location C (should be false)
      const result = await assignmentValidator.hasValidAssignmentForLocation({
        truckNumber: testData.truck.truck_number,
        locationId: testData.locationC.id, // Unassigned location
        client
      });

      expect(result.hasValidAssignment).toBe(false);
      expect(result.message).toContain('no assignments for this location');
    });

    test('should create new assignment with copied values during exception', async () => {
      // This test verifies that exception approval creates proper assignments
      // with copied values from the original assignment
      const originalAssignment = await createTestAssignment(
        testData.truck.id, 
        testData.locationA.id, 
        testData.locationB.id,
        {
          priority: 'high',
          expected_loads_per_day: 5,
          start_time: '08:00:00',
          end_time: '17:00:00'
        }
      );

      // Simulate exception creation (this would normally be done by scanner)
      const exceptionAssignment = await createTestAssignment(
        testData.truck.id,
        testData.locationC.id, // New loading location
        testData.locationB.id, // Same unloading location
        {
          status: 'pending_approval',
          priority: originalAssignment.priority,
          expected_loads_per_day: originalAssignment.expected_loads_per_day,
          start_time: originalAssignment.start_time,
          end_time: originalAssignment.end_time
        }
      );

      expect(exceptionAssignment.status).toBe('pending_approval');
      expect(exceptionAssignment.priority).toBe(originalAssignment.priority);
      expect(exceptionAssignment.expected_loads_per_day).toBe(originalAssignment.expected_loads_per_day);
    });
  });

  /**
   * Test 4: Trip Status Logic for Exception Approval
   * Requirement: Mark trip as 'trip_completed' after exception approval
   */
  describe('Trip Status Logic for Exception Approval', () => {
    test('should mark trip as completed when exception is approved', async () => {
      // Create trip with exception_pending status
      const assignment = await createTestAssignment(testData.truck.id, testData.locationA.id, testData.locationB.id);
      const trip = await createTestTrip(assignment.id, 'exception_pending', { is_exception: true });

      // Create approval record
      const approval = await createTestApproval(trip.id, 'route_deviation');

      // Process approval
      const result = await processApprovalAndUpdateTrip(client, approval.id, 'approved', testData.user.id);

      expect(result.success).toBe(true);
      expect(result.trip_update.new_status).toBe('trip_completed');

      // Verify trip status in database
      const updatedTrip = await client.query('SELECT status FROM trip_logs WHERE id = $1', [trip.id]);
      expect(updatedTrip.rows[0].status).toBe('trip_completed');
    });
  });

  /**
   * Test 5: Duplicate Assignment Prevention
   * Requirement: Prevent duplicate assignments for same truck-location combinations
   */
  describe('Duplicate Assignment Prevention', () => {
    test('should prevent duplicate assignments', async () => {
      const assignmentData = {
        truckId: testData.truck.id,
        loadingLocationId: testData.locationA.id,
        unloadingLocationId: testData.locationB.id,
        assignedDate: new Date().toISOString().split('T')[0]
      };

      // Create first assignment
      await createTestAssignment(assignmentData.truckId, assignmentData.loadingLocationId, assignmentData.unloadingLocationId);

      // Check for duplicate (assignedDate no longer needed)
      const duplicateCheck = await assignmentValidator.checkDuplicateAssignment({
        truckId: assignmentData.truckId,
        loadingLocationId: assignmentData.loadingLocationId,
        unloadingLocationId: assignmentData.unloadingLocationId,
        client
      });

      expect(duplicateCheck.isDuplicate).toBe(true);
      expect(duplicateCheck.existingAssignment).toBeDefined();
    });

    test('should include pending_approval status in duplicate checking', async () => {
      // Create assignment with pending_approval status
      await createTestAssignment(
        testData.truck.id, 
        testData.locationC.id, 
        testData.locationD.id,
        { status: 'pending_approval' }
      );

      const duplicateCheck = await assignmentValidator.checkDuplicateAssignment({
        truckId: testData.truck.id,
        loadingLocationId: testData.locationC.id,
        unloadingLocationId: testData.locationD.id,
        client
      });

      expect(duplicateCheck.isDuplicate).toBe(true);
      expect(duplicateCheck.existingAssignment.status).toBe('pending_approval');
    });
  });

  // Helper functions for test setup
  async function setupTestData() {
    // Create test user
    const userResult = await client.query(`
      INSERT INTO users (username, email, password_hash, role, full_name)
      VALUES ('test_admin', '<EMAIL>', 'hash', 'admin', 'Test Admin')
      ON CONFLICT (username) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
      RETURNING id
    `);
    testData.user = userResult.rows[0];

    // Create test truck
    const truckResult = await client.query(`
      INSERT INTO dump_trucks (truck_number, license_plate, make, model, status)
      VALUES ('DT-TEST-100', 'TEST-001', 'Test Make', 'Test Model', 'active')
      ON CONFLICT (truck_number) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
      RETURNING *
    `);
    testData.truck = truckResult.rows[0];

    // Create test driver
    const driverResult = await client.query(`
      INSERT INTO drivers (employee_id, full_name, license_number, status)
      VALUES ('EMP-TEST-001', 'Test Driver', 'LIC-TEST-001', 'active')
      ON CONFLICT (employee_id) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
      RETURNING id
    `);
    testData.driver = driverResult.rows[0];

    // Create test locations
    const locations = ['A', 'B', 'C', 'D'];
    for (const loc of locations) {
      const result = await client.query(`
        INSERT INTO locations (location_code, name, type, status, latitude, longitude)
        VALUES ($1, $2, $3, 'active', 0.0, 0.0)
        ON CONFLICT (location_code) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
        RETURNING *
      `, [`TEST-${loc}`, `Test Location ${loc}`, loc === 'B' || loc === 'D' ? 'unloading' : 'loading']);
      testData[`location${loc}`] = result.rows[0];
    }
  }

  async function createTestAssignment(truckId, loadingLocationId, unloadingLocationId, options = {}) {
    const assignmentCode = `TEST-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
    const result = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
        assigned_date, status, priority, expected_loads_per_day, start_time, end_time
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
      assignmentCode,
      truckId,
      testData.driver.id,
      loadingLocationId,
      unloadingLocationId,
      options.assigned_date || new Date().toISOString().split('T')[0],
      options.status || 'assigned',
      options.priority || 'normal',
      options.expected_loads_per_day || 1,
      options.start_time || null,
      options.end_time || null
    ]);
    return result.rows[0];
  }

  async function createTestTrip(assignmentId, status, options = {}) {
    const result = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, loading_start_time, is_exception
      ) VALUES ($1, 1, $2, $3, $4)
      RETURNING *
    `, [
      assignmentId,
      status,
      options.loading_start_time || new Date(),
      options.is_exception || false
    ]);
    return result.rows[0];
  }

  async function createTestApproval(tripLogId, exceptionType) {
    const result = await client.query(`
      INSERT INTO approvals (trip_log_id, exception_type, exception_description, status)
      VALUES ($1, $2, $3, 'pending')
      RETURNING *
    `, [tripLogId, exceptionType, `Test ${exceptionType} exception`]);
    return result.rows[0];
  }

  async function cleanupTestData() {
    // Clean up test data in reverse order of dependencies
    await client.query("DELETE FROM approvals WHERE exception_description LIKE 'Test %'");
    await client.query("DELETE FROM trip_logs WHERE assignment_id IN (SELECT id FROM assignments WHERE assignment_code LIKE 'TEST-%')");
    await client.query("DELETE FROM assignments WHERE assignment_code LIKE 'TEST-%'");
    await client.query("DELETE FROM locations WHERE location_code LIKE 'TEST-%'");
    await client.query("DELETE FROM drivers WHERE employee_id LIKE 'EMP-TEST-%'");
    await client.query("DELETE FROM dump_trucks WHERE truck_number LIKE 'DT-TEST-%'");
    await client.query("DELETE FROM users WHERE username = 'test_admin'");
  }
});
