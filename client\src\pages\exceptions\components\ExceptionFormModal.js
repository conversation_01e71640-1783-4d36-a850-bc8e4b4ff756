import React, { useState, useEffect } from 'react';
import { tripsAPI } from '../../../services/api';
import toast from 'react-hot-toast';

const ExceptionFormModal = ({ isOpen, onClose, onSubmit, exception }) => {
  const [formData, setFormData] = useState({
    trip_log_id: '',
    exception_type: '',
    exception_description: '',
    severity: 'medium'
  });
  const [loading, setLoading] = useState(false);
  const [trips, setTrips] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Load available trips
  useEffect(() => {
    const loadTrips = async () => {
      try {
        const response = await tripsAPI.getAll({ 
          params: { 
            page: 1, 
            limit: 50, 
            sortBy: 'created_at', 
            sortOrder: 'desc' 
          } 
        });
        setTrips(response.data.data || []);
      } catch (error) {
        console.error('Error loading trips:', error);
        toast.error('Failed to load trips');
      }
    };

    if (isOpen) {
      loadTrips();
    }
  }, [isOpen]);

  // Filter trips based on search
  const filteredTrips = trips.filter(trip => {
    const searchLower = searchTerm.toLowerCase();
    return (
      trip.trip_number?.toString().includes(searchLower) ||
      trip.truck_number?.toLowerCase().includes(searchLower) ||
      trip.driver_name?.toLowerCase().includes(searchLower)
    );
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (exception) {
        setFormData({
          trip_log_id: exception.trip_log_id || '',
          exception_type: exception.exception_type || '',
          exception_description: exception.exception_description || '',
          severity: exception.severity || 'medium'
        });
      } else {
        setFormData({
          trip_log_id: '',
          exception_type: '',
          exception_description: '',
          severity: 'medium'
        });
      }
    }
  }, [isOpen, exception]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.trip_log_id) {
      toast.error('Please select a trip');
      return;
    }

    if (!formData.exception_type) {
      toast.error('Please select an exception type');
      return;
    }

    if (!formData.exception_description.trim()) {
      toast.error('Please provide an exception description');
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting exception:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && !loading) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      {/* Backdrop */}
      <div 
        className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
        onClick={handleBackdropClick}
      >
        <div className="fixed inset-0 bg-secondary-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        {/* Modal */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-secondary-900" id="modal-title">
              {exception ? 'Edit Exception' : 'Report Exception'}
            </h3>
            <button
              onClick={onClose}
              disabled={loading}
              className="text-secondary-400 hover:text-secondary-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            
            {/* Trip Selection */}
            <div>
              <label htmlFor="trip_log_id" className="block text-sm font-medium text-secondary-700 mb-1">
                Select Trip <span className="text-red-500">*</span>
              </label>
              
              {/* Trip Search */}
              <div className="mb-2">
                <input
                  type="text"
                  placeholder="Search by trip number, truck, or driver..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input"
                />
              </div>
              
              <select
                id="trip_log_id"
                name="trip_log_id"
                value={formData.trip_log_id}
                onChange={handleInputChange}
                required
                disabled={loading}
                className="input"
              >
                <option value="">Select a trip...</option>
                {filteredTrips.map((trip) => (
                  <option key={trip.id} value={trip.id}>
                    Trip #{trip.trip_number} - {trip.truck_number} ({trip.driver_name || 'No Driver'}) - {trip.status}
                  </option>
                ))}
              </select>
            </div>

            {/* Exception Type */}
            <div>
              <label htmlFor="exception_type" className="block text-sm font-medium text-secondary-700 mb-1">
                Exception Type <span className="text-red-500">*</span>
              </label>
              <select
                id="exception_type"
                name="exception_type"
                value={formData.exception_type}
                onChange={handleInputChange}
                required
                disabled={loading}
                className="input"
              >
                <option value="">Select exception type...</option>
                <option value="route_deviation">Route Deviation</option>
                <option value="time_violation">Time Violation</option>
                <option value="equipment_issue">Equipment Issue</option>
                <option value="weather_delay">Weather Delay</option>
                <option value="manual_override">Manual Override</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Severity */}
            <div>
              <label htmlFor="severity" className="block text-sm font-medium text-secondary-700 mb-1">
                Severity
              </label>
              <select
                id="severity"
                name="severity"
                value={formData.severity}
                onChange={handleInputChange}
                disabled={loading}
                className="input"
              >
                <option value="low">Low - Minor issue, minimal impact</option>
                <option value="medium">Medium - Moderate issue, some impact</option>
                <option value="high">High - Significant issue, major impact</option>
                <option value="critical">Critical - Severe issue, immediate attention required</option>
              </select>
            </div>

            {/* Exception Description */}
            <div>
              <label htmlFor="exception_description" className="block text-sm font-medium text-secondary-700 mb-1">
                Description <span className="text-red-500">*</span>
              </label>
              <textarea
                id="exception_description"
                name="exception_description"
                rows={4}
                value={formData.exception_description}
                onChange={handleInputChange}
                placeholder="Provide detailed description of the exception..."
                required
                disabled={loading}
                className="input"
              />
              <p className="mt-1 text-sm text-secondary-500">
                Describe what happened, why it occurred, and any relevant details for the review process.
              </p>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-secondary-200">
              <button
                type="button"
                onClick={onClose}
                disabled={loading}
                className="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="btn btn-primary"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    {exception ? 'Updating...' : 'Reporting...'}
                  </div>
                ) : (
                  exception ? 'Update Exception' : 'Report Exception'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ExceptionFormModal;
