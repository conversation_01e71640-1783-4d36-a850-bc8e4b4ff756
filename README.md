# 🚛 Hauling QR Trip System

**Version 2.0 - Production Ready**  
**Status: ✅ COMPLETE & DEPLOYED**

A comprehensive QR code-based trip management system for dump truck operations with intelligent route management, real-time exception handling, and automated approval workflows.

## 🎯 Key Features

### ✅ Core Operations
- **Smart QR Code Scanning**: Two-step validation workflow (Location → Truck)
- **Intelligent Assignment Management**: Prevents exact duplicates while allowing flexible routing
- **Real-time Trip Tracking**: Complete state machine with validation and timing
- **Exception Management**: Automatic route deviation detection with approval workflows
- **Distance-based Rate Calculation**: Automatic rate computation using Haversine formula
- **Flexible Flow Patterns**: Supports standard (A→B→A), exception (A→B→C), and adaptive (C→B→C) flows

### ✅ Advanced Capabilities  
- **Admin-Controlled Assignment Creation**: Assignments must be created by administrators, scanner only uses existing assignments
- **Exception Handling with Approval**: Route deviations and unassigned trucks require explicit admin approval
- **WebSocket Notifications**: Real-time admin alerts for exceptions and approvals
- **Comprehensive Audit Trail**: Complete tracking of all operations and exceptions
- **Production-Grade Security**: ACID transactions, proper constraints, and data integrity

## � Documentation

- **[Flow Logic Complete](FLOW_LOGIC_COMPLETE.md)** - Comprehensive documentation of system flow logic
- **[Production Deployment Guide](PRODUCTION_DEPLOYMENT_GUIDE.md)** - Guide for deploying to production
- **[Troubleshooting](TROUBLESHOOTING.md)** - Solutions for common issues
- **[Installation](INSTALLATION.md)** - Installation instructions

## �🛠️ Tech Stack

### Frontend
- **React.js 18+** - Modern UI framework
- **Tailwind CSS** - Utility-first styling
- **React Router DOM** - Client-side routing
- **React QR Reader** - QR code scanning
- **React Hook Form** - Form handling
- **Chart.js** - Data visualization

### Backend
- **Node.js** - Server runtime
- **Express.js** - Web framework
- **PostgreSQL** - Database
- **JWT** - Authentication
- **QRCode** - QR code generation
- **Bcrypt** - Password hashing

## 🚀 Quick Start

### Prerequisites

- **Node.js** (v16 or higher)
- **PostgreSQL** (v12 or higher)
- **npm** or **yarn**

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd hauling-qr-trip-system
```

2. **Install all dependencies**
```bash
npm run install-deps
```

3. **Database Setup**

   a. **Install PostgreSQL** (if not already installed)
   - Download from https://www.postgresql.org/download/
   - Install with default settings
   - Remember your postgres user password

   b. **Create Database**
   ```bash
   # Connect to PostgreSQL
   psql -U postgres
   
   # Create database
   CREATE DATABASE hauling_qr_system;
   
   # Exit psql
   \q
   ```

   c. **Run Database Migration**
   ```bash
   psql -U postgres -d hauling_qr_system -f database/init.sql
   ```

4. **Environment Configuration**

   a. **Backend Environment**
   ```bash
   cd server
   cp .env.example .env
   ```
   
   Edit `server/.env` with your settings:
   ```env
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=hauling_qr_system
   DB_USER=postgres
   DB_PASSWORD=your_postgres_password
   JWT_SECRET=your_super_secure_jwt_secret_key_here
   PORT=5000
   NODE_ENV=development
   FRONTEND_URL=http://localhost:3000
   ```

5. **Start Development Servers**
```bash
# From root directory - starts both frontend and backend
npm run dev
```

Or start them separately:
```bash
# Backend (Terminal 1)
npm run server

# Frontend (Terminal 2)
npm run client
```

## 📱 Application Access

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Health Check**: http://localhost:5000/health

### Default Admin Login
```
Username: admin
Password: admin123
```

## 🗂️ Project Structure

```
hauling-qr-trip-system/
├── client/                     # React Frontend
│   ├── public/                # Static files
│   ├── src/
│   │   ├── components/        # Reusable components
│   │   │   ├── layout/        # Layout components
│   │   │   ├── forms/         # Form components
│   │   │   └── scanner/       # QR scanner components
│   │   ├── pages/             # Page components
│   │   │   ├── auth/          # Authentication pages
│   │   │   ├── dashboard/     # Dashboard pages
│   │   │   └── scanner/       # Scanner pages
│   │   ├── services/          # API services
│   │   ├── utils/             # Utilities
│   │   └── context/           # React contexts
│   ├── package.json
│   └── tailwind.config.js
├── server/                     # Node.js Backend
│   ├── controllers/           # Route controllers
│   ├── middleware/            # Custom middleware
│   ├── routes/                # API routes
│   ├── utils/                 # Utilities
│   ├── config/                # Configuration
│   ├── package.json
│   └── server.js
├── database/
│   └── init.sql              # Database schema
├── package.json              # Root package.json
└── README.md
```

## 🔧 Available Scripts

### Root Level
- `npm run dev` - Start both frontend and backend
- `npm run install-deps` - Install all dependencies
- `npm run setup` - Complete setup including database

### Backend (`server/`)
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm test` - Run tests

### Frontend (`client/`)
- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests

## 📊 Database Schema

### Core Tables
- **users** - Admin authentication
- **dump_trucks** - Truck information with QR codes
- **drivers** - Driver details and assignments
- **locations** - Loading/unloading points with QR codes
- **assignments** - Flexible route assignments
- **trip_logs** - Complete trip tracking with timestamps
- **approvals** - Exception handling for route deviations
- **scan_logs** - Audit trail for all QR scans

## 🎯 Trip Flow Logic

### Normal Trip (A→B→A)
1. **ASSIGNED** - Truck and driver assigned to route
2. **LOADING_START** - Start loading at Point A
3. **LOADING_END** - Complete loading, begin travel
4. **UNLOADING_START** - Arrive at Point B, start unloading
5. **UNLOADING_END** - Complete unloading, begin return
6. **TRIP_COMPLETED** - Return to Point A

### Exception Trip (A→B→C)
- Requires admin approval when truck deviates to Point C
- System detects deviation and prompts for approval
- Admin can approve/reject with reason codes
- Trip continues or returns based on approval

## 🔒 Security Features

- **JWT Authentication** with secure token expiration
- **Password Hashing** using bcrypt
- **Input Validation** on all endpoints
- **Rate Limiting** to prevent abuse
- **CORS Configuration** for secure cross-origin requests
- **Helmet.js** for security headers

## 📱 QR Code Format

### Location QR Code
```json
{
  "type": "location",
  "id": "LOC-001",
  "name": "Point A",
  "coordinates": "lat,lng",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

### Truck QR Code
```json
{
  "type": "truck",
  "id": "DT-100",
  "assigned_route": "A-B",
  "driver_id": "DR-001",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

## 🚨 Troubleshooting

### Database Connection Issues
```bash
# Check PostgreSQL service
pg_ctl status

# Restart PostgreSQL service
sudo service postgresql restart

# Check database exists
psql -U postgres -l
```

### Port Already in Use
```bash
# Kill process on port 3000
npx kill-port 3000

# Kill process on port 5000
npx kill-port 5000
```

### Clear Cache
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

## 📖 API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - Admin login
- `POST /api/auth/logout` - Admin logout
- `GET /api/auth/profile` - Get current user profile

### Truck Management
- `GET /api/trucks` - List all trucks
- `POST /api/trucks` - Create new truck
- `PUT /api/trucks/:id` - Update truck
- `DELETE /api/trucks/:id` - Delete truck
- `GET /api/trucks/:id/qr` - Generate truck QR code

### Trip Management
- `GET /api/trips` - List all trips
- `POST /api/trips` - Create new trip
- `PUT /api/trips/:id` - Update trip status
- `GET /api/trips/:id` - Get trip details

### Scanner Endpoints
- `POST /api/scanner/scan` - Process QR scan
- `GET /api/scanner/status/:tripId` - Get current trip status

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the troubleshooting section above

---

**Built with ❤️ for efficient hauling operations**