/**
 * Check Assignment Update Issue
 * 
 * Why is assignment ASG-1751024590244-RD-PENDING still in pending_approval status?
 */

const { getClient } = require('../config/database');

async function checkAssignmentUpdateIssue() {
  console.log('🔍 Checking Assignment Update Issue...\n');
  
  const client = await getClient();
  
  try {
    // Check the pending assignment
    console.log('1️⃣ CHECKING PENDING ASSIGNMENT STATUS');
    
    const pendingAssignmentResult = await client.query(`
      SELECT * FROM assignments 
      WHERE assignment_code = 'ASG-1751024590244-RD-PENDING'
    `);
    
    if (pendingAssignmentResult.rows.length === 0) {
      console.log('❌ Assignment ASG-1751024590244-RD-PENDING not found');
      return;
    }
    
    const pendingAssignment = pendingAssignmentResult.rows[0];
    console.log('📋 Pending Assignment Details:');
    console.log(`   ID: ${pendingAssignment.id}`);
    console.log(`   Code: ${pendingAssignment.assignment_code}`);
    console.log(`   Status: ${pendingAssignment.status} ${pendingAssignment.status === 'pending_approval' ? '⚠️ STILL PENDING' : '✅'}`);
    console.log(`   Created: ${pendingAssignment.created_at}`);
    console.log(`   Updated: ${pendingAssignment.updated_at}`);
    
    // Check if there are any trip logs referencing this assignment
    console.log('\n2️⃣ CHECKING TRIP LOGS REFERENCING THIS ASSIGNMENT');
    
    const tripLogsResult = await client.query(`
      SELECT 
          tl.id,
          tl.assignment_id,
          tl.notes,
          tl.created_at,
          ap.id as approval_id,
          ap.status as approval_status,
          ap.reviewed_at
      FROM trip_logs tl
      LEFT JOIN approvals ap ON tl.id = ap.trip_log_id
      WHERE tl.notes IS NOT NULL
        AND (tl.notes::text LIKE '%${pendingAssignment.id}%' 
             OR tl.assignment_id = ${pendingAssignment.id})
      ORDER BY tl.created_at DESC
    `);
    
    console.log(`📊 Found ${tripLogsResult.rows.length} trip log(s) referencing this assignment:`);
    
    for (const tripLog of tripLogsResult.rows) {
      console.log(`\n   🔍 Trip Log ID: ${tripLog.id}`);
      console.log(`      Assignment ID: ${tripLog.assignment_id}`);
      console.log(`      Created: ${tripLog.created_at}`);
      console.log(`      Approval ID: ${tripLog.approval_id}`);
      console.log(`      Approval Status: ${tripLog.approval_status}`);
      console.log(`      Reviewed At: ${tripLog.reviewed_at}`);
      
      if (tripLog.notes) {
        try {
          const notes = JSON.parse(tripLog.notes);
          console.log(`      Trip Notes:`, JSON.stringify(notes, null, 8));
          
          // Check if this trip references the pending assignment
          if (notes.pending_assignment_id && notes.pending_assignment_id == pendingAssignment.id) {
            console.log(`      🎯 This trip references the pending assignment as pending_assignment_id`);
            
            // Check if the approval was processed
            if (tripLog.approval_status === 'approved') {
              console.log(`      ✅ Approval is approved`);
              console.log(`      🚨 BUT assignment status was NOT updated - this is the issue!`);
              
              // Test if the update query would work now
              console.log(`\n      🧪 Testing assignment update query:`);
              
              const testUpdateResult = await client.query(`
                UPDATE assignments
                SET status = 'assigned',
                    assigned_date = CURRENT_DATE,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = $1
                AND status = 'pending_approval'
                RETURNING id, status
              `, [pendingAssignment.id]);
              
              console.log(`         Rows updated: ${testUpdateResult.rowCount}`);
              if (testUpdateResult.rowCount > 0) {
                console.log(`         New status: ${testUpdateResult.rows[0].status}`);
                console.log(`         ✅ Update query works - assignment status fixed!`);
              } else {
                console.log(`         ❌ Update query failed - assignment may not be in pending_approval status`);
              }
              
            } else {
              console.log(`      ⚠️ Approval is not approved yet: ${tripLog.approval_status}`);
            }
          } else if (notes.new_assignment_id && notes.new_assignment_id == pendingAssignment.id) {
            console.log(`      🎯 This trip references the pending assignment as new_assignment_id`);
          } else {
            console.log(`      ℹ️ This trip does not reference the pending assignment`);
          }
          
        } catch (e) {
          console.log(`      Trip Notes (raw): ${tripLog.notes}`);
        }
      }
    }
    
    // Check if the assignment update logic was executed
    console.log('\n3️⃣ CHECKING IF ASSIGNMENT UPDATE LOGIC WAS EXECUTED');
    
    // Look for any logs related to this assignment
    const fs = require('fs');
    const logFiles = ['logs/exception-flow.log', 'logs/app.log', 'logs/error.log'];
    
    for (const logFile of logFiles) {
      try {
        if (fs.existsSync(logFile)) {
          const logContent = fs.readFileSync(logFile, 'utf8');
          const assignmentLogs = logContent.split('\n').filter(line => 
            line.includes(pendingAssignment.id.toString()) || 
            line.includes(pendingAssignment.assignment_code)
          );
          
          if (assignmentLogs.length > 0) {
            console.log(`\n   📋 Found ${assignmentLogs.length} log entries in ${logFile}:`);
            assignmentLogs.slice(-5).forEach((log, i) => {
              console.log(`      ${i+1}. ${log}`);
            });
          }
        }
      } catch (e) {
        // Ignore file read errors
      }
    }
    
    // Final analysis
    console.log('\n🎯 ANALYSIS SUMMARY:');
    
    if (pendingAssignment.status === 'pending_approval') {
      console.log('🚨 CONFIRMED: Assignment is still in pending_approval status');
      console.log('💡 Possible causes:');
      console.log('   1. Assignment update logic was not executed');
      console.log('   2. Trip notes do not contain correct pending_assignment_id');
      console.log('   3. Database transaction was rolled back');
      console.log('   4. Exception flow manager was not called');
      console.log('   5. Approval was processed through different workflow');
    } else {
      console.log('✅ Assignment status has been updated correctly');
    }
    
  } catch (error) {
    console.error('Check failed:', error.message);
  } finally {
    client.release();
  }
}

checkAssignmentUpdateIssue();
